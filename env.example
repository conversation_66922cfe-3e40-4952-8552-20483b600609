# 环境配置示例文件
# 复制此文件为 .env 并根据需要修改配置

# 环境设置
# 可选值: dev, prod
APP_ENV=dev

# 生产环境数据库配置 (当APP_ENV=prod时使用)
DB_HOST=your-production-db-host
DB_USER=your-db-user
DB_PASSWORD=your-db-password
DB_NAME=hkchat

# 生产环境Redis配置
REDIS_HOST=your-redis-host
REDIS_PASSWORD=your-redis-password

# JWT密钥 (生产环境必须设置)
JWT_SECRET_KEY=your-super-secret-jwt-key-for-production

# CORS允许的源 (生产环境)
CORS_ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# ==========================================
# 使用方法：
# ==========================================

# 1. 开发环境 (使用 config/dev.yaml)
# export APP_ENV=dev
# 然后启动应用即可，会自动使用dev.yaml中的配置

# 2. 生产环境 (使用 config/prod.yaml + 环境变量)
# export APP_ENV=prod
# export DB_HOST=gugu.helwd.com
# export DB_USER=root
# export DB_PASSWORD=!Qq868285
# export DB_NAME=hkchat
# export JWT_SECRET_KEY=your-production-jwt-secret-key
# export CORS_ALLOWED_ORIGINS=https://yourdomain.com

# ==========================================
# Windows 设置环境变量方法：
# ==========================================
# set APP_ENV=dev
# set DB_HOST=gugu.helwd.com
# set DB_USER=root
# set DB_PASSWORD=!Qq868285
# set DB_NAME=hkchat

# ==========================================
# Linux/Mac 设置环境变量方法：
# ==========================================
# export APP_ENV=dev
# export DB_HOST=gugu.helwd.com
# export DB_USER=root
# export DB_PASSWORD=!Qq868285
# export DB_NAME=hkchat 