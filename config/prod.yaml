# 生产环境配置
app:
  name: "HKChat API"
  version: "1.0.0"
  env: "production"
  port: 8080
  debug: false

database:
  host: "${DB_HOST}"
  port: 5432
  user: "${DB_USER}"
  password: "${DB_PASSWORD}"
  dbname: "${DB_NAME}"
  sslmode: "require"
  timezone: "Asia/Shanghai"
  max_open_conns: 100
  max_idle_conns: 10
  # log_level: # 不单独设置，跟随应用日志级别

redis:
  host: "${REDIS_HOST}"
  port: 6379
  password: "${REDIS_PASSWORD}"
  db: 0

jwt:
  secret_key: "${JWT_SECRET_KEY}"
  expire_hours: 24

log:
  level: "info"
  file_path: ""  # 不输出到文件，只输出到控制台
  max_size: 500  # MB
  max_backups: 10
  max_age: 30    # days

cors:
  allowed_origins:
    - "${CORS_ALLOWED_ORIGINS}"
  allowed_methods:
    - "GET"
    - "POST"
    - "PUT"
    - "DELETE"
    - "OPTIONS"
  allowed_headers:
    - "Content-Type"
    - "Authorization" 