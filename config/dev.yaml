# 开发环境配置
app:
  name: "HKChat API"
  version: "1.0.0"
  env: "development"
  port: 8081
  debug: true

database:
  host: "*************"
  port: 5432
  user: "root"
  password: "!Qq868285"
  dbname: "hkchat"
  sslmode: "disable"
  timezone: "Asia/Shanghai"
  max_open_conns: 25
  max_idle_conns: 5
  # log_level: # 不单独设置，跟随应用日志级别

redis:
  host: "localhost"
  port: 6379
  password: ""
  db: 0

jwt:
  secret_key: "l6tT6w0vJrRf8pSh"
  expire_hours: 24

log:
  level: "info"  # 设置为info级别，日志更简洁
  file_path: ""  # 不输出到文件，只输出到控制台
  max_size: 100  # MB
  max_backups: 3
  max_age: 28    # days

cors:
  allowed_origins:
    - "http://localhost:3000"
    - "http://localhost:3001" 
  allowed_methods:
    - "GET"
    - "POST"
    - "PUT"
    - "DELETE"
    - "OPTIONS"
  allowed_headers:
    - "Content-Type"
    - "Authorization"

# OpenAI 配置
openai:
  api_key: ""
  base_url: "https://api.openai.com/v1"
  model: "gpt-3.5-turbo"
  max_tokens: 4000
  temperature: 0.7
  timeout: 30

# Tavily 搜索引擎配置
tavily:
  api_key: "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"
  base_url: "https://api.tavily.com"
  max_results: 10
  timeout: 30 