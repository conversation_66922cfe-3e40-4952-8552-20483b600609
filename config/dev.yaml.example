# 应用配置
app:
  name: "HKChat API"
  version: "1.0.0"
  env: "dev"
  port: 8080
  debug: true

# 数据库配置
database:
  host: "localhost"
  port: 5432
  user: "postgres"
  password: "password"
  dbname: "hkchat"
  sslmode: "disable"
  timezone: "UTC"
  max_open_conns: 100
  max_idle_conns: 10
  log_level: "info"

# Redis配置
redis:
  host: "localhost"
  port: 6379
  password: ""
  db: 0

# JWT配置
jwt:
  secret_key: "your-secret-key"
  expire_hours: 24

# 日志配置
log:
  level: "info"
  file_path: "logs/app.log"
  max_size: 100
  max_backups: 5
  max_age: 30

# CORS配置
cors:
  allowed_origins: ["*"]
  allowed_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
  allowed_headers: ["*"]

# OpenAI配置
openai:
  api_key: "your-openai-api-key"
  base_url: "https://api.openai.com/v1"
  model: "gpt-3.5-turbo"
  max_tokens: 2000
  temperature: 0.7
  timeout: 30

# Tavily搜索配置
tavily:
  api_key: "your-tavily-api-key"
  base_url: "https://api.tavily.com"
  max_results: 5
  timeout: 10 