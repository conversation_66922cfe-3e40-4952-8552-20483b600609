package config

// Config 应用配置结构体
type Config struct {
	App      AppConfig    `yaml:"app"`
	Database DatabaseConf `yaml:"database"`
	Redis    RedisConfig  `yaml:"redis"`
	JWT      JWTConfig    `yaml:"jwt"`
	Log      LogConfig    `yaml:"log"`
	CORS     CORSConfig   `yaml:"cors"`
	OpenAI   OpenAIConfig `yaml:"openai"`
	Tavily   <PERSON>onfig `yaml:"tavily"`
}

// AppConfig 应用配置
type AppConfig struct {
	Name    string `yaml:"name"`
	Version string `yaml:"version"`
	Env     string `yaml:"env"`
	Port    int    `yaml:"port"`
	Debug   bool   `yaml:"debug"`
}

// DatabaseConf 数据库配置
type DatabaseConf struct {
	Host        string `yaml:"host"`
	Port        int    `yaml:"port"`
	User        string `yaml:"user"`
	Password    string `yaml:"password"`
	DBName      string `yaml:"dbname"`
	SSLMode     string `yaml:"sslmode"`
	Timezone    string `yaml:"timezone"`
	MaxOpenConn int    `yaml:"max_open_conns"`
	MaxIdleConn int    `yaml:"max_idle_conns"`
	LogLevel    string `yaml:"log_level"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host     string `yaml:"host"`
	Port     int    `yaml:"port"`
	Password string `yaml:"password"`
	DB       int    `yaml:"db"`
}

// JWTConfig JWT配置
type JWTConfig struct {
	SecretKey   string `yaml:"secret_key"`
	ExpireHours int    `yaml:"expire_hours"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level      string `yaml:"level"`
	FilePath   string `yaml:"file_path"`
	MaxSize    int    `yaml:"max_size"`
	MaxBackups int    `yaml:"max_backups"`
	MaxAge     int    `yaml:"max_age"`
}

// CORSConfig CORS配置
type CORSConfig struct {
	AllowedOrigins []string `yaml:"allowed_origins"`
	AllowedMethods []string `yaml:"allowed_methods"`
	AllowedHeaders []string `yaml:"allowed_headers"`
}

// OpenAIConfig OpenAI配置
type OpenAIConfig struct {
	APIKey      string  `yaml:"api_key"`
	BaseURL     string  `yaml:"base_url"`
	Model       string  `yaml:"model"`
	MaxTokens   int     `yaml:"max_tokens"`
	Temperature float32 `yaml:"temperature"`
	Timeout     int     `yaml:"timeout"`
}

// TavilyConfig Tavily搜索配置
type TavilyConfig struct {
	APIKey     string `yaml:"api_key"`
	BaseURL    string `yaml:"base_url"`
	MaxResults int    `yaml:"max_results"`
	Timeout    int    `yaml:"timeout"`
}

// Environment 环境枚举
type Environment string

const (
	EnvDevelopment Environment = "dev"
	EnvProduction  Environment = "prod"
)
