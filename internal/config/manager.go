package config

import (
	"fmt"
	"sync"

	configModels "hkchat_api/internal/models/config"
	"hkchat_api/pkg/utils"
)

// Manager 配置管理器 - 负责配置缓存和线程安全访问
type Manager struct {
	configService *Service
	appConfig     *configModels.AppConfiguration
	mu            sync.RWMutex
}

// NewManager 创建配置管理器实例
func NewManager(service *Service) *Manager {
	return &Manager{
		configService: service,
	}
}

// LoadAppConfig 加载应用配置
func (m *Manager) LoadAppConfig() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	utils.Info("🔧 开始加载应用配置...")

	// 委托给 ConfigService 处理业务逻辑
	appConfig, err := m.configService.LoadAppConfigFromDB()
	if err != nil {
		utils.Errorf("从数据库加载应用配置失败: %v", err)
		return err
	}

	// 缓存配置
	m.appConfig = appConfig

	utils.Info("✅ 应用配置加载完成")
	utils.Debugf("📊 应用配置版本: %d", appConfig.Version)
	return nil
}

// GetAppConfig 获取应用配置（线程安全）
func (m *Manager) GetAppConfig() *configModels.AppConfiguration {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.appConfig
}

// ReloadAppConfig 重新加载应用配置
func (m *Manager) ReloadAppConfig() error {
	utils.Info("🔄 重新加载应用配置...")

	if err := m.LoadAppConfig(); err != nil {
		utils.Errorf("重新加载应用配置失败: %v", err)
		return err
	}

	utils.Info("✅ 应用配置重新加载完成")
	return nil
}

// SaveAppConfig 保存应用配置
func (m *Manager) SaveAppConfig(appConfig *configModels.AppConfiguration, version int64) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	utils.Debugf("保存应用配置，版本: %d", version)

	// 委托给 ConfigService 处理业务逻辑
	if err := m.configService.SaveAppConfig(appConfig, version); err != nil {
		utils.Errorf("保存应用配置失败: %v", err)
		return err
	}

	// 更新缓存
	m.appConfig = appConfig

	utils.Debugf("✅ 应用配置保存成功，版本: %d", version)
	return nil
}

// LoadAppConfigByVersion 根据版本加载应用配置
func (m *Manager) LoadAppConfigByVersion(version int64) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	utils.Debugf("🔧 加载版本 %d 的应用配置...", version)

	// 委托给 ConfigService 处理业务逻辑
	appConfig, err := m.configService.GetConfigByVersion(version)
	if err != nil {
		utils.Errorf("从数据库加载版本 %d 的配置失败: %v", version, err)
		return err
	}

	// 缓存配置
	m.appConfig = appConfig

	utils.Debugf("✅ 版本 %d 的应用配置加载完成", version)
	return nil
}

// ValidateCurrentConfig 验证当前配置
func (m *Manager) ValidateCurrentConfig() error {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if m.appConfig == nil {
		return fmt.Errorf("当前没有加载任何配置")
	}

	// 委托给 ConfigService 处理验证逻辑
	return m.configService.ValidateAppConfig(m.appConfig)
}

// GetCurrentVersion 获取当前配置版本
func (m *Manager) GetCurrentVersion() int {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if m.appConfig == nil {
		return 0
	}
	return m.appConfig.Version
}

// IsConfigLoaded 检查配置是否已加载
func (m *Manager) IsConfigLoaded() bool {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.appConfig != nil
}

// GetOpenAIConfig 获取OpenAI配置
func (m *Manager) GetOpenAIConfig() *configModels.AppOpenAIConfig {
	appConfig := m.GetAppConfig()
	if appConfig == nil {
		return nil
	}
	return &appConfig.OpenAI
}

// GetAuthConfig 获取认证配置
func (m *Manager) GetAuthConfig() *configModels.AppAuthConfig {
	appConfig := m.GetAppConfig()
	if appConfig == nil {
		return nil
	}
	return &appConfig.Auth
}

// GetRAGConfig 获取RAG配置
func (m *Manager) GetRAGConfig() *configModels.RAGConfig {
	appConfig := m.GetAppConfig()
	if appConfig == nil {
		return nil
	}
	return &appConfig.RAG
}

// GetUIConfig 获取UI配置
func (m *Manager) GetUIConfig() *configModels.UIConfig {
	appConfig := m.GetAppConfig()
	if appConfig == nil {
		return nil
	}
	return &appConfig.UI
}

// GetAudioConfig 获取音频配置
func (m *Manager) GetAudioConfig() *configModels.AudioConfig {
	appConfig := m.GetAppConfig()
	if appConfig == nil {
		return nil
	}
	return &appConfig.Audio
}

// GetToolServerConfig 获取工具服务器配置
func (m *Manager) GetToolServerConfig() *configModels.ToolServerConfig {
	appConfig := m.GetAppConfig()
	if appConfig == nil {
		return nil
	}
	return &appConfig.ToolServer
}

// GetCodeExecutionConfig 获取代码执行配置
func (m *Manager) GetCodeExecutionConfig() *configModels.CodeExecutionConfig {
	appConfig := m.GetAppConfig()
	if appConfig == nil {
		return nil
	}
	return &appConfig.CodeExec
}

// GetImageGenConfig 获取图像生成配置
func (m *Manager) GetImageGenConfig() *configModels.ImageGenConfig {
	appConfig := m.GetAppConfig()
	if appConfig == nil {
		return nil
	}
	return &appConfig.ImageGen
}

// GetUserConfig 获取用户配置
func (m *Manager) GetUserConfig() *configModels.UserConfig {
	appConfig := m.GetAppConfig()
	if appConfig == nil {
		return nil
	}
	return &appConfig.User
}

// GetModelsConfig 获取模型配置
func (m *Manager) GetModelsConfig() *configModels.ModelsConfig {
	appConfig := m.GetAppConfig()
	if appConfig == nil {
		return nil
	}
	return &appConfig.Models
}

// GetModelConfig 根据模型ID获取模型配置
func (m *Manager) GetModelConfig(modelID string) *configModels.ModelConfig {
	modelsConfig := m.GetModelsConfig()
	if modelsConfig == nil {
		return nil
	}

	if modelConfig, exists := modelsConfig.ModelConfigs[modelID]; exists {
		return &modelConfig
	}

	return nil
}

// GetModelSystemPrompt 根据模型ID获取系统提示
func (m *Manager) GetModelSystemPrompt(modelID string) string {
	modelsConfig := m.GetModelsConfig()
	if modelsConfig == nil {
		return ""
	}

	// 先查找具体模型配置
	if modelConfig, exists := modelsConfig.ModelConfigs[modelID]; exists {
		if modelConfig.SystemPrompt != "" {
			return modelConfig.SystemPrompt
		}
	}

	// 如果找不到或为空，返回默认系统提示
	return modelsConfig.DefaultSystemPrompt
}

// GetConfigSummary 获取配置摘要信息
func (m *Manager) GetConfigSummary() map[string]interface{} {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if m.appConfig == nil {
		return map[string]interface{}{
			"loaded":  false,
			"version": 0,
		}
	}

	return map[string]interface{}{
		"loaded":           true,
		"version":          m.appConfig.Version,
		"openai_enabled":   m.appConfig.OpenAI.Enable,
		"ui_signup":        m.appConfig.UI.EnableSignup,
		"rag_enabled":      m.appConfig.RAG.TopK > 0,
		"audio_tts_engine": m.appConfig.Audio.TTS.Engine,
		"audio_stt_engine": m.appConfig.Audio.STT.Engine,
		"direct_enabled":   m.appConfig.Direct.Enable,
		"ollama_enabled":   m.appConfig.Ollama.Enable,
	}
}
