package config

import (
	"encoding/json"
	"fmt"
	"strings"

	configModels "hkchat_api/internal/models/config"
	"hkchat_api/internal/models/entities"
	"hkchat_api/internal/repository"
	"hkchat_api/pkg/utils"
)

// Service 配置服务实现
type Service struct {
	configRepo *repository.ConfigRepository
}

// NewService 创建配置服务实例
func NewService(configRepo *repository.ConfigRepository) *Service {
	return &Service{
		configRepo: configRepo,
	}
}

// LoadAppConfigFromDB 从数据库加载应用配置
func (s *Service) LoadAppConfigFromDB() (*configModels.AppConfiguration, error) {
	utils.Debug("从数据库加载应用配置")

	// 获取最新配置
	configEntity, err := s.configRepo.GetLatestConfig()
	if err != nil {
		utils.Errorf("获取最新配置失败: %v", err)
		return nil, fmt.Errorf("获取最新配置失败: %v", err)
	}

	if configEntity == nil {
		utils.Warn("数据库中没有找到配置记录")
		return nil, fmt.Errorf("数据库中没有找到配置记录")
	}

	// 将 entities.JSON 转换为 []byte
	configData, err := json.Marshal(configEntity.Data)
	if err != nil {
		utils.Errorf("序列化配置数据失败: %v", err)
		return nil, fmt.Errorf("序列化配置数据失败: %v", err)
	}

	// 解析配置数据
	appConfig, err := s.ParseConfigData(configData)
	if err != nil {
		utils.Errorf("解析配置数据失败: %v", err)
		return nil, fmt.Errorf("解析配置数据失败: %v", err)
	}

	utils.Debugf("成功加载应用配置，版本: %d", configEntity.Version)
	return appConfig, nil
}

// GetLatestConfig 获取最新配置
func (s *Service) GetLatestConfig() (*entities.Config, error) {
	utils.Debug("获取最新配置记录")
	return s.configRepo.GetLatestConfig()
}

// GetConfigByVersion 根据版本获取配置
func (s *Service) GetConfigByVersion(version int64) (*configModels.AppConfiguration, error) {
	utils.Debugf("根据版本获取配置: %d", version)

	configEntity, err := s.configRepo.GetConfigByVersion(version)
	if err != nil {
		utils.Errorf("根据版本获取配置失败: %v", err)
		return nil, fmt.Errorf("根据版本获取配置失败: %v", err)
	}

	if configEntity == nil {
		utils.Warnf("未找到版本 %d 的配置", version)
		return nil, fmt.Errorf("未找到版本 %d 的配置", version)
	}

	// 将 entities.JSON 转换为 []byte
	configData, err := json.Marshal(configEntity.Data)
	if err != nil {
		utils.Errorf("序列化配置数据失败: %v", err)
		return nil, fmt.Errorf("序列化配置数据失败: %v", err)
	}

	// 解析配置数据
	appConfig, err := s.ParseConfigData(configData)
	if err != nil {
		utils.Errorf("解析配置数据失败: %v", err)
		return nil, fmt.Errorf("解析配置数据失败: %v", err)
	}

	utils.Debugf("成功获取版本 %d 的配置", version)
	return appConfig, nil
}

// SaveAppConfig 保存应用配置到数据库
func (s *Service) SaveAppConfig(appConfig *configModels.AppConfiguration, version int64) error {
	utils.Debugf("保存应用配置到数据库，版本: %d", version)

	// 验证配置
	if err := s.ValidateAppConfig(appConfig); err != nil {
		utils.Errorf("配置验证失败: %v", err)
		return fmt.Errorf("配置验证失败: %v", err)
	}

	// 将配置序列化为JSON
	configData, err := json.Marshal(appConfig)
	if err != nil {
		utils.Errorf("序列化配置数据失败: %v", err)
		return fmt.Errorf("序列化配置数据失败: %v", err)
	}

	// 将 []byte 转换为 entities.JSON
	var configJSON entities.JSON
	if err := json.Unmarshal(configData, &configJSON); err != nil {
		utils.Errorf("转换配置数据为JSON失败: %v", err)
		return fmt.Errorf("转换配置数据为JSON失败: %v", err)
	}

	// 创建配置实体
	configEntity := &entities.Config{
		Data:    configJSON,
		Version: version,
	}

	// 保存到数据库
	if err := s.configRepo.SaveConfig(configEntity); err != nil {
		utils.Errorf("保存配置到数据库失败: %v", err)
		return fmt.Errorf("保存配置到数据库失败: %v", err)
	}

	utils.Debugf("成功保存应用配置，版本: %d", version)
	return nil
}

// ParseConfigData 解析配置数据
func (s *Service) ParseConfigData(data []byte) (*configModels.AppConfiguration, error) {
	utils.Debug("解析配置数据")

	if len(data) == 0 {
		return nil, fmt.Errorf("配置数据为空")
	}

	// 先验证是否为有效JSON
	if !json.Valid(data) {
		utils.Errorf("配置数据不是有效的JSON格式")
		return nil, fmt.Errorf("配置数据不是有效的JSON格式")
	}

	var appConfig configModels.AppConfiguration
	if err := json.Unmarshal(data, &appConfig); err != nil {
		utils.Errorf("解析JSON配置失败: %v", err)
		return nil, fmt.Errorf("解析JSON配置失败: %v", err)
	}

	// 验证解析后的配置
	if err := s.ValidateAppConfig(&appConfig); err != nil {
		utils.Errorf("配置验证失败: %v", err)
		return nil, fmt.Errorf("配置验证失败: %v", err)
	}

	utils.Debug("配置数据解析成功")
	return &appConfig, nil
}

// ValidateAppConfig 验证应用配置
func (s *Service) ValidateAppConfig(appConfig *configModels.AppConfiguration) error {
	utils.Debug("验证应用配置")

	if appConfig == nil {
		return fmt.Errorf("配置不能为空")
	}

	// 验证版本
	if appConfig.Version < 0 {
		return fmt.Errorf("配置版本不能为负数")
	}

	// 验证OpenAI配置
	if err := s.validateOpenAIConfig(&appConfig.OpenAI); err != nil {
		return fmt.Errorf("OpenAI配置验证失败: %v", err)
	}

	// 验证UI配置
	if err := s.validateUIConfig(&appConfig.UI); err != nil {
		return fmt.Errorf("UI配置验证失败: %v", err)
	}

	// 验证RAG配置
	if err := s.validateRAGConfig(&appConfig.RAG); err != nil {
		return fmt.Errorf("RAG配置验证失败: %v", err)
	}

	// 验证Audio配置
	if err := s.validateAudioConfig(&appConfig.Audio); err != nil {
		return fmt.Errorf("Audio配置验证失败: %v", err)
	}

	// 验证Auth配置
	if err := s.validateAuthConfig(&appConfig.Auth); err != nil {
		return fmt.Errorf("Auth配置验证失败: %v", err)
	}

	utils.Debug("应用配置验证通过")
	return nil
}

// validateOpenAIConfig 验证OpenAI配置
func (s *Service) validateOpenAIConfig(openaiConfig *configModels.AppOpenAIConfig) error {
	if openaiConfig == nil {
		return fmt.Errorf("OpenAI配置不能为空")
	}

	// 如果启用了OpenAI，必须有API Keys
	if openaiConfig.Enable && len(openaiConfig.APIKeys) == 0 {
		return fmt.Errorf("启用OpenAI时必须配置API Keys")
	}

	// 验证API Keys格式
	for i, apiKey := range openaiConfig.APIKeys {
		if strings.TrimSpace(apiKey) == "" {
			// 在开发环境中允许空的API Key
			utils.Warnf("API Key[%d]为空，这在生产环境中可能会导致问题", i)
			continue
		}
	}

	// 验证API Base URLs
	for i, baseURL := range openaiConfig.APIBaseURLs {
		if strings.TrimSpace(baseURL) == "" {
			return fmt.Errorf("API Base URL[%d]不能为空", i)
		}
		if !strings.HasPrefix(baseURL, "http://") && !strings.HasPrefix(baseURL, "https://") {
			return fmt.Errorf("API Base URL[%d]必须以http://或https://开头", i)
		}
	}

	// 验证API配置
	for idx, apiConfig := range openaiConfig.APIConfigs {
		if err := s.validateAPIConfig(idx, &apiConfig); err != nil {
			return fmt.Errorf("API配置[%s]验证失败: %v", idx, err)
		}
	}

	return nil
}

// validateAPIConfig 验证API配置
func (s *Service) validateAPIConfig(idx string, apiConfig *configModels.AppOpenAIAPIConfig) error {
	if apiConfig == nil {
		return fmt.Errorf("API配置不能为空")
	}

	// 验证连接类型
	validConnectionTypes := []string{"openai", "external", "local"}
	if !s.isValidConnectionType(apiConfig.ConnectionType, validConnectionTypes) {
		return fmt.Errorf("无效的连接类型: %s", apiConfig.ConnectionType)
	}

	return nil
}

// validateUIConfig 验证UI配置
func (s *Service) validateUIConfig(uiConfig *configModels.UIConfig) error {
	if uiConfig == nil {
		return fmt.Errorf("UI配置不能为空")
	}

	// 验证默认用户角色
	validUserRoles := []string{"admin", "user", "pending"}
	if uiConfig.DefaultUserRole != "" && !s.contains(validUserRoles, uiConfig.DefaultUserRole) {
		return fmt.Errorf("无效的默认用户角色: %s", uiConfig.DefaultUserRole)
	}

	// 验证提示建议
	for i, suggestion := range uiConfig.PromptSuggestions {
		// 处理不同类型的Title - 在开发环境中更宽松的验证
		var titleEmpty bool = true
		switch v := suggestion.Title.(type) {
		case []string:
			titleEmpty = len(v) == 0
		case string:
			titleEmpty = strings.TrimSpace(v) == ""
		case []interface{}:
			titleEmpty = len(v) == 0
		default:
			// 对于其他类型，警告但允许通过
			utils.Warnf("提示建议[%d]的标题类型不是预期的格式", i)
			titleEmpty = false
		}

		if titleEmpty {
			// 在开发环境中只警告，不阻止启动
			utils.Warnf("提示建议[%d]的标题为空", i)
		}

		// 处理不同类型的Content - 只做警告，不阻止启动
		switch v := suggestion.Content.(type) {
		case string:
			if strings.TrimSpace(v) == "" {
				utils.Warnf("提示建议[%d]的内容为空", i)
			}
		case map[string]interface{}:
			// 对象类型是允许的
		default:
			utils.Warnf("提示建议[%d]的内容类型未知", i)
		}
	}

	return nil
}

// validateRAGConfig 验证RAG配置
func (s *Service) validateRAGConfig(ragConfig *configModels.RAGConfig) error {
	if ragConfig == nil {
		return fmt.Errorf("RAG配置不能为空")
	}

	// 验证TopK值
	if ragConfig.TopK < 1 {
		return fmt.Errorf("TopK值必须大于0")
	}

	// 验证ChunkSize
	if ragConfig.ChunkSize < 1 {
		return fmt.Errorf("ChunkSize必须大于0")
	}

	// 验证ChunkOverlap
	if ragConfig.ChunkOverlap < 0 {
		return fmt.Errorf("ChunkOverlap不能为负数")
	}

	// 验证RelevanceThreshold
	if ragConfig.RelevanceThreshold < 0 || ragConfig.RelevanceThreshold > 1 {
		return fmt.Errorf("RelevanceThreshold必须在0-1之间")
	}

	return nil
}

// validateAudioConfig 验证音频配置
func (s *Service) validateAudioConfig(audioConfig *configModels.AudioConfig) error {
	if audioConfig == nil {
		return fmt.Errorf("Audio配置不能为空")
	}

	// 验证TTS引擎
	validTTSEngines := []string{"openai", "azure", "local", "web"}
	if audioConfig.TTS.Engine != "" && !s.contains(validTTSEngines, audioConfig.TTS.Engine) {
		// 在开发环境中只警告，不阻止启动
		utils.Warnf("不支持的TTS引擎: %s，支持的引擎: %v", audioConfig.TTS.Engine, validTTSEngines)
	}

	// 验证STT引擎
	validSTTEngines := []string{"openai", "azure", "whisper", "deepgram", "web"}
	if audioConfig.STT.Engine != "" && !s.contains(validSTTEngines, audioConfig.STT.Engine) {
		// 在开发环境中只警告，不阻止启动
		utils.Warnf("不支持的STT引擎: %s，支持的引擎: %v", audioConfig.STT.Engine, validSTTEngines)
	}

	return nil
}

// validateAuthConfig 验证认证配置
func (s *Service) validateAuthConfig(authConfig *configModels.AppAuthConfig) error {
	if authConfig == nil {
		return fmt.Errorf("Auth配置不能为空")
	}

	// 验证JWT过期时间格式
	if authConfig.JWTExpiry != "" && authConfig.JWTExpiry != "-1" {
		// 可以添加更详细的时间格式验证
		if !strings.HasSuffix(authConfig.JWTExpiry, "h") &&
			!strings.HasSuffix(authConfig.JWTExpiry, "m") &&
			!strings.HasSuffix(authConfig.JWTExpiry, "s") {
			return fmt.Errorf("JWT过期时间格式不正确: %s", authConfig.JWTExpiry)
		}
	}

	return nil
}

// 辅助方法

// isValidConnectionType 检查连接类型是否有效
func (s *Service) isValidConnectionType(connectionType string, validTypes []string) bool {
	return s.contains(validTypes, connectionType)
}

// contains 检查字符串切片是否包含指定元素
func (s *Service) contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}
