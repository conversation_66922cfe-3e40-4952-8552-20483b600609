package config

import (
	"fmt"
)

// GetRedisConfig 获取Redis配置
func GetRedisConfig() *RedisConfig {
	if GlobalConfig == nil {
		return nil
	}
	return &GlobalConfig.Redis
}

// GetRedisAddr 获取Redis地址
func GetRedisAddr() string {
	if GlobalConfig == nil {
		return ""
	}
	return fmt.Sprintf("%s:%d", GlobalConfig.Redis.Host, GlobalConfig.Redis.Port)
}

// GetRedisPassword 获取Redis密码
func GetRedisPassword() string {
	if GlobalConfig == nil {
		return ""
	}
	return GlobalConfig.Redis.Password
}

// GetRedisDB 获取Redis数据库编号
func GetRedisDB() int {
	if GlobalConfig == nil {
		return 0
	}
	return GlobalConfig.Redis.DB
}

// GetRedisHost 获取Redis主机地址
func GetRedisHost() string {
	if GlobalConfig == nil {
		return ""
	}
	return GlobalConfig.Redis.Host
}

// GetRedisPort 获取Redis端口
func GetRedisPort() int {
	if GlobalConfig == nil {
		return 6379
	}
	return GlobalConfig.Redis.Port
}
