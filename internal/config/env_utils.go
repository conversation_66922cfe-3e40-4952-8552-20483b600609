package config

import (
	"fmt"
	"os"
	"strconv"
	"strings"

	"github.com/joho/godotenv"
)

// loadEnvFile 加载环境变量文件
func loadEnvFile() error {
	logger := getConfigLogger()

	// 尝试加载不同的环境文件
	envFiles := []string{
		".env.local",
		".env",
	}

	for _, envFile := range envFiles {
		if err := loadSingleEnvFile(envFile); err != nil {
			logger.Warnf("⚠️ 加载 %s 文件失败: %v", envFile, err)
		} else {
			logger.Infof("📄 已加载环境变量文件: %s", envFile)
			return nil
		}
	}

	return fmt.Errorf("未找到环境变量文件")
}

// loadSingleEnvFile 加载单个环境变量文件
func loadSingleEnvFile(envFile string) error {
	if _, err := os.Stat(envFile); os.IsNotExist(err) {
		return fmt.Errorf("文件不存在: %s", envFile)
	}
	return godotenv.Load(envFile)
}

// getEnvironment 获取环境变量
func getEnvironment() string {
	env := os.Getenv("APP_ENV")
	if env == "" {
		env = "dev" // 默认环境
		logger := getConfigLogger()
		logger.Warnf("⚠️ 未知环境变量 APP_ENV=%s，使用默认环境 dev", env)
	}
	return env
}

// getEnvFromFile 从环境变量文件中获取环境
func getEnvFromFile() string {
	appEnv := os.Getenv("APP_ENV")
	if appEnv == "" {
		appEnv = "dev" // 默认环境
		logger := getConfigLogger()
		logger.Warnf("⚠️ 未知环境变量 APP_ENV=%s，使用默认环境 dev", appEnv)
	}
	return appEnv
}

// resolveEnvVars 解析环境变量
func resolveEnvVars(value string) string {
	logger := getConfigLogger()

	// 检查是否包含环境变量引用
	if strings.Contains(value, "${") {
		// 简单的环境变量替换
		for strings.Contains(value, "${") {
			start := strings.Index(value, "${")
			end := strings.Index(value[start:], "}")
			if end == -1 {
				break
			}

			envVar := value[start+2 : start+end]
			envValue := os.Getenv(envVar)
			if envValue == "" {
				logger.Warnf("⚠️ 环境变量 %s 未设置，使用原值", envVar)
				break
			}

			value = value[:start] + envValue + value[start+end+1:]
		}
	}

	return value
}

// overrideFromEnv 从环境变量覆盖配置
func overrideFromEnv(config *Config) {
	overrideAppConfig(config)
	overrideDatabaseConfig(config)
	overrideRedisConfig(config)
	overrideJWTConfig(config)
	overrideOpenAIConfig(config)
	overrideTavilyConfig(config)
}

// overrideAppConfig 覆盖应用配置
func overrideAppConfig(config *Config) {
	if port := os.Getenv("APP_PORT"); port != "" {
		if p, err := strconv.Atoi(port); err == nil {
			config.App.Port = p
		}
	}
	if debug := os.Getenv("APP_DEBUG"); debug != "" {
		if d, err := strconv.ParseBool(debug); err == nil {
			config.App.Debug = d
		}
	}
	if name := os.Getenv("APP_NAME"); name != "" {
		config.App.Name = name
	}
	if version := os.Getenv("APP_VERSION"); version != "" {
		config.App.Version = version
	}
	if env := os.Getenv("APP_ENV"); env != "" {
		config.App.Env = env
	}
}

// overrideDatabaseConfig 覆盖数据库配置
func overrideDatabaseConfig(config *Config) {
	if host := os.Getenv("DB_HOST"); host != "" {
		config.Database.Host = host
	}
	if port := os.Getenv("DB_PORT"); port != "" {
		if p, err := strconv.Atoi(port); err == nil {
			config.Database.Port = p
		}
	}
	if user := os.Getenv("DB_USER"); user != "" {
		config.Database.User = user
	}
	if password := os.Getenv("DB_PASSWORD"); password != "" {
		config.Database.Password = password
	}
	if dbname := os.Getenv("DB_NAME"); dbname != "" {
		config.Database.DBName = dbname
	}
	if sslmode := os.Getenv("DB_SSLMODE"); sslmode != "" {
		config.Database.SSLMode = sslmode
	}
	if timezone := os.Getenv("DB_TIMEZONE"); timezone != "" {
		config.Database.Timezone = timezone
	}
}

// overrideRedisConfig 覆盖Redis配置
func overrideRedisConfig(config *Config) {
	if host := os.Getenv("REDIS_HOST"); host != "" {
		config.Redis.Host = host
	}
	if port := os.Getenv("REDIS_PORT"); port != "" {
		if p, err := strconv.Atoi(port); err == nil {
			config.Redis.Port = p
		}
	}
	if password := os.Getenv("REDIS_PASSWORD"); password != "" {
		config.Redis.Password = password
	}
	if db := os.Getenv("REDIS_DB"); db != "" {
		if d, err := strconv.Atoi(db); err == nil {
			config.Redis.DB = d
		}
	}
}

// overrideJWTConfig 覆盖JWT配置
func overrideJWTConfig(config *Config) {
	if secretKey := os.Getenv("JWT_SECRET"); secretKey != "" {
		config.JWT.SecretKey = secretKey
	}
	if expireHours := os.Getenv("JWT_EXPIRE_HOURS"); expireHours != "" {
		if h, err := strconv.Atoi(expireHours); err == nil {
			config.JWT.ExpireHours = h
		}
	}
}

// overrideOpenAIConfig 覆盖OpenAI配置
func overrideOpenAIConfig(config *Config) {
	if apiKey := os.Getenv("OPENAI_API_KEY"); apiKey != "" {
		config.OpenAI.APIKey = apiKey
	}
	if baseURL := os.Getenv("OPENAI_BASE_URL"); baseURL != "" {
		config.OpenAI.BaseURL = baseURL
	}
	if model := os.Getenv("OPENAI_MODEL"); model != "" {
		config.OpenAI.Model = model
	}
	if maxTokens := os.Getenv("OPENAI_MAX_TOKENS"); maxTokens != "" {
		if m, err := strconv.Atoi(maxTokens); err == nil {
			config.OpenAI.MaxTokens = m
		}
	}
	if temperature := os.Getenv("OPENAI_TEMPERATURE"); temperature != "" {
		if t, err := strconv.ParseFloat(temperature, 32); err == nil {
			config.OpenAI.Temperature = float32(t)
		}
	}
	if timeout := os.Getenv("OPENAI_TIMEOUT"); timeout != "" {
		if t, err := strconv.Atoi(timeout); err == nil {
			config.OpenAI.Timeout = t
		}
	}
}

// overrideTavilyConfig 覆盖Tavily配置
func overrideTavilyConfig(config *Config) {
	if apiKey := os.Getenv("TAVILY_API_KEY"); apiKey != "" {
		config.Tavily.APIKey = apiKey
	}
	if baseURL := os.Getenv("TAVILY_BASE_URL"); baseURL != "" {
		config.Tavily.BaseURL = baseURL
	}
	if maxResults := os.Getenv("TAVILY_MAX_RESULTS"); maxResults != "" {
		if m, err := strconv.Atoi(maxResults); err == nil {
			config.Tavily.MaxResults = m
		}
	}
	if timeout := os.Getenv("TAVILY_TIMEOUT"); timeout != "" {
		if t, err := strconv.Atoi(timeout); err == nil {
			config.Tavily.Timeout = t
		}
	}
}
