package config

import (
	"fmt"
	"io"
	"os"
	"path/filepath"

	"github.com/sirupsen/logrus"
	"gopkg.in/natefinch/lumberjack.v2"
)

// ConfigFormatter 配置专用的简洁格式化器
type ConfigFormatter struct{}

// Format 实现logrus.Formatter接口
func (f *ConfigFormatter) Format(entry *logrus.Entry) ([]byte, error) {
	// 对于配置加载阶段，显示时间戳让用户知道进度
	timestamp := entry.Time.Format("2006/01/02 15:04:05")
	level := getEmojiForLevel(entry.Level)
	message := entry.Message
	return []byte(fmt.Sprintf("%s %s %s\n", timestamp, level, message)), nil
}

// getEmojiForLevel 获取日志级别对应的emoji
func getEmojiForLevel(level logrus.Level) string {
	switch level {
	case logrus.DebugLevel:
		return "🐛"
	case logrus.InfoLevel:
		return "📝"
	case logrus.WarnLevel:
		return "⚠️"
	case logrus.ErrorLevel:
		return "❌"
	case logrus.FatalLevel:
		return "💀"
	default:
		return "📝"
	}
}

// getConfigLogger 获取配置专用的日志实例
func getConfigLogger() *logrus.Logger {
	logger := logrus.New()

	// 设置简洁格式（不显示时间戳）
	logger.SetFormatter(&ConfigFormatter{})

	// 如果全局配置存在，使用配置的日志设置
	if GlobalConfig != nil {
		// 设置日志级别
		level, err := logrus.ParseLevel(GlobalConfig.Log.Level)
		if err != nil {
			level = logrus.InfoLevel
		}
		logger.SetLevel(level)

		// 如果文件路径为空，只输出到控制台
		if GlobalConfig.Log.FilePath == "" {
			logger.SetOutput(os.Stdout)
		} else {
			// 创建日志目录
			logDir := filepath.Dir(GlobalConfig.Log.FilePath)
			if err := os.MkdirAll(logDir, 0755); err == nil {
				// 配置日志轮转
				lumberJackLogger := &lumberjack.Logger{
					Filename:   GlobalConfig.Log.FilePath,
					MaxSize:    GlobalConfig.Log.MaxSize,
					MaxBackups: GlobalConfig.Log.MaxBackups,
					MaxAge:     GlobalConfig.Log.MaxAge,
					Compress:   true,
				}

				// 同时输出到控制台和文件
				multiWriter := io.MultiWriter(os.Stdout, lumberJackLogger)
				logger.SetOutput(multiWriter)
			} else {
				// 如果创建目录失败，只输出到控制台
				logger.SetOutput(os.Stdout)
			}
		}
	} else {
		// 默认配置：只输出到控制台
		logger.SetOutput(os.Stdout)
		logger.SetLevel(logrus.InfoLevel)
	}

	return logger
}

// PrintConfigSummary 打印配置摘要
func PrintConfigSummary() {
	logger := getConfigLogger()

	if GlobalConfig == nil {
		logger.Warn("⚠️ 配置未加载")
		return
	}

	logger.Info("📋 ===== 配置摘要 =====")
	logger.Infof("📱 应用: %s v%s", GlobalConfig.App.Name, GlobalConfig.App.Version)
	logger.Infof("🌍 环境: %s", GlobalConfig.App.Env)
	logger.Infof("🌐 端口: %d", GlobalConfig.App.Port)
	logger.Infof("🐛 调试: %v", GlobalConfig.App.Debug)
	logger.Infof("🗄️  数据库: %s@%s:%d/%s",
		GlobalConfig.Database.User,
		GlobalConfig.Database.Host,
		GlobalConfig.Database.Port,
		GlobalConfig.Database.DBName)
	logger.Infof("📝 日志: %s (%s)", GlobalConfig.Log.Level, GlobalConfig.Log.FilePath)
	logger.Infof("🔐 JWT: %d小时过期", GlobalConfig.JWT.ExpireHours)
	logger.Info("📋 ===== 摘要结束 =====")
}
