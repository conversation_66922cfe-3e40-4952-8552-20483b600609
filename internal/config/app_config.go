package config

// GetAppPort 获取应用端口
func GetAppPort() int {
	if GlobalConfig != nil {
		return GlobalConfig.App.Port
	}
	return 8080 // 默认端口
}

// GetAppName 获取应用名称
func GetAppName() string {
	if GlobalConfig != nil {
		return GlobalConfig.App.Name
	}
	return "HKChat API" // 默认名称
}

// GetAppVersion 获取应用版本
func GetAppVersion() string {
	if GlobalConfig != nil {
		return GlobalConfig.App.Version
	}
	return "1.0.0" // 默认版本
}

// GetAppEnv 获取应用环境
func GetAppEnv() string {
	if GlobalConfig != nil {
		return GlobalConfig.App.Env
	}
	return "dev" // 默认环境
}

// IsDebugMode 是否调试模式
func IsDebugMode() bool {
	if GlobalConfig != nil {
		return GlobalConfig.App.Debug
	}
	return false // 默认关闭调试
}

// GetJWTConfig 获取JWT配置
func GetJWTConfig() *JWTConfig {
	if GlobalConfig == nil {
		return nil
	}
	return &GlobalConfig.JWT
}

// GetLogConfig 获取日志配置
func GetLogConfig() *LogConfig {
	if GlobalConfig == nil {
		return nil
	}
	return &GlobalConfig.Log
}

// GetCORSConfig 获取CORS配置
func GetCORSConfig() *CORSConfig {
	if GlobalConfig == nil {
		return nil
	}
	return &GlobalConfig.CORS
}

// GetOpenAIConfig 获取OpenAI配置
func GetOpenAIConfig() *OpenAIConfig {
	if GlobalConfig == nil {
		return nil
	}
	return &GlobalConfig.OpenAI
}

// GetTavilyConfig 获取Tavily配置
func GetTavilyConfig() *TavilyConfig {
	if GlobalConfig == nil {
		return nil
	}
	return &GlobalConfig.Tavily
}

// IsProduction 是否生产环境
func IsProduction() bool {
	if GlobalConfig != nil {
		return GlobalConfig.App.Env == "production"
	}
	return false
}

// IsDevelopment 是否开发环境
func IsDevelopment() bool {
	if GlobalConfig != nil {
		return GlobalConfig.App.Env == "development"
	}
	return true // 默认开发环境
}
