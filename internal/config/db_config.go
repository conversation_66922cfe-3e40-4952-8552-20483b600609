package config

import (
	"fmt"
	"hkchat_api/pkg/clients/database"
)

// GetDatabaseConfig 获取数据库配置
func GetDatabaseConfig() *database.Config {
	if GlobalConfig == nil {
		return nil
	}

	// 数据库日志级别跟随应用日志级别
	var dbLogLevel string
	if GlobalConfig.Database.LogLevel != "" {
		dbLogLevel = GlobalConfig.Database.LogLevel
	} else {
		// 如果数据库没有单独配置日志级别，使用应用级别的日志级别
		dbLogLevel = GlobalConfig.Log.Level
	}

	return &database.Config{
		Host:        GlobalConfig.Database.Host,
		Port:        GlobalConfig.Database.Port,
		User:        GlobalConfig.Database.User,
		Password:    GlobalConfig.Database.Password,
		DBName:      GlobalConfig.Database.DBName,
		SSLMode:     GlobalConfig.Database.SSLMode,
		Timezone:    GlobalConfig.Database.Timezone,
		MaxOpenConn: GlobalConfig.Database.MaxOpenConn,
		MaxIdleConn: GlobalConfig.Database.MaxIdleConn,
		LogLevel:    dbLogLevel,
	}
}

// GetDatabaseDSN 获取数据库连接字符串
func GetDatabaseDSN() string {
	if GlobalConfig == nil {
		return ""
	}
	return fmt.Sprintf(
		"host=%s user=%s password=%s dbname=%s port=%d sslmode=%s TimeZone=%s",
		GlobalConfig.Database.Host,
		GlobalConfig.Database.User,
		GlobalConfig.Database.Password,
		GlobalConfig.Database.DBName,
		GlobalConfig.Database.Port,
		GlobalConfig.Database.SSLMode,
		GlobalConfig.Database.Timezone,
	)
}

// GetDatabaseHost 获取数据库主机地址
func GetDatabaseHost() string {
	if GlobalConfig == nil {
		return ""
	}
	return GlobalConfig.Database.Host
}

// GetDatabasePort 获取数据库端口
func GetDatabasePort() int {
	if GlobalConfig == nil {
		return 5432
	}
	return GlobalConfig.Database.Port
}

// GetDatabaseUser 获取数据库用户名
func GetDatabaseUser() string {
	if GlobalConfig == nil {
		return ""
	}
	return GlobalConfig.Database.User
}

// GetDatabaseName 获取数据库名称
func GetDatabaseName() string {
	if GlobalConfig == nil {
		return ""
	}
	return GlobalConfig.Database.DBName
}
