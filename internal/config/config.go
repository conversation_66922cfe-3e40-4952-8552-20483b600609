package config

import (
	"fmt"
	"os"

	"hkchat_api/pkg/utils"

	"gopkg.in/yaml.v3"
)

// 全局配置实例
var GlobalConfig *Config

// InitConfig 初始化配置
func InitConfig() error {
	utils.Debug("🔧 初始化配置系统...")

	if err := loadConfig(); err != nil {
		return fmt.Errorf("加载应用配置失败: %v", err)
	}

	utils.Debug("✅ 配置系统初始化完成")
	return nil
}

// ReloadConfig 重新加载配置
func ReloadConfig() error {
	utils.Info("🔄 重新加载配置...")

	if err := loadConfig(); err != nil {
		return fmt.Errorf("重新加载应用配置失败: %v", err)
	}

	utils.Info("✅ 配置重新加载完成")
	return nil
}

// loadConfig 内部配置加载函数
func loadConfig() error {
	logger := getConfigLogger()

	// 加载环境变量
	if err := loadEnvFile(); err != nil {
		// 忽略环境文件加载错误，继续使用系统环境变量
	}

	// 确定环境
	env := getEnvironment()

	// 加载配置文件
	configFile := fmt.Sprintf("config/%s.yaml", env)
	logger.Infof("🔧 加载 %s 环境配置 (文件: %s)...", env, configFile)

	data, err := os.ReadFile(configFile)
	if err != nil {
		return fmt.Errorf("读取配置文件失败: %v", err)
	}

	// 解析 YAML
	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return fmt.Errorf("解析配置文件失败: %v", err)
	}

	// 从环境变量中覆盖配置
	overrideFromEnv(&config)

	// 设置全局配置
	GlobalConfig = &config

	logger.Infof("✅ %s 环境配置加载成功", env)
	logger.Infof("📊 应用: %s v%s", config.App.Name, config.App.Version)
	logger.Infof("🌐 端口: %d", config.App.Port)
	logger.Infof("🗄️  数据库: %s@%s:%d/%s", config.Database.User, config.Database.Host, config.Database.Port, config.Database.DBName)

	return nil
}
