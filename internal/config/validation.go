package config

import (
	"fmt"
)

// ValidateConfig 验证配置
func ValidateConfig() error {
	if GlobalConfig == nil {
		return fmt.Errorf("配置未初始化")
	}

	return GlobalConfig.ValidateConfig()
}

// ValidateConfig 验证配置方法
func (c *Config) ValidateConfig() error {
	if c.Database.Host == "" {
		return fmt.Errorf("数据库主机地址不能为空")
	}
	if c.Database.User == "" {
		return fmt.Errorf("数据库用户名不能为空")
	}
	if c.Database.DBName == "" {
		return fmt.Errorf("数据库名称不能为空")
	}
	if c.JWT.SecretKey == "" {
		return fmt.Errorf("JWT密钥不能为空")
	}
	if c.App.Port <= 0 || c.App.Port > 65535 {
		return fmt.Errorf("端口号必须在1-65535之间")
	}
	return nil
}

// ValidateAppConfig 验证应用配置
func ValidateAppConfig() error {
	if GlobalConfig == nil {
		return fmt.Errorf("配置未初始化")
	}

	if GlobalConfig.App.Name == "" {
		return fmt.Errorf("应用名称不能为空")
	}
	if GlobalConfig.App.Version == "" {
		return fmt.Errorf("应用版本不能为空")
	}
	if GlobalConfig.App.Env == "" {
		return fmt.Errorf("应用环境不能为空")
	}

	return nil
}

// ValidateDatabaseConfig 验证数据库配置
func ValidateDatabaseConfig() error {
	if GlobalConfig == nil {
		return fmt.Errorf("配置未初始化")
	}

	if GlobalConfig.Database.Host == "" {
		return fmt.Errorf("数据库主机地址不能为空")
	}
	if GlobalConfig.Database.User == "" {
		return fmt.Errorf("数据库用户名不能为空")
	}
	if GlobalConfig.Database.DBName == "" {
		return fmt.Errorf("数据库名称不能为空")
	}
	if GlobalConfig.Database.Port <= 0 || GlobalConfig.Database.Port > 65535 {
		return fmt.Errorf("数据库端口号必须在1-65535之间")
	}

	return nil
}

// ValidateRedisConfig 验证Redis配置
func ValidateRedisConfig() error {
	if GlobalConfig == nil {
		return fmt.Errorf("配置未初始化")
	}

	if GlobalConfig.Redis.Host == "" {
		return fmt.Errorf("Redis主机地址不能为空")
	}
	if GlobalConfig.Redis.Port <= 0 || GlobalConfig.Redis.Port > 65535 {
		return fmt.Errorf("Redis端口号必须在1-65535之间")
	}

	return nil
}

// ValidateJWTConfig 验证JWT配置
func ValidateJWTConfig() error {
	if GlobalConfig == nil {
		return fmt.Errorf("配置未初始化")
	}

	if GlobalConfig.JWT.SecretKey == "" {
		return fmt.Errorf("JWT密钥不能为空")
	}
	if GlobalConfig.JWT.ExpireHours <= 0 {
		return fmt.Errorf("JWT过期时间必须大于0")
	}

	return nil
}
