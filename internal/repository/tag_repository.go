package repository

import (
	"hkchat_api/internal/models/entities"

	"gorm.io/gorm"
)

// TagRepository 标签仓储
type TagRepository struct {
	*BaseRepository
}

// NewTagRepository 创建标签仓储实例
func NewTagRepository(db *gorm.DB) *TagRepository {
	return &TagRepository{
		BaseRepository: NewBaseRepository(db),
	}
}

// ==================== 业务特有方法 ====================

// GetByUserID 根据用户ID获取标签列表
func (r *TagRepository) GetByUserID(userID string, offset, limit int) ([]*entities.Tag, error) {
	var tags []*entities.Tag
	err := r.BaseRepository.WhereWithPagination(&tags, "user_id = ?", offset, limit, userID)
	return tags, err
}

// SearchByKeyword 根据关键词搜索标签
func (r *TagRepository) SearchByKeyword(keyword string, offset, limit int) ([]*entities.Tag, error) {
	var tags []*entities.Tag
	searchFields := []string{"name"}
	err := r.BaseRepository.SearchWithSelect(&tags, "*", searchFields, keyword, offset, limit)
	return tags, err
}
