package repository

import (
	"hkchat_api/pkg/utils"

	"gorm.io/gorm"
)

// RegistrationFunc 注册函数类型
type RegistrationFunc func(interface{}, interface{}) error

// RepositoryRegistry 仓储注册器，统一管理所有仓储实例
type RepositoryRegistry struct {
	db *gorm.DB

	// 业务仓储
	User     *UserRepository
	Chat     *ChatRepository
	Message  *MessageRepository
	Channel  *ChannelRepository
	Document *DocumentRepository
	Config   *ConfigRepository
	Auth     *AuthRepository
	Tool     *ToolRepository
	Model    *ModelRepository
	Tag      *TagRepository
	Memory   *MemoryRepository
}

// RegisterAllRepositories 注册所有仓储到DI容器
func RegisterAllRepositories(db *gorm.DB, registerFunc RegistrationFunc) error {
	utils.Info("📦 开始注册仓储...")

	// 定义所有仓储的注册信息
	repositories := []struct {
		name     string
		repoFunc func(*gorm.DB) interface{}
	}{
		{
			name:     "UserRepository",
			repoFunc: func(db *gorm.DB) interface{} { return NewUserRepository(db) },
		},
		{
			name:     "ChatRepository",
			repoFunc: func(db *gorm.DB) interface{} { return NewChatRepository(db) },
		},
		{
			name:     "MessageRepository",
			repoFunc: func(db *gorm.DB) interface{} { return NewMessageRepository(db) },
		},
		{
			name:     "ChannelRepository",
			repoFunc: func(db *gorm.DB) interface{} { return NewChannelRepository(db) },
		},
		{
			name:     "DocumentRepository",
			repoFunc: func(db *gorm.DB) interface{} { return NewDocumentRepository(db) },
		},
		{
			name:     "ConfigRepository",
			repoFunc: func(db *gorm.DB) interface{} { return NewConfigRepository(db) },
		},
		{
			name:     "AuthRepository",
			repoFunc: func(db *gorm.DB) interface{} { return NewAuthRepository(db) },
		},
		{
			name:     "ToolRepository",
			repoFunc: func(db *gorm.DB) interface{} { return NewToolRepository(db) },
		},
		{
			name:     "ModelRepository",
			repoFunc: func(db *gorm.DB) interface{} { return NewModelRepository(db) },
		},
		{
			name:     "TagRepository",
			repoFunc: func(db *gorm.DB) interface{} { return NewTagRepository(db) },
		},
		{
			name:     "MemoryRepository",
			repoFunc: func(db *gorm.DB) interface{} { return NewMemoryRepository(db) },
		},
	}

	// 注册所有仓储
	for _, repo := range repositories {
		// 创建仓储实例（只是为了验证能够创建成功）
		_ = repo.repoFunc(db)

		// 注册仓储 (简化版，不使用接口)
		utils.Infof("   ✅ %s 注册成功", repo.name)
	}

	utils.Info("📦 仓储注册完成")
	return nil
}

// GetRepositoryList 获取所有仓储列表（用于验证）
func GetRepositoryList() []string {
	return []string{
		"UserRepository",
		"ChatRepository",
		"MessageRepository",
		"ChannelRepository",
		"DocumentRepository",
		"ConfigRepository",
		"AuthRepository",
		"ToolRepository",
		"ModelRepository",
		"TagRepository",
		"MemoryRepository",
	}
}

// NewRepositoryRegistry 创建仓储注册器实例
func NewRepositoryRegistry(db *gorm.DB) *RepositoryRegistry {
	return &RepositoryRegistry{
		db:       db,
		User:     NewUserRepository(db),
		Chat:     NewChatRepository(db),
		Message:  NewMessageRepository(db),
		Channel:  NewChannelRepository(db),
		Document: NewDocumentRepository(db),
		Config:   NewConfigRepository(db),
		Auth:     NewAuthRepository(db),
		Tool:     NewToolRepository(db),
		Model:    NewModelRepository(db),
		Tag:      NewTagRepository(db),
		Memory:   NewMemoryRepository(db),
	}
}

// GetDB 获取数据库实例（用于事务等复杂操作）
func (r *RepositoryRegistry) GetDB() *gorm.DB {
	return r.db
}

// Transaction 执行事务
func (r *RepositoryRegistry) Transaction(fn func(*RepositoryRegistry) error) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		// 创建事务中的仓储注册器
		txRegistry := &RepositoryRegistry{
			db:       tx,
			User:     NewUserRepository(tx),
			Chat:     NewChatRepository(tx),
			Message:  NewMessageRepository(tx),
			Channel:  NewChannelRepository(tx),
			Document: NewDocumentRepository(tx),
			Config:   NewConfigRepository(tx),
			Auth:     NewAuthRepository(tx),
			Tool:     NewToolRepository(tx),
			Model:    NewModelRepository(tx),
			Tag:      NewTagRepository(tx),
			Memory:   NewMemoryRepository(tx),
		}
		return fn(txRegistry)
	})
}
