package repository

import (
	"hkchat_api/internal/models/entities"

	"gorm.io/gorm"
)

// UserRepository 用户仓储
type UserRepository struct {
	*BaseRepository        // 组合基础仓储
	userFields      string // 用户字段列表（避免复杂JSON字段的问题）
}

// NewUserRepository 创建用户仓储实例
func NewUserRepository(db *gorm.DB) *UserRepository {
	return &UserRepository{
		BaseRepository: NewBaseRepository(db),
		userFields:     "id, name, email, role, profile_image_url, api_key, created_at, updated_at, last_active_at, oauth_sub",
	}
}

// GetUserFields 获取用户字段列表（供外部使用）
func (r *UserRepository) GetUserFields() string {
	return r.userFields
}

// ==================== 业务特有方法 ====================

// GetByEmail 根据邮箱获取用户
func (r *UserRepository) GetByEmail(email string) (*entities.User, error) {
	var user entities.User
	err := r.BaseRepository.First(&user, "email = ?", email)
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// GetByName 根据用户名获取用户
func (r *UserRepository) GetByName(name string) (*entities.User, error) {
	var user entities.User
	err := r.BaseRepository.First(&user, "name = ?", name)
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// GetByStatus 根据状态获取用户
func (r *UserRepository) GetByStatus(status string, offset, limit int) ([]*entities.User, error) {
	var users []*entities.User
	err := r.BaseRepository.WhereWithSelectAndPagination(&users, r.userFields, "role = ?", offset, limit, status)
	return users, err
}

// GetActiveUsers 获取活跃用户
func (r *UserRepository) GetActiveUsers(offset, limit int) ([]*entities.User, error) {
	var users []*entities.User
	err := r.BaseRepository.WhereWithSelectAndPagination(&users, r.userFields, "last_active_at > ?", offset, limit, 0)
	return users, err
}

// SearchByKeyword 根据关键词搜索用户
func (r *UserRepository) SearchByKeyword(keyword string, offset, limit int) ([]*entities.User, error) {
	var users []*entities.User
	searchFields := []string{"name", "email"}
	err := r.BaseRepository.SearchWithSelect(&users, r.userFields, searchFields, keyword, offset, limit)
	return users, err
}
