package repository

import (
	"time"

	"hkchat_api/internal/models/entities"

	"gorm.io/gorm"
)

// ConfigRepository 配置仓储
type ConfigRepository struct {
	*BaseRepository
}

// NewConfigRepository 创建配置仓储实例
func NewConfigRepository(db *gorm.DB) *ConfigRepository {
	return &ConfigRepository{
		BaseRepository: NewBaseRepository(db),
	}
}

// ==================== 业务特有方法 ====================

// GetByKey 根据键获取配置
func (r *ConfigRepository) GetByKey(key string) (*entities.Config, error) {
	var config entities.Config
	err := r.BaseRepository.First(&config, "key = ?", key)
	if err != nil {
		return nil, err
	}
	return &config, nil
}

// GetByCategory 根据分类获取配置
func (r *ConfigRepository) GetByCategory(category string, offset, limit int) ([]*entities.Config, error) {
	var configs []*entities.Config
	err := r.BaseRepository.WhereWithPagination(&configs, "category = ?", offset, limit, category)
	return configs, err
}

// SetValue 设置配置值（简化版）
func (r *ConfigRepository) SetValue(key, value string) error {
	// 简化实现，直接创建新配置
	jsonData := entities.JSON{key: value}
	config := entities.Config{
		Data:    jsonData,
		Version: 1,
	}
	return r.BaseRepository.Create(&config)
}

// GetAppConfig 获取应用配置（简化版）
func (r *ConfigRepository) GetAppConfig() (map[string]string, error) {
	var configs []entities.Config
	err := r.BaseRepository.Where(&configs, "version > ?", 0)
	if err != nil {
		return nil, err
	}

	result := make(map[string]string)
	result["app"] = "default"
	return result, nil
}

// GetUserConfig 获取用户配置（简化版）
func (r *ConfigRepository) GetUserConfig(userID string) (map[string]string, error) {
	result := make(map[string]string)
	result["user"] = userID
	return result, nil
}

// SetUserConfig 设置用户配置（简化版）
func (r *ConfigRepository) SetUserConfig(userID, key, value string) error {
	jsonData := entities.JSON{key: value, "user_id": userID}
	config := entities.Config{
		Data:    jsonData,
		Version: 1,
	}
	return r.BaseRepository.Create(&config)
}

// GetDefaultConfig 获取默认配置（简化版）
func (r *ConfigRepository) GetDefaultConfig() (map[string]string, error) {
	result := make(map[string]string)
	result["default"] = "config"
	return result, nil
}

// SearchByKeyword 根据关键词搜索配置（简化版）
func (r *ConfigRepository) SearchByKeyword(keyword string, offset, limit int) ([]*entities.Config, error) {
	var configs []*entities.Config
	err := r.BaseRepository.GetAll(&configs, offset, limit)
	return configs, err
}

// GetLatestConfig 获取最新的配置
func (r *ConfigRepository) GetLatestConfig() (*entities.Config, error) {
	var config entities.Config
	err := r.BaseRepository.GetDB().Order("id desc").First(&config).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &config, nil
}

// SaveConfig 保存配置（更新现有配置或创建新配置）
func (r *ConfigRepository) SaveConfig(config *entities.Config) error {
	now := time.Now().Unix()
	config.UpdatedAt = &now

	if config.Id == 0 {
		config.CreatedAt = now
		return r.CreateConfig(config)
	}

	err := r.BaseRepository.GetDB().Save(config).Error
	if err != nil {
		return err
	}
	return nil
}

// CreateConfig 创建新配置
func (r *ConfigRepository) CreateConfig(config *entities.Config) error {
	now := time.Now().Unix()
	config.CreatedAt = now

	err := r.BaseRepository.Create(config)
	if err != nil {
		return err
	}
	return nil
}

// GetConfigByVersion 根据版本获取配置
func (r *ConfigRepository) GetConfigByVersion(version int64) (*entities.Config, error) {
	var config entities.Config
	err := r.BaseRepository.GetDB().Where("version = ?", version).First(&config).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &config, nil
}

// GetConfigHistory 获取配置历史记录
func (r *ConfigRepository) GetConfigHistory(limit, offset int) ([]*entities.Config, error) {
	var configs []*entities.Config
	err := r.BaseRepository.GetDB().Order("id desc").Limit(limit).Offset(offset).Find(&configs).Error
	if err != nil {
		return nil, err
	}
	return configs, nil
}

// DeleteOldConfigs 删除旧配置（保留最近N个版本）
func (r *ConfigRepository) DeleteOldConfigs(keepCount int) error {
	var keepIDs []int64
	err := r.BaseRepository.GetDB().Model(&entities.Config{}).
		Select("id").
		Order("id desc").
		Limit(keepCount).
		Pluck("id", &keepIDs).Error
	if err != nil {
		return err
	}

	if len(keepIDs) == 0 {
		return nil
	}

	result := r.BaseRepository.GetDB().Where("id NOT IN ?", keepIDs).Delete(&entities.Config{})
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// GetConfigCount 获取配置记录总数
func (r *ConfigRepository) GetConfigCount() (int64, error) {
	var count int64
	err := r.BaseRepository.GetDB().Model(&entities.Config{}).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}
