# Repository 层说明

## 概述

Repository 层负责数据访问逻辑，为业务层提供统一的数据操作接口。采用接口与实现分离的设计模式，便于测试和扩展。

## 架构设计

### 基础架构

- **BaseRepository**: 提供通用的 CRUD 操作
- **BaseRepositoryInterface**: 定义基础仓储接口
- **RepositoryRegistry**: 统一管理所有仓储实例

### 设计模式

- **组合模式**: 具体 Repository 组合 BaseRepository 获得通用功能
- **接口隔离**: 为每个实体定义专门的 Repository 接口
- **依赖注入**: 通过构造函数注入数据库连接

## 目录结构

```
internal/repository/
├── interfaces/           # Repository 接口定义
│   ├── base_repository.go      # 基础仓储接口
│   ├── user_repository.go      # 用户仓储接口
│   ├── chat_repository.go      # 聊天仓储接口
│   └── message_repository.go   # 消息仓储接口
├── base_repository.go    # 基础仓储实现
├── user_repository.go    # 用户仓储实现
├── chat_repository.go    # 聊天仓储实现
├── message_repository.go # 消息仓储实现
├── registry.go          # 仓储注册器
└── README.md            # 说明文档
```

## 已实现的 Repository

### 1. UserRepository
- **GetByEmail**: 根据邮箱获取用户
- **GetByName**: 根据用户名获取用户
- **GetByStatus**: 根据状态获取用户列表
- **GetActiveUsers**: 获取活跃用户列表
- **SearchByKeyword**: 关键词搜索用户

### 2. ChatRepository
- **GetByUserID**: 根据用户ID获取聊天列表
- **GetByShareID**: 根据分享ID获取聊天
- **GetArchivedChats**: 获取已归档的聊天
- **GetPinnedChats**: 获取置顶的聊天
- **GetByFolderID**: 根据文件夹ID获取聊天
- **SearchByKeyword**: 关键词搜索聊天

### 3. MessageRepository
- **GetByUserID**: 根据用户ID获取消息列表
- **GetByChatID**: 根据聊天ID获取消息列表
- **GetByRole**: 根据角色获取消息
- **GetLatestByChatID**: 获取聊天的最新消息
- **SearchByKeyword**: 关键词搜索消息

## 使用方式

### 初始化
```go
// 创建仓储注册器
repos := repository.NewRepositoryRegistry(db)

// 使用具体的仓储
user, err := repos.User.GetByEmail("<EMAIL>")
chats, err := repos.Chat.GetByUserID("user_id", 0, 10)
messages, err := repos.Message.GetByChatID("chat_id", 0, 20)
```

### 事务操作
```go
err := repos.Transaction(func(txRepos *repository.RepositoryRegistry) error {
    // 在事务中进行多个操作
    user, err := txRepos.User.GetByID(&user, "user_id")
    if err != nil {
        return err
    }
    
    return txRepos.Chat.Create(&chat)
})
```

## 扩展说明

### 添加新的 Repository

1. **创建接口**（`interfaces/new_repository.go`）：
```go
type NewRepository interface {
    BaseRepositoryInterface
    // 业务特有方法
    CustomMethod() error
}
```

2. **创建实现**（`new_repository.go`）：
```go
type newRepository struct {
    *BaseRepository
    newFields string
}

func NewNewRepository(db *gorm.DB) NewRepository {
    return &newRepository{
        BaseRepository: NewBaseRepository(db),
        newFields:     "id, name, created_at",
    }
}
```

3. **更新注册器**（`registry.go`）：
```go
type RepositoryRegistry struct {
    // ... 现有字段
    New interfaces.NewRepository
}

func NewRepositoryRegistry(db *gorm.DB) *RepositoryRegistry {
    return &RepositoryRegistry{
        // ... 现有初始化
        New: NewNewRepository(db),
    }
}
```

## 注意事项

1. **字段选择**: 为避免 JSON 字段的性能问题，各 Repository 都定义了字段列表
2. **错误处理**: 所有方法都返回标准的 Go error，调用方需要适当处理
3. **分页**: 查询列表的方法都支持分页，使用 offset 和 limit 参数
4. **搜索**: 搜索功能使用 ILIKE 进行模糊匹配（PostgreSQL）
5. **事务**: 复杂操作应使用事务确保数据一致性

## 待扩展的 Repository

基于现有的实体类，还可以添加以下 Repository：
- DocumentRepository
- FileRepository
- FolderRepository
- NoteRepository
- PromptRepository
- ToolRepository
- ModelRepository
- FunctionRepository
- KnowledgeRepository
- FeedbackRepository
- TagRepository
- MemoryRepository
- ConfigRepository
- AuthRepository
- ChannelRepository
- ChannelMemberRepository
- MessageReactionRepository 