package repository

import (
	"reflect"

	"gorm.io/gorm"
)

// BaseRepository 基础仓储实现，提供通用的 CRUD 操作
type BaseRepository struct {
	db *gorm.DB
}

// NewBaseRepository 创建基础仓储实例
func NewBaseRepository(db *gorm.DB) *BaseRepository {
	return &BaseRepository{
		db: db,
	}
}

// Create 创建实体
func (r *BaseRepository) Create(entity interface{}) error {
	return r.db.Create(entity).Error
}

// GetByID 根据ID获取实体
func (r *BaseRepository) GetByID(result interface{}, id interface{}) error {
	// 使用明确的字段名查询，避免PostgreSQL类型推断错误
	return r.db.Where("id = ?", id).First(result).Error
}

// Update 更新实体
func (r *BaseRepository) Update(entity interface{}) error {
	return r.db.Save(entity).Error
}

// Delete 删除实体
func (r *BaseRepository) Delete(model interface{}, id interface{}) error {
	return r.db.Delete(model, id).Error
}

// GetAll 获取所有实体（分页）
func (r *BaseRepository) GetAll(result interface{}, offset, limit int) error {
	return r.db.Offset(offset).Limit(limit).Find(result).Error
}

// GetAllWithSelect 获取所有实体（分页，指定字段）
func (r *BaseRepository) GetAllWithSelect(result interface{}, fields string, offset, limit int) error {
	return r.db.Select(fields).Offset(offset).Limit(limit).Find(result).Error
}

// Count 获取总数
func (r *BaseRepository) Count(model interface{}) (int64, error) {
	var count int64
	err := r.db.Model(model).Count(&count).Error
	return count, err
}

// Where 根据条件查询
func (r *BaseRepository) Where(result interface{}, condition string, args ...interface{}) error {
	return r.db.Where(condition, args...).Find(result).Error
}

// WhereWithPagination 根据条件查询（分页）
func (r *BaseRepository) WhereWithPagination(result interface{}, condition string, offset, limit int, args ...interface{}) error {
	return r.db.Where(condition, args...).Offset(offset).Limit(limit).Find(result).Error
}

// WhereWithSelect 根据条件查询（指定字段）
func (r *BaseRepository) WhereWithSelect(result interface{}, fields, condition string, args ...interface{}) error {
	return r.db.Select(fields).Where(condition, args...).Find(result).Error
}

// WhereWithSelectAndPagination 根据条件查询（指定字段，分页）
func (r *BaseRepository) WhereWithSelectAndPagination(result interface{}, fields, condition string, offset, limit int, args ...interface{}) error {
	return r.db.Select(fields).Where(condition, args...).Offset(offset).Limit(limit).Find(result).Error
}

// First 查询第一个实体
func (r *BaseRepository) First(result interface{}, condition string, args ...interface{}) error {
	return r.db.Where(condition, args...).First(result).Error
}

// Search 模糊搜索
func (r *BaseRepository) Search(result interface{}, searchFields []string, keyword string, offset, limit int) error {
	query := r.db

	if keyword != "" && len(searchFields) > 0 {
		searchQuery := "%" + keyword + "%"

		// 构建 OR 查询条件
		orConditions := make([]string, len(searchFields))
		args := make([]interface{}, len(searchFields))

		for i, field := range searchFields {
			orConditions[i] = field + " ILIKE ?"
			args[i] = searchQuery
		}

		// 组合所有条件
		condition := ""
		for i, cond := range orConditions {
			if i > 0 {
				condition += " OR "
			}
			condition += cond
		}

		query = query.Where(condition, args...)
	}

	return query.Offset(offset).Limit(limit).Find(result).Error
}

// SearchWithSelect 模糊搜索（指定字段）
func (r *BaseRepository) SearchWithSelect(result interface{}, fields string, searchFields []string, keyword string, offset, limit int) error {
	query := r.db.Select(fields)

	if keyword != "" && len(searchFields) > 0 {
		searchQuery := "%" + keyword + "%"

		// 构建 OR 查询条件
		orConditions := make([]string, len(searchFields))
		args := make([]interface{}, len(searchFields))

		for i, field := range searchFields {
			orConditions[i] = field + " ILIKE ?"
			args[i] = searchQuery
		}

		// 组合所有条件
		condition := ""
		for i, cond := range orConditions {
			if i > 0 {
				condition += " OR "
			}
			condition += cond
		}

		query = query.Where(condition, args...)
	}

	return query.Offset(offset).Limit(limit).Find(result).Error
}

// GetDB 获取数据库实例（用于复杂查询）
func (r *BaseRepository) GetDB() *gorm.DB {
	return r.db
}

// Transaction 执行事务
func (r *BaseRepository) Transaction(fn func(*gorm.DB) error) error {
	return r.db.Transaction(fn)
}

// GetModelName 获取模型名称（用于调试）
func (r *BaseRepository) GetModelName(model interface{}) string {
	return reflect.TypeOf(model).Elem().Name()
}
