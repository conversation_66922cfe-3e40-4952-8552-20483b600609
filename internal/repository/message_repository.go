package repository

import (
	"hkchat_api/internal/models/entities"

	"gorm.io/gorm"
)

// MessageRepository 消息仓储
type MessageRepository struct {
	*BaseRepository
	messageFields string // 消息字段列表（避免复杂JSON字段的问题）
}

// NewMessageRepository 创建消息仓储实例
func NewMessageRepository(db *gorm.DB) *MessageRepository {
	return &MessageRepository{
		BaseRepository: NewBaseRepository(db),
		messageFields:  "id, chat_id, user_id, role, content, timestamp, created_at, updated_at, model",
	}
}

// GetMessageFields 获取消息字段列表（供外部使用）
func (r *MessageRepository) GetMessageFields() string {
	return r.messageFields
}

// ==================== 业务特有方法 ====================

// GetByChatID 根据聊天ID获取消息列表
func (r *MessageRepository) GetByChatID(chatID string, offset, limit int) ([]*entities.Message, error) {
	var messages []*entities.Message
	err := r.BaseRepository.WhereWithSelectAndPagination(&messages, r.messageFields, "chat_id = ?", offset, limit, chatID)
	return messages, err
}

// GetByUserID 根据用户ID获取消息列表
func (r *MessageRepository) GetByUserID(userID string, offset, limit int) ([]*entities.Message, error) {
	var messages []*entities.Message
	err := r.BaseRepository.WhereWithSelectAndPagination(&messages, r.messageFields, "user_id = ?", offset, limit, userID)
	return messages, err
}

// GetByRole 根据角色获取消息列表
func (r *MessageRepository) GetByRole(role string, offset, limit int) ([]*entities.Message, error) {
	var messages []*entities.Message
	err := r.BaseRepository.WhereWithSelectAndPagination(&messages, r.messageFields, "role = ?", offset, limit, role)
	return messages, err
}

// SearchByContent 根据内容搜索消息
func (r *MessageRepository) SearchByContent(keyword string, offset, limit int) ([]*entities.Message, error) {
	var messages []*entities.Message
	searchFields := []string{"content"}
	err := r.BaseRepository.SearchWithSelect(&messages, r.messageFields, searchFields, keyword, offset, limit)
	return messages, err
}

// GetRecentMessages 获取最近的消息
func (r *MessageRepository) GetRecentMessages(limit int) ([]*entities.Message, error) {
	var messages []*entities.Message
	err := r.BaseRepository.GetDB().Select(r.messageFields).Order("timestamp DESC").Limit(limit).Find(&messages).Error
	return messages, err
}

// GetMessagesByChatAndRole 根据聊天ID和角色获取消息
func (r *MessageRepository) GetMessagesByChatAndRole(chatID string, role string, offset, limit int) ([]*entities.Message, error) {
	var messages []*entities.Message
	err := r.BaseRepository.WhereWithSelectAndPagination(&messages, r.messageFields, "chat_id = ? AND role = ?", offset, limit, chatID, role)
	return messages, err
}
