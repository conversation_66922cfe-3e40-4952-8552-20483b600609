package repository

import (
	"hkchat_api/internal/models/entities"

	"gorm.io/gorm"
)

// DocumentRepository 文档仓储
type DocumentRepository struct {
	*BaseRepository
	documentFields string
}

// NewDocumentRepository 创建文档仓储实例
func NewDocumentRepository(db *gorm.DB) *DocumentRepository {
	return &DocumentRepository{
		BaseRepository: NewBaseRepository(db),
		documentFields: "id, name, title, content, collection_name, filename, created_at, updated_at",
	}
}

// GetDocumentFields 获取 Document 字段列表（供外部使用）
func (r *DocumentRepository) GetDocumentFields() string {
	return r.documentFields
}

// ==================== 业务特有方法 ====================

// GetByCollectionName 根据集合名称获取文档列表
func (r *DocumentRepository) GetByCollectionName(collectionName string, offset, limit int) ([]*entities.Document, error) {
	var documents []*entities.Document
	err := r.BaseRepository.WhereWithSelectAndPagination(&documents, r.documentFields, "collection_name = ?", offset, limit, collectionName)
	return documents, err
}

// GetByTitle 根据标题获取文档
func (r *DocumentRepository) GetByTitle(title string) (*entities.Document, error) {
	var entity entities.Document
	err := r.BaseRepository.First(&entity, "title = ?", title)
	if err != nil {
		return nil, err
	}
	return &entity, nil
}

// SearchByKeyword 根据关键词搜索文档
func (r *DocumentRepository) SearchByKeyword(keyword string, offset, limit int) ([]*entities.Document, error) {
	var documents []*entities.Document
	searchFields := []string{"name", "title", "content"}
	err := r.BaseRepository.SearchWithSelect(&documents, r.documentFields, searchFields, keyword, offset, limit)
	return documents, err
}
