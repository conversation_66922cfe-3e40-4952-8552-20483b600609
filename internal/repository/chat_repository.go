package repository

import (
	"hkchat_api/internal/models/entities"

	"gorm.io/gorm"
)

// ChatRepository 聊天仓储
type ChatRepository struct {
	*BaseRepository        // 组合基础仓储
	chatFields      string // 聊天字段列表（避免复杂JSON字段的问题）
}

// NewChatRepository 创建聊天仓储实例
func NewChatRepository(db *gorm.DB) *ChatRepository {
	return &ChatRepository{
		BaseRepository: NewBaseRepository(db),
		chatFields:     "id, user_id, title, chat, created_at, updated_at, share_id, archived, pinned, folder_id",
	}
}

// GetChatFields 获取聊天字段列表（供外部使用）
func (r *ChatRepository) GetChatFields() string {
	return r.chatFields
}

// ==================== 业务特有方法 ====================

// GetByUserID 根据用户ID获取聊天记录
func (r *ChatRepository) GetByUserID(userID string, offset, limit int) ([]*entities.Chat, error) {
	var chats []*entities.Chat
	err := r.BaseRepository.WhereWithSelectAndPagination(&chats, r.chatFields, "user_id = ?", offset, limit, userID)
	return chats, err
}

// GetByShareID 根据分享ID获取聊天记录
func (r *ChatRepository) GetByShareID(shareID string) (*entities.Chat, error) {
	var chat entities.Chat
	err := r.BaseRepository.First(&chat, "share_id = ?", shareID)
	if err != nil {
		return nil, err
	}
	return &chat, nil
}

// GetByTitleAndUserID 根据标题和用户ID获取聊天记录
func (r *ChatRepository) GetByTitleAndUserID(title string, userID string) (*entities.Chat, error) {
	var chat entities.Chat
	err := r.BaseRepository.First(&chat, "title = ? AND user_id = ?", title, userID)
	if err != nil {
		return nil, err
	}
	return &chat, nil
}

// GetArchivedChats 获取已归档的聊天记录
func (r *ChatRepository) GetArchivedChats(userID string, offset, limit int) ([]*entities.Chat, error) {
	var chats []*entities.Chat
	err := r.BaseRepository.WhereWithSelectAndPagination(&chats, r.chatFields, "user_id = ? AND archived = ?", offset, limit, userID, true)
	return chats, err
}

// GetPinnedChats 获取置顶的聊天记录
func (r *ChatRepository) GetPinnedChats(userID string, offset, limit int) ([]*entities.Chat, error) {
	var chats []*entities.Chat
	err := r.BaseRepository.WhereWithSelectAndPagination(&chats, r.chatFields, "user_id = ? AND pinned = ?", offset, limit, userID, true)
	return chats, err
}

// SearchByKeyword 根据关键词搜索聊天记录
func (r *ChatRepository) SearchByKeyword(keyword string, userID string, offset, limit int) ([]*entities.Chat, error) {
	var chats []*entities.Chat
	searchFields := []string{"title", "chat"}
	err := r.BaseRepository.SearchWithSelect(&chats, r.chatFields, searchFields, keyword, offset, limit)
	return chats, err
}

// GetByFolderID 根据文件夹ID获取聊天
func (r *ChatRepository) GetByFolderID(folderID string, offset, limit int) ([]*entities.Chat, error) {
	var entities []*entities.Chat
	err := r.BaseRepository.WhereWithSelectAndPagination(&entities, r.chatFields, "folder_id = ?", offset, limit, folderID)
	return entities, err
}

// GetChatHistoryByID 根据会话ID获取聊天历史
func (r *ChatRepository) GetChatHistoryByID(chatID string) (*entities.Chat, error) {
	var chat entities.Chat
	err := r.BaseRepository.First(&chat, "id = ?", chatID)
	if err != nil {
		return nil, err
	}
	return &chat, nil
}

// GetChatHistoryByUserIDAndChatID 根据用户ID和会话ID获取聊天历史
func (r *ChatRepository) GetChatHistoryByUserIDAndChatID(userID, chatID string) (*entities.Chat, error) {
	var chat entities.Chat
	err := r.BaseRepository.First(&chat, "user_id = ? AND id = ?", userID, chatID)
	if err != nil {
		return nil, err
	}
	return &chat, nil
}
