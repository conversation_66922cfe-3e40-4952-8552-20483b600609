package repository

import (
	"hkchat_api/internal/models/entities"

	"gorm.io/gorm"
)

// ChannelRepository 频道仓储
type ChannelRepository struct {
	*BaseRepository
	channelFields string
}

// NewChannelRepository 创建频道仓储实例
func NewChannelRepository(db *gorm.DB) *ChannelRepository {
	return &ChannelRepository{
		BaseRepository: NewBaseRepository(db),
		channelFields:  "id, name, description, user_id, created_at, updated_at",
	}
}

// GetChannelFields 获取频道字段列表
func (r *ChannelRepository) GetChannelFields() string {
	return r.channelFields
}

// ==================== 业务特有方法 ====================

// GetByUserID 根据用户ID获取频道列表
func (r *ChannelRepository) GetByUserID(userID string, offset, limit int) ([]*entities.Channel, error) {
	var channels []*entities.Channel
	err := r.BaseRepository.WhereWithSelectAndPagination(&channels, r.channelFields, "user_id = ?", offset, limit, userID)
	return channels, err
}

// GetByName 根据名称获取频道
func (r *ChannelRepository) GetByName(name string) (*entities.Channel, error) {
	var channel entities.Channel
	err := r.BaseRepository.First(&channel, "name = ?", name)
	if err != nil {
		return nil, err
	}
	return &channel, nil
}

// SearchByKeyword 根据关键词搜索频道
func (r *ChannelRepository) SearchByKeyword(keyword string, offset, limit int) ([]*entities.Channel, error) {
	var channels []*entities.Channel
	searchFields := []string{"name", "description"}
	err := r.BaseRepository.SearchWithSelect(&channels, r.channelFields, searchFields, keyword, offset, limit)
	return channels, err
}
