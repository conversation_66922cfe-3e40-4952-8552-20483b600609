package repository

import (
	"hkchat_api/internal/models/entities"

	"gorm.io/gorm"
)

// MemoryRepository 内存仓储
type MemoryRepository struct {
	*BaseRepository
}

// NewMemoryRepository 创建内存仓储实例
func NewMemoryRepository(db *gorm.DB) *MemoryRepository {
	return &MemoryRepository{
		BaseRepository: NewBaseRepository(db),
	}
}

// ==================== 业务特有方法 ====================

// GetByUserID 根据用户ID获取内存列表
func (r *MemoryRepository) GetByUserID(userID string, offset, limit int) ([]*entities.Memory, error) {
	var memories []*entities.Memory
	err := r.BaseRepository.WhereWithPagination(&memories, "user_id = ?", offset, limit, userID)
	return memories, err
}

// SearchByKeyword 根据关键词搜索内存
func (r *MemoryRepository) SearchByKeyword(keyword string, offset, limit int) ([]*entities.Memory, error) {
	var memories []*entities.Memory
	searchFields := []string{"content"}
	err := r.BaseRepository.SearchWithSelect(&memories, "*", searchFields, keyword, offset, limit)
	return memories, err
}
