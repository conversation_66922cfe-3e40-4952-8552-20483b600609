package repository

import (
	"hkchat_api/internal/models/entities"

	"gorm.io/gorm"
)

// AuthRepository 认证仓储
type AuthRepository struct {
	*BaseRepository
}

// NewAuthRepository 创建认证仓储实例
func NewAuthRepository(db *gorm.DB) *AuthRepository {
	return &AuthRepository{
		BaseRepository: NewBaseRepository(db),
	}
}

// ==================== 业务特有方法 ====================

// GetByUserID 根据用户ID获取认证信息
func (r *AuthRepository) GetByUserID(userID string) (*entities.Auth, error) {
	var auth entities.Auth
	err := r.BaseRepository.First(&auth, "user_id = ?", userID)
	if err != nil {
		return nil, err
	}
	return &auth, nil
}

// GetByToken 根据Token获取认证信息
func (r *AuthRepository) GetByToken(token string) (*entities.Auth, error) {
	var auth entities.Auth
	err := r.BaseRepository.First(&auth, "token = ?", token)
	if err != nil {
		return nil, err
	}
	return &auth, nil
}

// SearchByKeyword 根据关键词搜索认证信息
func (r *AuthRepository) SearchByKeyword(keyword string, offset, limit int) ([]*entities.Auth, error) {
	var auths []*entities.Auth
	searchFields := []string{"user_id", "email"}
	err := r.BaseRepository.SearchWithSelect(&auths, "*", searchFields, keyword, offset, limit)
	return auths, err
}
