package repository

import (
	"hkchat_api/internal/models/entities"

	"gorm.io/gorm"
)

// ToolRepository 工具仓储
type ToolRepository struct {
	*BaseRepository
}

// NewToolRepository 创建工具仓储实例
func NewToolRepository(db *gorm.DB) *ToolRepository {
	return &ToolRepository{
		BaseRepository: NewBaseRepository(db),
	}
}

// ==================== 业务特有方法 ====================

// GetByUserID 根据用户ID获取工具列表
func (r *ToolRepository) GetByUserID(userID string, offset, limit int) ([]*entities.Tool, error) {
	var tools []*entities.Tool
	err := r.BaseRepository.WhereWithPagination(&tools, "user_id = ?", offset, limit, userID)
	return tools, err
}

// SearchByKeyword 根据关键词搜索工具
func (r *ToolRepository) SearchByKeyword(keyword string, offset, limit int) ([]*entities.Tool, error) {
	var tools []*entities.Tool
	searchFields := []string{"name", "description"}
	err := r.BaseRepository.SearchWithSelect(&tools, "*", searchFields, keyword, offset, limit)
	return tools, err
}
