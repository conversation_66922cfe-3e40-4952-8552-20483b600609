package repository

import (
	"hkchat_api/internal/models/entities"

	"gorm.io/gorm"
)

// ModelRepository 模型仓储
type ModelRepository struct {
	*BaseRepository
}

// NewModelRepository 创建模型仓储实例
func NewModelRepository(db *gorm.DB) *ModelRepository {
	return &ModelRepository{
		BaseRepository: NewBaseRepository(db),
	}
}

// ==================== 业务特有方法 ====================

// GetByUserID 根据用户ID获取模型列表
func (r *ModelRepository) GetByUserID(userID string, offset, limit int) ([]*entities.Model, error) {
	var models []*entities.Model
	err := r.BaseRepository.WhereWithPagination(&models, "user_id = ?", offset, limit, userID)
	return models, err
}

// SearchByKeyword 根据关键词搜索模型
func (r *ModelRepository) SearchByKeyword(keyword string, offset, limit int) ([]*entities.Model, error) {
	var models []*entities.Model
	searchFields := []string{"name", "info"}
	err := r.BaseRepository.SearchWithSelect(&models, "*", searchFields, keyword, offset, limit)
	return models, err
}
