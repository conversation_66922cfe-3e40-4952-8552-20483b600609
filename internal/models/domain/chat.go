package domain

import (
	"encoding/json"
	"hkchat_api/internal/core/interfaces"
)

// ChatFeatures 功能配置领域模型
type ChatFeatures struct {
	CodeInterpreter bool
	ImageGeneration bool
	Memory          bool
	WebSearch       bool
}

// ChatMessage 聊天消息领域模型
type ChatMessage struct {
	Role    string // user, assistant, system
	Content string
}

func (m *ChatMessage) GetRole() string {
	return m.Role
}

func (m *ChatMessage) GetContent() string {
	return m.Content
}

func (m *ChatMessage) SetRole(role string) {
	m.Role = role
}

func (m *ChatMessage) SetContent(content string) {
	m.Content = content
}

// ChatFile 聊天文件领域模型
type ChatFile struct {
	ID   string
	Name string
	Type string
	URL  string
}

// ChatRequest 聊天请求领域模型（内部使用）
type ChatRequest struct {
	Model           string
	Messages        []ChatMessage
	Query           string
	Stream          bool
	Temperature     *float64
	MaxTokens       *int
	Stop            []string
	ChatID          string
	MessageID       string
	SessionID       string
	FilterIDs       []string
	ToolIDs         []string
	Files           []ChatFile
	Features        *ChatFeatures
	Variables       map[string]interface{}
	Metadata        map[string]interface{}
	FunctionCalling string
}

func (r *ChatRequest) GetModel() string {
	return r.Model
}

func (r *ChatRequest) GetMessages() []interfaces.Message {
	messages := make([]interfaces.Message, len(r.Messages))
	for i, msg := range r.Messages {
		messages[i] = &msg
	}
	return messages
}

func (r *ChatRequest) GetStream() bool {
	return r.Stream
}

func (r *ChatRequest) GetMaxTokens() *int {
	return r.MaxTokens
}

func (r *ChatRequest) GetTemperature() *float64 {
	return r.Temperature
}

func (r *ChatRequest) SetModel(model string) {
	r.Model = model
}

func (r *ChatRequest) SetMessages(messages []interfaces.Message) {
	r.Messages = make([]ChatMessage, len(messages))
	for i, msg := range messages {
		r.Messages[i] = ChatMessage{
			Role:    msg.GetRole(),
			Content: msg.GetContent(),
		}
	}
}

func (r *ChatRequest) SetStream(stream bool) {
	r.Stream = stream
}

func (r *ChatRequest) SetMaxTokens(maxTokens *int) {
	r.MaxTokens = maxTokens
}

func (r *ChatRequest) SetTemperature(temperature *float64) {
	r.Temperature = temperature
}

func (r *ChatRequest) ToJSON() string {
	data, _ := json.Marshal(r)
	return string(data)
}

// Clone 创建请求的深拷贝
func (r *ChatRequest) Clone() *ChatRequest {
	clone := &ChatRequest{
		Model:           r.Model,
		Query:           r.Query,
		Stream:          r.Stream,
		ChatID:          r.ChatID,
		MessageID:       r.MessageID,
		SessionID:       r.SessionID,
		FunctionCalling: r.FunctionCalling,
	}

	// 复制指针字段
	if r.Temperature != nil {
		temp := *r.Temperature
		clone.Temperature = &temp
	}
	if r.MaxTokens != nil {
		tokens := *r.MaxTokens
		clone.MaxTokens = &tokens
	}

	// 复制切片
	if r.Messages != nil {
		clone.Messages = make([]ChatMessage, len(r.Messages))
		copy(clone.Messages, r.Messages)
	}
	if r.Stop != nil {
		clone.Stop = make([]string, len(r.Stop))
		copy(clone.Stop, r.Stop)
	}
	if r.FilterIDs != nil {
		clone.FilterIDs = make([]string, len(r.FilterIDs))
		copy(clone.FilterIDs, r.FilterIDs)
	}
	if r.ToolIDs != nil {
		clone.ToolIDs = make([]string, len(r.ToolIDs))
		copy(clone.ToolIDs, r.ToolIDs)
	}
	if r.Files != nil {
		clone.Files = make([]ChatFile, len(r.Files))
		copy(clone.Files, r.Files)
	}

	// 复制Features
	if r.Features != nil {
		clone.Features = &ChatFeatures{
			CodeInterpreter: r.Features.CodeInterpreter,
			ImageGeneration: r.Features.ImageGeneration,
			Memory:          r.Features.Memory,
			WebSearch:       r.Features.WebSearch,
		}
	}

	// 复制map字段
	if r.Variables != nil {
		clone.Variables = make(map[string]interface{})
		for k, v := range r.Variables {
			clone.Variables[k] = v
		}
	}
	if r.Metadata != nil {
		clone.Metadata = make(map[string]interface{})
		for k, v := range r.Metadata {
			clone.Metadata[k] = v
		}
	}

	return clone
}

// HasFeature 检查是否启用了特定功能
func (r *ChatRequest) HasFeature(feature string) bool {
	if r.Features == nil {
		return false
	}

	switch feature {
	case "web_search":
		return r.Features.WebSearch
	case "code_interpreter":
		return r.Features.CodeInterpreter
	case "image_generation":
		return r.Features.ImageGeneration
	case "memory":
		return r.Features.Memory
	default:
		return false
	}
}

// AddSystemMessage 添加系统消息到消息列表开头
func (r *ChatRequest) AddSystemMessage(content string) {
	systemMsg := ChatMessage{
		Role:    "system",
		Content: content,
	}
	r.Messages = append([]ChatMessage{systemMsg}, r.Messages...)
}
