package domain

import (
	"encoding/json"
	"hkchat_api/pkg/prompts"
)

// PromptConfig 提示词配置
type PromptConfig struct {
	TaskModel                            string `json:"task_model"`
	TaskModelExternal                    string `json:"task_model_external"`
	EnableTitleGeneration                bool   `json:"enable_title_generation"`
	TitleGenerationPromptTemplate        string `json:"title_generation_prompt_template"`
	ImagePromptGenerationPromptTemplate  string `json:"image_prompt_generation_prompt_template"`
	EnableAutocompleteGeneration         bool   `json:"enable_autocomplete_generation"`
	AutocompleteGenerationInputMaxLength int    `json:"autocomplete_generation_input_max_length"`
	TagsGenerationPromptTemplate         string `json:"tags_generation_prompt_template"`
	FollowUpGenerationPromptTemplate     string `json:"follow_up_generation_prompt_template"`
	EnableFollowUpGeneration             bool   `json:"enable_follow_up_generation"`
	EnableTagsGeneration                 bool   `json:"enable_tags_generation"`
	EnableSearchQueryGeneration          bool   `json:"enable_search_query_generation"`
	EnableRetrievalQueryGeneration       bool   `json:"enable_retrieval_query_generation"`
	QueryGenerationPromptTemplate        string `json:"query_generation_prompt_template"`
	ToolsFunctionCallingPromptTemplate   string `json:"tools_function_calling_prompt_template"`
}

// NewDefaultPromptConfig 创建默认提示词配置
func NewDefaultPromptConfig() *PromptConfig {
	return &PromptConfig{
		TaskModel:                            "gpt-3.5-turbo",
		TaskModelExternal:                    "",
		EnableTitleGeneration:                true,
		TitleGenerationPromptTemplate:        "根据对话内容生成一个简洁的标题",
		ImagePromptGenerationPromptTemplate:  "根据用户描述生成图片提示词",
		EnableAutocompleteGeneration:         true,
		AutocompleteGenerationInputMaxLength: 200,
		TagsGenerationPromptTemplate:         "根据对话内容生成相关标签",
		FollowUpGenerationPromptTemplate:     "根据对话内容生成后续问题建议",
		EnableFollowUpGeneration:             true,
		EnableTagsGeneration:                 true,
		EnableSearchQueryGeneration:          true,
		EnableRetrievalQueryGeneration:       true,
		QueryGenerationPromptTemplate:        "根据用户问题生成搜索查询",
		ToolsFunctionCallingPromptTemplate:   prompts.GetToolsFunctionCallingTemplate(),
	}
}

// ToJSON 转换为JSON字符串
func (pc *PromptConfig) ToJSON() (string, error) {
	data, err := json.Marshal(pc)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// FromJSON 从JSON字符串解析
func (pc *PromptConfig) FromJSON(jsonStr string) error {
	return json.Unmarshal([]byte(jsonStr), pc)
}

// GetToolsFunctionCallingTemplate 获取工具函数调用模板
func (pc *PromptConfig) GetToolsFunctionCallingTemplate() string {
	if pc.ToolsFunctionCallingPromptTemplate != "" {
		return pc.ToolsFunctionCallingPromptTemplate
	}
	return prompts.GetToolsFunctionCallingTemplate()
}

// GetTitleGenerationTemplate 获取标题生成模板
func (pc *PromptConfig) GetTitleGenerationTemplate() string {
	if pc.TitleGenerationPromptTemplate != "" {
		return pc.TitleGenerationPromptTemplate
	}
	return "根据对话内容生成一个简洁的标题"
}

// GetQueryGenerationTemplate 获取查询生成模板
func (pc *PromptConfig) GetQueryGenerationTemplate() string {
	if pc.QueryGenerationPromptTemplate != "" {
		return pc.QueryGenerationPromptTemplate
	}
	return "根据用户问题生成搜索查询"
}

// UpdateFromRequest 从请求更新配置
func (pc *PromptConfig) UpdateFromRequest(req map[string]interface{}) {
	if val, ok := req["TASK_MODEL"].(string); ok && val != "" {
		pc.TaskModel = val
	}
	if val, ok := req["TASK_MODEL_EXTERNAL"].(string); ok {
		pc.TaskModelExternal = val
	}
	if val, ok := req["ENABLE_TITLE_GENERATION"].(bool); ok {
		pc.EnableTitleGeneration = val
	}
	if val, ok := req["TITLE_GENERATION_PROMPT_TEMPLATE"].(string); ok {
		pc.TitleGenerationPromptTemplate = val
	}
	if val, ok := req["IMAGE_PROMPT_GENERATION_PROMPT_TEMPLATE"].(string); ok {
		pc.ImagePromptGenerationPromptTemplate = val
	}
	if val, ok := req["ENABLE_AUTOCOMPLETE_GENERATION"].(bool); ok {
		pc.EnableAutocompleteGeneration = val
	}
	if val, ok := req["AUTOCOMPLETE_GENERATION_INPUT_MAX_LENGTH"].(float64); ok {
		pc.AutocompleteGenerationInputMaxLength = int(val)
	}
	if val, ok := req["TAGS_GENERATION_PROMPT_TEMPLATE"].(string); ok {
		pc.TagsGenerationPromptTemplate = val
	}
	if val, ok := req["FOLLOW_UP_GENERATION_PROMPT_TEMPLATE"].(string); ok {
		pc.FollowUpGenerationPromptTemplate = val
	}
	if val, ok := req["ENABLE_FOLLOW_UP_GENERATION"].(bool); ok {
		pc.EnableFollowUpGeneration = val
	}
	if val, ok := req["ENABLE_TAGS_GENERATION"].(bool); ok {
		pc.EnableTagsGeneration = val
	}
	if val, ok := req["ENABLE_SEARCH_QUERY_GENERATION"].(bool); ok {
		pc.EnableSearchQueryGeneration = val
	}
	if val, ok := req["ENABLE_RETRIEVAL_QUERY_GENERATION"].(bool); ok {
		pc.EnableRetrievalQueryGeneration = val
	}
	if val, ok := req["QUERY_GENERATION_PROMPT_TEMPLATE"].(string); ok {
		pc.QueryGenerationPromptTemplate = val
	}
	if val, ok := req["TOOLS_FUNCTION_CALLING_PROMPT_TEMPLATE"].(string); ok {
		pc.ToolsFunctionCallingPromptTemplate = val
	}
}
