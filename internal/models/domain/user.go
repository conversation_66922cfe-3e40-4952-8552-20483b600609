package domain

import (
	"errors"
	"regexp"
	"strings"
	"time"
)

// UserRole 用户角色枚举
type UserRole string

const (
	RolePending UserRole = "pending"
	RoleUser    UserRole = "user"
	RoleAdmin   UserRole = "admin"
)

// IsValid 检查角色是否有效
func (r UserRole) IsValid() bool {
	return r == RolePending || r == RoleUser || r == RoleAdmin
}

// UserSettings 用户设置结构体
type UserSettings struct {
	Theme           string            `json:"theme"`            // 主题设置
	Language        string            `json:"language"`         // 语言设置
	Notifications   NotificationPrefs `json:"notifications"`    // 通知偏好
	Privacy         PrivacySettings   `json:"privacy"`          // 隐私设置
	ChatPreferences ChatPreferences   `json:"chat_preferences"` // 聊天偏好
}

// NotificationPrefs 通知偏好设置
type NotificationPrefs struct {
	Email   bool `json:"email"`   // 邮件通知
	Desktop bool `json:"desktop"` // 桌面通知
	Mobile  bool `json:"mobile"`  // 移动端通知
	Sound   bool `json:"sound"`   // 声音通知
}

// PrivacySettings 隐私设置
type PrivacySettings struct {
	ShowEmail      bool `json:"show_email"`       // 显示邮箱
	ShowLastActive bool `json:"show_last_active"` // 显示最后活动时间
	AllowDirectMsg bool `json:"allow_direct_msg"` // 允许私信
}

// ChatPreferences 聊天偏好设置
type ChatPreferences struct {
	DefaultModel   string `json:"default_model"`   // 默认AI模型
	MessageHistory int    `json:"message_history"` // 消息历史长度
	AutoSave       bool   `json:"auto_save"`       // 自动保存
	ShowTimestamps bool   `json:"show_timestamps"` // 显示时间戳
}

// UserInfo 用户扩展信息
type UserInfo struct {
	Bio         string            `json:"bio"`          // 个人简介
	Location    string            `json:"location"`     // 位置
	Website     string            `json:"website"`      // 个人网站
	SocialLinks map[string]string `json:"social_links"` // 社交链接
	Preferences UserPreferences   `json:"preferences"`  // 个人偏好
	Stats       UserStats         `json:"stats"`        // 用户统计
}

// UserPreferences 用户个人偏好
type UserPreferences struct {
	Timezone   string   `json:"timezone"`    // 时区
	DateFormat string   `json:"date_format"` // 日期格式
	TimeFormat string   `json:"time_format"` // 时间格式
	Interests  []string `json:"interests"`   // 兴趣爱好
	Skills     []string `json:"skills"`      // 技能标签
}

// UserStats 用户统计信息
type UserStats struct {
	TotalChats    int `json:"total_chats"`    // 总聊天数
	TotalMessages int `json:"total_messages"` // 总消息数
	JoinedDays    int `json:"joined_days"`    // 加入天数
}

// User 用户业务领域模型 - 表示业务概念和规则
type User struct {
	// 标识信息
	ID        string    `gorm:"primaryKey;type:varchar(255)" json:"id"`
	CreatedAt time.Time `gorm:"type:timestamp;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt time.Time `gorm:"type:timestamp;default:CURRENT_TIMESTAMP" json:"updated_at"`

	// 基本信息
	Name            string   `gorm:"type:varchar(255);not null" json:"name"`
	Email           string   `gorm:"type:varchar(255);uniqueIndex;not null" json:"email"`
	Role            UserRole `gorm:"type:varchar(50);not null;default:'user'" json:"role"`
	ProfileImageURL string   `gorm:"type:text" json:"profile_image_url"`

	// 活动状态
	LastActiveAt time.Time `gorm:"type:timestamp;default:CURRENT_TIMESTAMP" json:"last_active_at"`
	IsActive     bool      `gorm:"type:boolean;default:true" json:"is_active"` // 业务状态计算字段

	// 业务配置 - 使用JSON类型存储复杂结构
	Settings UserSettings `gorm:"type:json" json:"settings"`
	Info     UserInfo     `gorm:"type:json" json:"info"`

	// 业务状态
	HasAPIKey       bool `gorm:"type:boolean;default:false" json:"has_api_key"`      // 是否有API密钥
	IsOAuthUser     bool `gorm:"type:boolean;default:false" json:"is_oauth_user"`    // 是否OAuth用户
	ProfileComplete bool `gorm:"type:boolean;default:false" json:"profile_complete"` // 个人资料是否完整
}

// TableName 指定表名
func (User) TableName() string {
	return "users"
}

// 业务规则验证

var (
	emailRegex = regexp.MustCompile(`^[a-z0-9._%+\-]+@[a-z0-9.\-]+\.[a-z]{2,}$`)
	nameRegex  = regexp.MustCompile(`^[a-zA-Z0-9\p{Han}\s\-_.]{2,50}$`)
)

// ValidateForCreation 验证用户创建时的业务规则
func (u *User) ValidateForCreation() error {
	if err := u.validateName(); err != nil {
		return err
	}
	if err := u.validateEmail(); err != nil {
		return err
	}
	if !u.Role.IsValid() {
		return errors.New("无效的用户角色")
	}
	return nil
}

// ValidateForUpdate 验证用户更新时的业务规则
func (u *User) ValidateForUpdate() error {
	if u.Name != "" {
		if err := u.validateName(); err != nil {
			return err
		}
	}
	if u.Email != "" {
		if err := u.validateEmail(); err != nil {
			return err
		}
	}
	if u.Role != "" && !u.Role.IsValid() {
		return errors.New("无效的用户角色")
	}
	return nil
}

// validateName 验证用户名
func (u *User) validateName() error {
	if len(strings.TrimSpace(u.Name)) < 2 {
		return errors.New("用户名长度不能少于2个字符")
	}
	if len(u.Name) > 50 {
		return errors.New("用户名长度不能超过50个字符")
	}
	if !nameRegex.MatchString(u.Name) {
		return errors.New("用户名包含无效字符")
	}
	return nil
}

// validateEmail 验证邮箱格式
func (u *User) validateEmail() error {
	email := strings.ToLower(strings.TrimSpace(u.Email))
	if !emailRegex.MatchString(email) {
		return errors.New("邮箱格式无效")
	}
	return nil
}

// 业务方法

// IsOnline 判断用户是否在线（业务逻辑）
func (u *User) IsOnline() bool {
	// 5分钟内有活动认为在线
	return time.Since(u.LastActiveAt) < 5*time.Minute
}

// CanPerformAdminActions 检查是否能执行管理员操作
func (u *User) CanPerformAdminActions() bool {
	return u.Role == RoleAdmin
}

// CanAccessChat 检查是否能访问聊天功能
func (u *User) CanAccessChat() bool {
	return u.Role == RoleUser || u.Role == RoleAdmin
}

// GetDisplayName 获取显示名称
func (u *User) GetDisplayName() string {
	if u.Name != "" {
		return u.Name
	}
	return strings.Split(u.Email, "@")[0] // 使用邮箱前缀作为默认显示名
}

// UpdateActivity 更新用户活动时间
func (u *User) UpdateActivity() {
	u.LastActiveAt = time.Now()
	u.IsActive = true
}

// CheckProfileCompleteness 检查个人资料完整性
func (u *User) CheckProfileCompleteness() {
	u.ProfileComplete = u.Name != "" &&
		u.Email != "" &&
		u.Info.Bio != "" &&
		u.ProfileImageURL != ""
}

// SetDefaultSettings 设置默认用户设置
func (u *User) SetDefaultSettings() {
	u.Settings = UserSettings{
		Theme:    "light",
		Language: "zh-CN",
		Notifications: NotificationPrefs{
			Email:   true,
			Desktop: true,
			Mobile:  true,
			Sound:   false,
		},
		Privacy: PrivacySettings{
			ShowEmail:      false,
			ShowLastActive: true,
			AllowDirectMsg: true,
		},
		ChatPreferences: ChatPreferences{
			DefaultModel:   "gpt-3.5-turbo",
			MessageHistory: 50,
			AutoSave:       true,
			ShowTimestamps: true,
		},
	}
}

// SetDefaultInfo 设置默认用户信息
func (u *User) SetDefaultInfo() {
	u.Info = UserInfo{
		SocialLinks: make(map[string]string),
		Preferences: UserPreferences{
			Timezone:   "Asia/Shanghai",
			DateFormat: "YYYY-MM-DD",
			TimeFormat: "24h",
			Interests:  []string{},
			Skills:     []string{},
		},
		Stats: UserStats{
			TotalChats:    0,
			TotalMessages: 0,
			JoinedDays:    0,
		},
	}
}

// NewUser 创建新用户
func NewUser(name, email, role string) *User {
	now := time.Now()
	return &User{
		Name:      name,
		Email:     email,
		Role:      UserRole(role),
		IsActive:  true,
		CreatedAt: now,
		UpdatedAt: now,
	}
}

// UpdateProfile 更新用户资料
func (u *User) UpdateProfile(name, profileImageURL string) {
	u.Name = name
	u.ProfileImageURL = profileImageURL
	u.UpdatedAt = time.Now()
}

// ChangeRole 变更用户角色
func (u *User) ChangeRole(newRole string) error {
	validRoles := map[string]bool{
		"admin": true,
		"user":  true,
		"guest": true,
	}

	if !validRoles[newRole] {
		return errors.New("无效的用户角色")
	}

	u.Role = UserRole(newRole)
	u.UpdatedAt = time.Now()
	return nil
}

// Activate 激活用户
func (u *User) Activate() {
	u.IsActive = true
	u.UpdatedAt = time.Now()
}

// Deactivate 停用用户
func (u *User) Deactivate() {
	u.IsActive = false
	u.UpdatedAt = time.Now()
}

// IsAdmin 检查是否为管理员
func (u *User) IsAdmin() bool {
	return u.Role == RoleAdmin
}

// Validate 验证用户数据
func (u *User) Validate() error {
	if u.Name == "" {
		return errors.New("用户名不能为空")
	}

	if u.Email == "" {
		return errors.New("邮箱不能为空")
	}

	if u.Role == "" {
		return errors.New("角色不能为空")
	}

	return nil
}

// DomainError 领域错误
type DomainError struct {
	Message string
}

func (e *DomainError) Error() string {
	return e.Message
}

// NewDomainError 创建领域错误
func NewDomainError(message string) *DomainError {
	return &DomainError{Message: message}
}

// ========== 接口实现方法 ==========

// GetID 获取用户ID
func (u *User) GetID() string {
	return u.ID
}

// GetName 获取用户名
func (u *User) GetName() string {
	return u.Name
}

// GetEmail 获取用户邮箱
func (u *User) GetEmail() string {
	return u.Email
}

// GetRole 获取用户角色
func (u *User) GetRole() string {
	return string(u.Role)
}

// GetLastActiveAt 获取最后活跃时间
func (u *User) GetLastActiveAt() interface{} {
	return u.LastActiveAt
}
