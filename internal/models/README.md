# 数据库模型结构说明

## 📁 文件结构

```
models/
├── types.go              # 公共类型定义 (JSON)
├── base.go               # 基础模型定义
├── user.go               # 用户模型
├── auth.go               # 认证模型
├── group.go              # 用户组模型
├── chat.go               # 聊天模型
├── message.go            # 消息模型
├── message_reaction.go   # 消息反应模型
├── function.go           # 函数模型
├── model.go              # AI模型配置
├── file.go               # 文件模型
├── models.go             # 模型注册
└── README.md             # 本文档
```

## 🏗️ 基础模型类型

### BaseModel
包含：`ID`, `CreatedAt`, `UpdatedAt`
- 适用于需要更新时间的表
- 使用秒级时间戳

### BaseModelWithoutUpdate
包含：`ID`, `CreatedAt`
- 适用于不需要更新时间的表（如文件记录）
- 使用秒级时间戳

### BaseModelNano
包含：`ID`, `CreatedAt`, `UpdatedAt`
- 适用于需要高精度时间的表（如消息）
- 使用纳秒级时间戳

### BaseModelNanoWithoutUpdate
包含：`ID`, `CreatedAt`
- 适用于不需要更新时间但需要高精度时间的表（如消息反应）
- 使用纳秒级时间戳

## 📊 模型映射关系

| 模型 | 文件 | 基础类型 | 表名 | 说明 |
|------|------|----------|------|------|
| `User` | user.go | BaseModel | user | 用户信息表，含特殊的 LastActiveAt |
| `Auth` | auth.go | BaseModel | auth | 用户认证表 |
| `Group` | group.go | BaseModel | group | 用户组表 |
| `Chat` | chat.go | BaseModel | chat | 聊天会话表 |
| `Message` | message.go | BaseModelNano | message | 消息表（纳秒时间戳） |
| `MessageReaction` | message_reaction.go | BaseModelNanoWithoutUpdate | message_reaction | 消息反应表（仅创建时间） |
| `Function` | function.go | BaseModel | function | 自定义函数表 |
| `Model` | model.go | BaseModel | model | AI模型配置表 |
| `File` | file.go | BaseModelWithoutUpdate | file | 文件信息表（仅创建时间） |

## ✨ 主要特性

### 🔧 自动功能
- **时间戳管理**：通过 GORM 钩子自动设置创建和更新时间
- **表名映射**：每个模型都有 `TableName()` 方法指定表名
- **JSON 支持**：自定义 `JSON` 类型处理 PostgreSQL 的 JSONB 字段

### 🎯 代码复用
- **基础模型**：公共字段（ID、时间戳）抽取到基础结构体
- **钩子函数**：统一的时间戳设置逻辑
- **类型定义**：JSON 类型复用于所有模型

### 📝 使用示例

```go
// 创建用户
user := models.User{
    Name:  "张三",
    Email: "<EMAIL>",
    Role:  "user",
}
// BaseModel 会自动设置 ID、CreatedAt、UpdatedAt、LastActiveAt

// 创建消息
message := models.Message{
    UserID:  "user123",
    Content: "Hello World",
}
// BaseModelNano 会自动设置纳秒级时间戳

// 自动迁移
config.AutoMigrate(models.GetAllModels()...)
```

## 🚀 优势

1. **代码简洁**：减少重复的字段定义和钩子函数
2. **易于维护**：统一的时间戳管理逻辑
3. **类型安全**：通过嵌入结构体确保字段一致性
4. **灵活扩展**：可根据需要选择不同的基础模型类型 