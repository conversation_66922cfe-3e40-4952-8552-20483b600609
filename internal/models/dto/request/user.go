package request

// CreateUserRequest 创建用户请求DTO
type CreateUserRequest struct {
	Name            string `json:"name" binding:"required,min=2,max=50" example:"张三"`
	Email           string `json:"email" binding:"required,email" example:"<EMAIL>"`
	Role            string `json:"role,omitempty" binding:"omitempty,oneof=pending user admin" example:"user"`
	ProfileImageURL string `json:"profile_image_url,omitempty" binding:"omitempty,url" example:"https://example.com/avatar.jpg"`
}

// UpdateUserRequest 更新用户请求DTO
type UpdateUserRequest struct {
	Name            *string `json:"name,omitempty" binding:"omitempty,min=2,max=50" example:"李四"`
	Role            *string `json:"role,omitempty" binding:"omitempty,oneof=pending user admin" example:"admin"`
	ProfileImageURL *string `json:"profile_image_url,omitempty" binding:"omitempty,url" example:"https://example.com/new-avatar.jpg"`
}

// UpdateUserSettingsRequest 更新用户设置请求DTO
type UpdateUserSettingsRequest struct {
	Theme           *string                   `json:"theme,omitempty" binding:"omitempty,oneof=light dark auto" example:"dark"`
	Language        *string                   `json:"language,omitempty" binding:"omitempty,oneof=zh-CN en-US ja-JP" example:"en-US"`
	Notifications   *NotificationPrefsRequest `json:"notifications,omitempty"`
	Privacy         *PrivacySettingsRequest   `json:"privacy,omitempty"`
	ChatPreferences *ChatPreferencesRequest   `json:"chat_preferences,omitempty"`
}

// NotificationPrefsRequest 通知偏好请求DTO
type NotificationPrefsRequest struct {
	Email   *bool `json:"email,omitempty" example:"true"`
	Desktop *bool `json:"desktop,omitempty" example:"false"`
	Mobile  *bool `json:"mobile,omitempty" example:"true"`
	Sound   *bool `json:"sound,omitempty" example:"false"`
}

// PrivacySettingsRequest 隐私设置请求DTO
type PrivacySettingsRequest struct {
	ShowEmail      *bool `json:"show_email,omitempty" example:"false"`
	ShowLastActive *bool `json:"show_last_active,omitempty" example:"true"`
	AllowDirectMsg *bool `json:"allow_direct_msg,omitempty" example:"true"`
}

// ChatPreferencesRequest 聊天偏好请求DTO
type ChatPreferencesRequest struct {
	DefaultModel   *string `json:"default_model,omitempty" binding:"omitempty,oneof=gpt-3.5-turbo gpt-4 claude-3" example:"gpt-4"`
	MessageHistory *int    `json:"message_history,omitempty" binding:"omitempty,min=10,max=1000" example:"100"`
	AutoSave       *bool   `json:"auto_save,omitempty" example:"true"`
	ShowTimestamps *bool   `json:"show_timestamps,omitempty" example:"true"`
}

// UpdateUserInfoRequest 更新用户信息请求DTO
type UpdateUserInfoRequest struct {
	Bio         *string                 `json:"bio,omitempty" binding:"omitempty,max=500" example:"这是我的个人简介"`
	Location    *string                 `json:"location,omitempty" binding:"omitempty,max=100" example:"北京"`
	Website     *string                 `json:"website,omitempty" binding:"omitempty,url" example:"https://example.com"`
	SocialLinks *map[string]string      `json:"social_links,omitempty"`
	Preferences *UserPreferencesRequest `json:"preferences,omitempty"`
}

// UserPreferencesRequest 用户偏好请求DTO
type UserPreferencesRequest struct {
	Timezone   *string   `json:"timezone,omitempty" example:"Asia/Shanghai"`
	DateFormat *string   `json:"date_format,omitempty" binding:"omitempty,oneof=YYYY-MM-DD MM/DD/YYYY DD/MM/YYYY" example:"YYYY-MM-DD"`
	TimeFormat *string   `json:"time_format,omitempty" binding:"omitempty,oneof=12h 24h" example:"24h"`
	Interests  *[]string `json:"interests,omitempty" example:"编程,音乐,旅行"`
	Skills     *[]string `json:"skills,omitempty" example:"Go,Python,JavaScript"`
}

// ChangePasswordRequest 修改密码请求DTO
type ChangePasswordRequest struct {
	CurrentPassword string `json:"current_password" binding:"required" example:"current123"`
	NewPassword     string `json:"new_password" binding:"required,min=8" example:"newpassword123"`
	ConfirmPassword string `json:"confirm_password" binding:"required,eqfield=NewPassword" example:"newpassword123"`
}

// GenerateAPIKeyRequest 生成API密钥请求DTO
type GenerateAPIKeyRequest struct {
	Name        string `json:"name" binding:"required,max=100" example:"我的API密钥"`
	Description string `json:"description,omitempty" binding:"omitempty,max=255" example:"用于自动化脚本"`
	ExpiresAt   *int64 `json:"expires_at,omitempty" example:"1735689600"` // Unix 时间戳
}

// UpdateAPIKeyRequest 更新API密钥请求DTO
type UpdateAPIKeyRequest struct {
	Name        *string `json:"name,omitempty" binding:"omitempty,max=100" example:"更新后的密钥名称"`
	Description *string `json:"description,omitempty" binding:"omitempty,max=255" example:"更新后的描述"`
	IsActive    *bool   `json:"is_active,omitempty" example:"false"`
}

// BatchDeleteUsersRequest 批量删除用户请求DTO
type BatchDeleteUsersRequest struct {
	UserIDs []string `json:"user_ids" binding:"required,min=1" example:"user1,user2,user3"`
	Force   bool     `json:"force,omitempty" example:"false"` // 是否强制删除
}

// SearchUsersRequest 搜索用户请求DTO
type SearchUsersRequest struct {
	Query    string `json:"query,omitempty" binding:"omitempty,min=1" example:"张三"`
	Role     string `json:"role,omitempty" binding:"omitempty,oneof=pending user admin" example:"user"`
	IsActive *bool  `json:"is_active,omitempty" example:"true"`
	Page     int    `json:"page,omitempty" binding:"omitempty,min=1" example:"1"`
	PageSize int    `json:"page_size,omitempty" binding:"omitempty,min=1,max=100" example:"20"`
	SortBy   string `json:"sort_by,omitempty" binding:"omitempty,oneof=name email created_at last_active_at" example:"created_at"`
	SortDir  string `json:"sort_dir,omitempty" binding:"omitempty,oneof=asc desc" example:"desc"`
}

// ImportUsersRequest 导入用户请求DTO
type ImportUsersRequest struct {
	Users []CreateUserRequest `json:"users" binding:"required,min=1,dive"`
}

// ExportUsersRequest 导出用户请求DTO
type ExportUsersRequest struct {
	UserIDs []string `json:"user_ids,omitempty" example:"user1,user2"` // 空则导出所有
	Format  string   `json:"format" binding:"required,oneof=csv json xlsx" example:"csv"`
	Fields  []string `json:"fields,omitempty" example:"name,email,role,created_at"` // 空则导出所有字段
}
