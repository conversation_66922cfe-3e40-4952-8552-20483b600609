package request

import (
	"encoding/json"
	"hkchat_api/pkg/clients/llm"
)

// Features 功能配置结构体
type Features struct {
	CodeInterpreter bool `json:"code_interpreter,omitempty"`
	ImageGeneration bool `json:"image_generation,omitempty"`
	Memory          bool `json:"memory,omitempty"`
	WebSearch       bool `json:"web_search,omitempty"`
}

// ChatCompletionRequest 聊天完成请求（简化版）
type ChatCompletionRequest struct {
	Model    string    `json:"model" binding:"required"`
	Query    string    `json:"query" binding:"required"`
	ChatID   string    `json:"chat_id,omitempty"`
	Features *Features `json:"features,omitempty"`
}

// ChatMessage 聊天消息
type ChatMessage struct {
	Role    string `json:"role" binding:"required"` // user, assistant, system
	Content string `json:"content" binding:"required"`
}

// ChatFile 聊天文件
type ChatFile struct {
	ID   string `json:"id"`
	Name string `json:"name"`
	Type string `json:"type"`
	URL  string `json:"url,omitempty"`
}

// ToOpenAI 转换为OpenAI格式（简化版）
func (r *ChatCompletionRequest) ToOpenAI() *llm.ChatRequest {
	// 为简化的DTO创建基本的OpenAI请求
	openaiReq := &llm.ChatRequest{
		Model:  r.Model,
		Stream: false, // 默认不使用流式
		Messages: []llm.ChatMessage{
			{
				Role:    "user",
				Content: r.Query,
			},
		},
	}

	return openaiReq
}

// ChatCompletedRequest 聊天完成后的请求
type ChatCompletedRequest struct {
	Model     string                 `json:"model" binding:"required"`
	ChatID    string                 `json:"chat_id" binding:"required"`
	MessageID string                 `json:"id" binding:"required"`
	SessionID string                 `json:"session_id" binding:"required"`
	Response  string                 `json:"response,omitempty"`
	FilterIDs []string               `json:"filter_ids,omitempty"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
	ModelItem *ModelItem             `json:"model_item,omitempty"`
}

// ModelItem 模型项
type ModelItem struct {
	ID       string                 `json:"id"`
	Name     string                 `json:"name"`
	Provider string                 `json:"provider"`
	Direct   bool                   `json:"direct,omitempty"`
	Config   map[string]interface{} `json:"config,omitempty"`
}

// CreateChatRequest 创建聊天请求
type CreateChatRequest struct {
	Title     string `json:"title" binding:"required"`
	ModelID   string `json:"model_id" binding:"required"`
	SessionID string `json:"session_id,omitempty"`
}

// SendMessageRequest 发送消息请求
type SendMessageRequest struct {
	ChatID    uint   `json:"chat_id" binding:"required"`
	MessageID string `json:"message_id,omitempty"`
	Role      string `json:"role" binding:"required"`
	Content   string `json:"content" binding:"required"`
	Type      string `json:"type,omitempty"`
}

// UpdateChatRequest 更新聊天请求
type UpdateChatRequest struct {
	Title  string `json:"title,omitempty"`
	Status string `json:"status,omitempty"`
}

// ListChatsRequest 列出聊天请求
type ListChatsRequest struct {
	UserID    uint   `form:"user_id,omitempty"`
	SessionID string `form:"session_id,omitempty"`
	Status    string `form:"status,omitempty"`
	Page      int    `form:"page,default=1"`
	PageSize  int    `form:"page_size,default=20"`
}
type ChatCompletionV2Request struct {
	Query          string `json:"query" binding:"required"`
	ModelID        string `json:"model_id" binding:"required"`
	ConversationID string `json:"conversation_id,omitempty"`
}

// ToJSON 转换为 JSON 字符串
func (r *ChatCompletionRequest) ToJSON() string {
	data, _ := json.Marshal(r)
	return string(data)
}
