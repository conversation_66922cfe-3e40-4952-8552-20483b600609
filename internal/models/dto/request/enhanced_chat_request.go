package request

// EnhancedChatCompletionRequest 增强版聊天完成请求（支持更多高级参数）
type EnhancedChatCompletionRequest struct {
	// 基础参数
	Model       string        `json:"model" binding:"required"`
	Messages    []ChatMessage `json:"messages" binding:"required"`
	Stream      bool          `json:"stream,omitempty"`
	Temperature *float64      `json:"temperature,omitempty"`
	MaxTokens   *int          `json:"max_tokens,omitempty"`
	Stop        []string      `json:"stop,omitempty"`

	// 会话管理
	ChatID    string `json:"chat_id,omitempty"`
	MessageID string `json:"id,omitempty"`
	SessionID string `json:"session_id,omitempty"`

	// 功能扩展
	FilterIDs []string               `json:"filter_ids,omitempty"`
	ToolIDs   []string               `json:"tool_ids,omitempty"`
	Files     []ChatFile             `json:"files,omitempty"`
	Features  map[string]interface{} `json:"features,omitempty"`
	Variables map[string]interface{} `json:"variables,omitempty"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`

	// 新增高级参数
	Params          map[string]interface{} `json:"params,omitempty"`           // 额外模型参数
	ToolServers     []ToolServer           `json:"tool_servers,omitempty"`     // 工具服务器配置
	ModelItem       *EnhancedModelItem     `json:"model_item,omitempty"`       // 详细模型配置
	BackgroundTasks *BackgroundTasks       `json:"background_tasks,omitempty"` // 后台任务配置
}

// ToolServer 工具服务器配置
type ToolServer struct {
	URL     string                 `json:"url" binding:"required"`
	OpenAPI map[string]interface{} `json:"openapi,omitempty"`
	Info    map[string]interface{} `json:"info,omitempty"`
	Specs   []ToolSpec             `json:"specs,omitempty"`
}

// ToolSpec 工具规范
type ToolSpec struct {
	Type        string                 `json:"type"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Parameters  map[string]interface{} `json:"parameters"`
}

// EnhancedModelItem 增强版模型项配置
type EnhancedModelItem struct {
	ID             string                 `json:"id"`
	Object         string                 `json:"object,omitempty"`
	Created        int64                  `json:"created,omitempty"`
	OwnedBy        string                 `json:"owned_by,omitempty"`
	Permission     interface{}            `json:"permission,omitempty"`
	Root           string                 `json:"root,omitempty"`
	Parent         string                 `json:"parent,omitempty"`
	ConnectionType string                 `json:"connection_type,omitempty"`
	Name           string                 `json:"name,omitempty"`
	OpenAI         map[string]interface{} `json:"openai,omitempty"`
	URLIdx         int                    `json:"urlIdx,omitempty"`
	Info           *ModelInfo             `json:"info,omitempty"`
	Actions        []interface{}          `json:"actions,omitempty"`
	Filters        []interface{}          `json:"filters,omitempty"`
	Tags           []string               `json:"tags,omitempty"`
}

// ModelInfo 模型详细信息
type ModelInfo struct {
	ID            string                 `json:"id"`
	UserID        string                 `json:"user_id"`
	BaseModelID   string                 `json:"base_model_id,omitempty"`
	Name          string                 `json:"name"`
	Params        map[string]interface{} `json:"params,omitempty"`
	Meta          *ModelMeta             `json:"meta,omitempty"`
	AccessControl interface{}            `json:"access_control,omitempty"`
	IsActive      bool                   `json:"is_active"`
	UpdatedAt     int64                  `json:"updated_at"`
	CreatedAt     int64                  `json:"created_at"`
}

// ModelMeta 模型元数据
type ModelMeta struct {
	ProfileImageURL   string             `json:"profile_image_url,omitempty"`
	Description       string             `json:"description,omitempty"`
	Capabilities      *ModelCapabilities `json:"capabilities,omitempty"`
	SuggestionPrompts []interface{}      `json:"suggestion_prompts,omitempty"`
	Tags              []string           `json:"tags,omitempty"`
}

// ModelCapabilities 模型能力配置
type ModelCapabilities struct {
	Vision          bool `json:"vision"`
	FileUpload      bool `json:"file_upload"`
	WebSearch       bool `json:"web_search"`
	ImageGeneration bool `json:"image_generation"`
	CodeInterpreter bool `json:"code_interpreter"`
	Citations       bool `json:"citations"`
}

// BackgroundTasks 后台任务配置
type BackgroundTasks struct {
	FollowUpGeneration bool                   `json:"follow_up_generation,omitempty"`
	CustomTasks        map[string]interface{} `json:"custom_tasks,omitempty"`
}

// ToBasicRequest 转换为简化的基础请求格式
func (e *EnhancedChatCompletionRequest) ToBasicRequest() *ChatCompletionRequest {
	// 获取最后一个用户消息作为query
	query := ""
	for i := len(e.Messages) - 1; i >= 0; i-- {
		if e.Messages[i].Role == "user" {
			query = e.Messages[i].Content
			break
		}
	}

	basic := &ChatCompletionRequest{
		Model:    e.Model,
		Query:    query,
		ChatID:   e.ChatID,
		Features: e.convertFeatures(),
	}

	// 简化版不支持高级参数，直接返回
	return basic
}

// convertFeatures converts map[string]interface{} to *Features
func (e *EnhancedChatCompletionRequest) convertFeatures() *Features {
	if e.Features == nil {
		return nil
	}
	
	features := &Features{}
	
	if val, ok := e.Features["code_interpreter"]; ok {
		if boolVal, ok := val.(bool); ok {
			features.CodeInterpreter = boolVal
		}
	}
	
	if val, ok := e.Features["image_generation"]; ok {
		if boolVal, ok := val.(bool); ok {
			features.ImageGeneration = boolVal
		}
	}
	
	if val, ok := e.Features["memory"]; ok {
		if boolVal, ok := val.(bool); ok {
			features.Memory = boolVal
		}
	}
	
	if val, ok := e.Features["web_search"]; ok {
		if boolVal, ok := val.(bool); ok {
			features.WebSearch = boolVal
		}
	}
	
	return features
}

// ExtractToolServerURLs 提取工具服务器URL列表
func (e *EnhancedChatCompletionRequest) ExtractToolServerURLs() []string {
	var urls []string
	for _, server := range e.ToolServers {
		urls = append(urls, server.URL)
	}
	return urls
}

// ExtractToolSpecs 提取工具规范列表
func (e *EnhancedChatCompletionRequest) ExtractToolSpecs() []ToolSpec {
	var specs []ToolSpec
	for _, server := range e.ToolServers {
		specs = append(specs, server.Specs...)
	}
	return specs
}

// HasAdvancedFeatures 检查是否包含高级功能
func (e *EnhancedChatCompletionRequest) HasAdvancedFeatures() bool {
	return len(e.ToolServers) > 0 ||
		e.ModelItem != nil ||
		e.BackgroundTasks != nil ||
		len(e.Params) > 0
}
