package request

// PromptConfigRequest 提示词配置请求
type PromptConfigRequest struct {
	TaskModel                            *string `json:"TASK_MODEL,omitempty" form:"TASK_MODEL"`
	TaskModelExternal                    *string `json:"TASK_MODEL_EXTERNAL,omitempty" form:"TASK_MODEL_EXTERNAL"`
	EnableTitleGeneration                *bool   `json:"ENABLE_TITLE_GENERATION,omitempty" form:"ENABLE_TITLE_GENERATION"`
	TitleGenerationPromptTemplate        *string `json:"TITLE_GENERATION_PROMPT_TEMPLATE,omitempty" form:"TITLE_GENERATION_PROMPT_TEMPLATE"`
	ImagePromptGenerationPromptTemplate  *string `json:"IMAGE_PROMPT_GENERATION_PROMPT_TEMPLATE,omitempty" form:"IMAGE_PROMPT_GENERATION_PROMPT_TEMPLATE"`
	EnableAutocompleteGeneration         *bool   `json:"ENABLE_AUTOCOMPLETE_GENERATION,omitempty" form:"ENABLE_AUTOCOMPLETE_GENERATION"`
	AutocompleteGenerationInputMaxLength *int    `json:"AUTOCOMPLETE_GENERATION_INPUT_MAX_LENGTH,omitempty" form:"AUTOCOMPLETE_GENERATION_INPUT_MAX_LENGTH"`
	TagsGenerationPromptTemplate         *string `json:"TAGS_GENERATION_PROMPT_TEMPLATE,omitempty" form:"TAGS_GENERATION_PROMPT_TEMPLATE"`
	FollowUpGenerationPromptTemplate     *string `json:"FOLLOW_UP_GENERATION_PROMPT_TEMPLATE,omitempty" form:"FOLLOW_UP_GENERATION_PROMPT_TEMPLATE"`
	EnableFollowUpGeneration             *bool   `json:"ENABLE_FOLLOW_UP_GENERATION,omitempty" form:"ENABLE_FOLLOW_UP_GENERATION"`
	EnableTagsGeneration                 *bool   `json:"ENABLE_TAGS_GENERATION,omitempty" form:"ENABLE_TAGS_GENERATION"`
	EnableSearchQueryGeneration          *bool   `json:"ENABLE_SEARCH_QUERY_GENERATION,omitempty" form:"ENABLE_SEARCH_QUERY_GENERATION"`
	EnableRetrievalQueryGeneration       *bool   `json:"ENABLE_RETRIEVAL_QUERY_GENERATION,omitempty" form:"ENABLE_RETRIEVAL_QUERY_GENERATION"`
	QueryGenerationPromptTemplate        *string `json:"QUERY_GENERATION_PROMPT_TEMPLATE,omitempty" form:"QUERY_GENERATION_PROMPT_TEMPLATE"`
	ToolsFunctionCallingPromptTemplate   *string `json:"TOOLS_FUNCTION_CALLING_PROMPT_TEMPLATE,omitempty" form:"TOOLS_FUNCTION_CALLING_PROMPT_TEMPLATE"`
}
