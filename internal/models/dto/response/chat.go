package response

import (
	"encoding/json"
	"hkchat_api/pkg/clients/llm"
	"time"
)

// ModelInfo 模型信息（API响应用）
type ModelInfo struct {
	ID             string    `json:"id"`
	Name           string    `json:"name"`
	ConnectionType string    `json:"connection_type"`
	Tags           []string  `json:"tags,omitempty"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
}

// ChatCompletionResponse 聊天完成响应结构
type ChatCompletionResponse struct {
	ID      string                   `json:"id"`
	Object  string                   `json:"object"`
	Created int64                    `json:"created"`
	Model   string                   `json:"model"`
	Choices []ChatCompletionChoice   `json:"choices"`
	Usage   ChatCompletionUsage      `json:"usage"`
	Error   *ChatCompletionError     `json:"error,omitempty"`
	Events  []map[string]interface{} `json:"events,omitempty"`
}

// ChatCompletionChoice 聊天完成选择
type ChatCompletionChoice struct {
	Index        int         `json:"index"`
	Message      ChatMessage `json:"message"`
	FinishReason string      `json:"finish_reason"`
}

// ChatMessage 聊天消息
type ChatMessage struct {
	ID        string `json:"id,omitempty"`
	Role      string `json:"role"` // system, user, assistant
	Content   string `json:"content"`
	CreatedAt int64  `json:"created_at,omitempty"`
}

// ChatCompletionUsage token使用统计
type ChatCompletionUsage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

// ChatCompletionError 错误信息
type ChatCompletionError struct {
	Type    string `json:"type"`
	Message string `json:"message"`
	Code    string `json:"code,omitempty"`
}

// FromOpenAI 从OpenAI格式转换
func (r *ChatCompletionResponse) FromOpenAI(openaiResp *llm.ChatResponse) {
	r.ID = openaiResp.ID
	r.Object = openaiResp.Object
	r.Created = openaiResp.Created
	r.Model = openaiResp.Model

	// 转换Choices
	r.Choices = make([]ChatCompletionChoice, len(openaiResp.Choices))
	for i, choice := range openaiResp.Choices {
		r.Choices[i] = ChatCompletionChoice{
			Index: choice.Index,
			Message: ChatMessage{
				Role:    choice.Message.Role,
				Content: choice.Message.Content,
			},
			FinishReason: choice.FinishReason,
		}
	}

	// 转换Usage
	r.Usage = ChatCompletionUsage{
		PromptTokens:     openaiResp.Usage.PromptTokens,
		CompletionTokens: openaiResp.Usage.CompletionTokens,
		TotalTokens:      openaiResp.Usage.TotalTokens,
	}
}

// NewFromOpenAI 从OpenAI响应创建新的ChatCompletionResponse
func NewFromOpenAI(openaiResp *llm.ChatResponse) *ChatCompletionResponse {
	resp := &ChatCompletionResponse{}
	resp.FromOpenAI(openaiResp)
	return resp
}

// ChatCompletedResponse 聊天完成后的响应
type ChatCompletedResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message,omitempty"`
	Data    *struct {
		ChatID    string `json:"chat_id"`
		MessageID string `json:"message_id"`
	} `json:"data,omitempty"`
}

// CreateChatResponse 创建聊天响应
type CreateChatResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message,omitempty"`
	Data    *struct {
		ID        uint   `json:"id"`
		Title     string `json:"title"`
		SessionID string `json:"session_id"`
		ModelID   string `json:"model_id"`
		CreatedAt int64  `json:"created_at"`
	} `json:"data,omitempty"`
}

// SendMessageResponse 发送消息响应
type SendMessageResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message,omitempty"`
	Data    *struct {
		ID        uint   `json:"id"`
		ChatID    uint   `json:"chat_id"`
		MessageID string `json:"message_id"`
		Role      string `json:"role"`
		Content   string `json:"content"`
		Type      string `json:"type"`
		CreatedAt int64  `json:"created_at"`
	} `json:"data,omitempty"`
}

// ChatListResponse 聊天列表响应
type ChatListResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message,omitempty"`
	Data    *struct {
		Chats      []ChatItem `json:"chats"`
		Total      int64      `json:"total"`
		Page       int        `json:"page"`
		PageSize   int        `json:"page_size"`
		TotalPages int        `json:"total_pages"`
	} `json:"data,omitempty"`
}

// ChatItem 聊天项
type ChatItem struct {
	ID           uint   `json:"id"`
	Title        string `json:"title"`
	Status       string `json:"status"`
	ModelID      string `json:"model_id"`
	SessionID    string `json:"session_id"`
	LastMessage  string `json:"last_message,omitempty"`
	MessageCount int    `json:"message_count"`
	CreatedAt    int64  `json:"created_at"`
	UpdatedAt    int64  `json:"updated_at"`
}

// ChatDetailResponse 聊天详情响应
type ChatDetailResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message,omitempty"`
	Data    *struct {
		ID        uint          `json:"id"`
		Title     string        `json:"title"`
		Status    string        `json:"status"`
		ModelID   string        `json:"model_id"`
		SessionID string        `json:"session_id"`
		Messages  []MessageItem `json:"messages,omitempty"`
		CreatedAt int64         `json:"created_at"`
		UpdatedAt int64         `json:"updated_at"`
	} `json:"data,omitempty"`
}

// MessageItem 消息项
type MessageItem struct {
	ID        uint   `json:"id"`
	MessageID string `json:"message_id"`
	Role      string `json:"role"`
	Content   string `json:"content"`
	Type      string `json:"type"`
	CreatedAt int64  `json:"created_at"`
}

// CommonResponse 通用响应
type CommonResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message,omitempty"`
	Data    interface{} `json:"data,omitempty"`
}

// ErrorResponse 错误响应
type ErrorResponse struct {
	Success bool   `json:"success"`
	Error   string `json:"error"`
	Message string `json:"message"`
	Code    string `json:"code,omitempty"`
	Details string `json:"details,omitempty"`
}

// ToJSON 转换为 JSON 字符串
func (r *ChatCompletionResponse) ToJSON() string {
	data, _ := json.Marshal(r)
	return string(data)
}

// ToJSON 转换为 JSON 字符串
func (r *CommonResponse) ToJSON() string {
	data, _ := json.Marshal(r)
	return string(data)
}
