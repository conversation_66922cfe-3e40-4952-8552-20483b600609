package response

import "hkchat_api/internal/models/domain"

// PromptConfigResponse 提示词配置响应
type PromptConfigResponse struct {
	TaskModel                            string `json:"TASK_MODEL"`
	TaskModelExternal                    string `json:"TASK_MODEL_EXTERNAL"`
	EnableTitleGeneration                bool   `json:"ENABLE_TITLE_GENERATION"`
	TitleGenerationPromptTemplate        string `json:"TITLE_GENERATION_PROMPT_TEMPLATE"`
	ImagePromptGenerationPromptTemplate  string `json:"IMAGE_PROMPT_GENERATION_PROMPT_TEMPLATE"`
	EnableAutocompleteGeneration         bool   `json:"ENABLE_AUTOCOMPLETE_GENERATION"`
	AutocompleteGenerationInputMaxLength int    `json:"AUTOCOMPLETE_GENERATION_INPUT_MAX_LENGTH"`
	TagsGenerationPromptTemplate         string `json:"TAGS_GENERATION_PROMPT_TEMPLATE"`
	FollowUpGenerationPromptTemplate     string `json:"FOLLOW_UP_GENERATION_PROMPT_TEMPLATE"`
	EnableFollowUpGeneration             bool   `json:"ENABLE_FOLLOW_UP_GENERATION"`
	EnableTagsGeneration                 bool   `json:"ENABLE_TAGS_GENERATION"`
	EnableSearchQueryGeneration          bool   `json:"ENABLE_SEARCH_QUERY_GENERATION"`
	EnableRetrievalQueryGeneration       bool   `json:"ENABLE_RETRIEVAL_QUERY_GENERATION"`
	QueryGenerationPromptTemplate        string `json:"QUERY_GENERATION_PROMPT_TEMPLATE"`
	ToolsFunctionCallingPromptTemplate   string `json:"TOOLS_FUNCTION_CALLING_PROMPT_TEMPLATE"`
}

// FromDomain 从domain模型转换
func (r *PromptConfigResponse) FromDomain(config *domain.PromptConfig) {
	r.TaskModel = config.TaskModel
	r.TaskModelExternal = config.TaskModelExternal
	r.EnableTitleGeneration = config.EnableTitleGeneration
	r.TitleGenerationPromptTemplate = config.TitleGenerationPromptTemplate
	r.ImagePromptGenerationPromptTemplate = config.ImagePromptGenerationPromptTemplate
	r.EnableAutocompleteGeneration = config.EnableAutocompleteGeneration
	r.AutocompleteGenerationInputMaxLength = config.AutocompleteGenerationInputMaxLength
	r.TagsGenerationPromptTemplate = config.TagsGenerationPromptTemplate
	r.FollowUpGenerationPromptTemplate = config.FollowUpGenerationPromptTemplate
	r.EnableFollowUpGeneration = config.EnableFollowUpGeneration
	r.EnableTagsGeneration = config.EnableTagsGeneration
	r.EnableSearchQueryGeneration = config.EnableSearchQueryGeneration
	r.EnableRetrievalQueryGeneration = config.EnableRetrievalQueryGeneration
	r.QueryGenerationPromptTemplate = config.QueryGenerationPromptTemplate
	r.ToolsFunctionCallingPromptTemplate = config.ToolsFunctionCallingPromptTemplate
}

// NewFromDomain 从domain模型创建响应
func NewPromptConfigResponse(config *domain.PromptConfig) *PromptConfigResponse {
	resp := &PromptConfigResponse{}
	resp.FromDomain(config)
	return resp
}
