package response

import (
	"encoding/json"
	"fmt"
	"time"
)

// HKGAIChatSSEEventType SSE事件类型
type HKGAIChatSSEEventType string

const (
	SSEEventTypeAppend HKGAIChatSSEEventType = "APPEND"
	SSEEventTypeFinish HKGAIChatSSEEventType = "FINISH"
	SSEEventTypeError  HKGAIChatSSEEventType = "ERROR"
)

// HKGAIChatSSEEvent SSE事件包装器
type HKGAIChatSSEEvent struct {
	Event string      `json:"event"`
	Data  interface{} `json:"data"`
}

// BaseMessageV2 基础消息结构V2
type BaseMessageV2 struct {
	ID                 string                 `json:"id"`
	Content            string                 `json:"content"`
	ModelName          string                 `json:"model_name"`
	Role               int                    `json:"role"` // 1=用户，2=助手，3=文件，4=卡片
	ConversationID     string                 `json:"conversation_id"`
	Status             int                    `json:"status"` // 1=完成，2=失败，5=进行中
	Tools              []Tool                 `json:"tools,omitempty"`
	Inputs             map[string]interface{} `json:"inputs,omitempty"`
	UsageMetadata      *UsageMetadata         `json:"usage_metadata,omitempty"`
	RetrieverResources []RetrieverResource    `json:"retriever_resources,omitempty"`
	ResponseMetadata   *ResponseMetadata      `json:"response_metadata,omitempty"`
}

// UsageMetadata 使用统计信息
type UsageMetadata struct {
	OutputTokens int     `json:"output_tokens"` // 输出token数
	TTFT         float64 `json:"ttft"`          // 首token延迟(秒)
	TPOT         float64 `json:"tpot"`          // 单token延迟(秒)
	Latency      float64 `json:"latency"`       // 总延迟(秒)
	TPS          float64 `json:"tps"`           // tokens/秒
}

// Tool 工具结构
type Tool struct {
	ToolName    string `json:"tool_name"`   // 工具名称
	ToolInput   string `json:"tool_input"`  // 工具输入
	Thought     string `json:"thought"`     // 思考过程
	Observation string `json:"observation"` // 观察结果(JSON格式)
}

// RetrieverResource 检索资源信息
type RetrieverResource struct {
	Position     int     `json:"position"`
	DatasetID    string  `json:"dataset_id"`
	DatasetName  string  `json:"dataset_name"`
	DocumentID   string  `json:"document_id"`
	DocumentName string  `json:"document_name"`
	SegmentID    string  `json:"segment_id"`
	Score        float64 `json:"score"`
	Content      string  `json:"content"`
}

// ResponseMetadata 响应元数据
type ResponseMetadata struct {
	ModelName        string   `json:"model_name"`
	FinishReason     string   `json:"finish_reason"`
	InvalidToolCalls []string `json:"invalid_tool_calls"`
}

// HKGAIChatAppendEvent APPEND事件数据
type HKGAIChatAppendEvent struct {
	BaseMessageV2
}

// HKGAIChatFinishEvent FINISH事件数据
type HKGAIChatFinishEvent struct {
	ID             string         `json:"id"`
	UserID         string         `json:"user_id"`
	ConversationID string         `json:"conversation_id"`
	SessionID      string         `json:"session_id"`
	GenerateID     string         `json:"generate_id"`
	Status         int            `json:"status"`
	Tokens         int            `json:"tokens"`
	Extra          string         `json:"extra"` // JSON字符串格式的工具调用信息
	Content        string         `json:"content"`
	Role           string         `json:"role"`
	Input          string         `json:"input"` // JSON字符串格式的输入参数
	MetaData       *UsageMetadata `json:"meta_data,omitempty"`
	CreatedAt      string         `json:"created_at"`
	UpdatedAt      string         `json:"updated_at"`
}

// HKGAIChatErrorEvent ERROR事件数据
type HKGAIChatErrorEvent struct {
	Message string `json:"message"`
}

// ToSSEString 转换为SSE格式字符串
func (e *HKGAIChatSSEEvent) ToSSEString() string {
	dataBytes, err := json.Marshal(e.Data)
	if err != nil {
		// 如果JSON序列化失败，返回错误事件
		errorData := map[string]interface{}{
			"message": fmt.Sprintf("JSON序列化失败: %v", err),
		}
		errorBytes, _ := json.Marshal(errorData)
		return "event: ERROR\ndata: " + string(errorBytes) + "\n\n"
	}
	return "event: " + e.Event + "\ndata: " + string(dataBytes) + "\n\n"
}

// NewAppendEvent 创建APPEND事件
func NewAppendEvent(data *HKGAIChatAppendEvent) *HKGAIChatSSEEvent {
	return &HKGAIChatSSEEvent{
		Event: string(SSEEventTypeAppend),
		Data:  data,
	}
}

// NewFinishEvent 创建FINISH事件
func NewFinishEvent(data *HKGAIChatFinishEvent) *HKGAIChatSSEEvent {
	return &HKGAIChatSSEEvent{
		Event: string(SSEEventTypeFinish),
		Data:  data,
	}
}

// NewErrorEvent 创建ERROR事件
func NewErrorEvent(message string) *HKGAIChatSSEEvent {
	return &HKGAIChatSSEEvent{
		Event: string(SSEEventTypeError),
		Data:  &HKGAIChatErrorEvent{Message: message},
	}
}

// HKGAIChatV2Response ChatV2的完整响应结构
type HKGAIChatV2Response struct {
	Events []HKGAIChatSSEEvent `json:"events"`
}

// ConvertToHKGAIFormat 将标准聊天响应转换为HKGAI格式
func (r *ChatCompletionResponse) ConvertToHKGAIFormat(userID, conversationID, sessionID string) []*HKGAIChatSSEEvent {
	var events []*HKGAIChatSSEEvent

	if len(r.Choices) > 0 {
		choice := r.Choices[0]

		// 创建APPEND事件
		appendEvent := &HKGAIChatAppendEvent{
			BaseMessageV2: BaseMessageV2{
				ID:             r.ID,
				Content:        choice.Message.Content,
				ModelName:      r.Model,
				Role:           2, // 助手
				ConversationID: conversationID,
				Status:         5,        // 进行中
				Tools:          []Tool{}, // 空的工具列表
				Inputs: map[string]interface{}{
					"temperature": "0.7",
					"language":    "zh-CN",
				},
				UsageMetadata: &UsageMetadata{
					OutputTokens: r.Usage.CompletionTokens,
					TTFT:         0.8,
					TPOT:         0.05,
					Latency:      2.5,
					TPS:          20.0,
				},
				RetrieverResources: []RetrieverResource{},
				ResponseMetadata: &ResponseMetadata{
					ModelName:        r.Model,
					FinishReason:     choice.FinishReason,
					InvalidToolCalls: []string{},
				},
			},
		}
		events = append(events, NewAppendEvent(appendEvent))

		// 创建FINISH事件
		finishEvent := &HKGAIChatFinishEvent{
			ID:             r.ID,
			UserID:         userID,
			ConversationID: conversationID,
			SessionID:      sessionID,
			GenerateID:     "gen_" + r.ID,
			Status:         1, // 完成
			Tokens:         r.Usage.TotalTokens,
			Extra:          "[]", // 空的工具调用信息
			Content:        choice.Message.Content,
			Role:           "assistant",
			Input:          `{"temperature":"0.7","language":"zh-CN"}`,
			MetaData: &UsageMetadata{
				OutputTokens: r.Usage.CompletionTokens,
				TTFT:         0.8,
				TPOT:         0.05,
				Latency:      7.5,
				TPS:          20.0,
			},
			CreatedAt: time.Unix(r.Created, 0).Format(time.RFC3339),
			UpdatedAt: time.Now().Format(time.RFC3339),
		}
		events = append(events, NewFinishEvent(finishEvent))
	}

	return events
}
