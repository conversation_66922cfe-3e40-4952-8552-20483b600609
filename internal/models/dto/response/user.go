package response

import "time"

// UserResponse 用户响应DTO - 公开用户信息
type UserResponse struct {
	ID              string    `json:"id" example:"user_123"`
	Name            string    `json:"name" example:"张三"`
	Email           string    `json:"email" example:"zhang<PERSON>@example.com"`
	Role            string    `json:"role" example:"user"`
	ProfileImageURL string    `json:"profile_image_url" example:"https://example.com/avatar.jpg"`
	IsActive        bool      `json:"is_active" example:"true"`
	LastActiveAt    time.Time `json:"last_active_at" example:"2024-01-01T12:00:00Z"`
	CreatedAt       time.Time `json:"created_at" example:"2024-01-01T00:00:00Z"`
	UpdatedAt       time.Time `json:"updated_at" example:"2024-01-01T12:00:00Z"`
}

// UserDetailResponse 用户详细信息响应DTO - 包含设置和扩展信息
type UserDetailResponse struct {
	UserResponse
	Settings        UserSettingsResponse `json:"settings"`
	Info            UserInfoResponse     `json:"info"`
	Has<PERSON>I<PERSON>ey       bool                 `json:"has_api_key" example:"true"`
	IsOAuthUser     bool                 `json:"is_oauth_user" example:"false"`
	ProfileComplete bool                 `json:"profile_complete" example:"true"`
}

// UserSettingsResponse 用户设置响应DTO
type UserSettingsResponse struct {
	Theme           string                    `json:"theme" example:"dark"`
	Language        string                    `json:"language" example:"zh-CN"`
	Notifications   NotificationPrefsResponse `json:"notifications"`
	Privacy         PrivacySettingsResponse   `json:"privacy"`
	ChatPreferences ChatPreferencesResponse   `json:"chat_preferences"`
}

// NotificationPrefsResponse 通知偏好响应DTO
type NotificationPrefsResponse struct {
	Email   bool `json:"email" example:"true"`
	Desktop bool `json:"desktop" example:"false"`
	Mobile  bool `json:"mobile" example:"true"`
	Sound   bool `json:"sound" example:"false"`
}

// PrivacySettingsResponse 隐私设置响应DTO
type PrivacySettingsResponse struct {
	ShowEmail      bool `json:"show_email" example:"false"`
	ShowLastActive bool `json:"show_last_active" example:"true"`
	AllowDirectMsg bool `json:"allow_direct_msg" example:"true"`
}

// ChatPreferencesResponse 聊天偏好响应DTO
type ChatPreferencesResponse struct {
	DefaultModel   string `json:"default_model" example:"gpt-4"`
	MessageHistory int    `json:"message_history" example:"100"`
	AutoSave       bool   `json:"auto_save" example:"true"`
	ShowTimestamps bool   `json:"show_timestamps" example:"true"`
}

// UserInfoResponse 用户信息响应DTO
type UserInfoResponse struct {
	Bio         string                  `json:"bio" example:"这是我的个人简介"`
	Location    string                  `json:"location" example:"北京"`
	Website     string                  `json:"website" example:"https://example.com"`
	SocialLinks map[string]string       `json:"social_links" example:"github:username,twitter:handle"`
	Preferences UserPreferencesResponse `json:"preferences"`
	Stats       UserStatsResponse       `json:"stats"`
}

// UserPreferencesResponse 用户偏好响应DTO
type UserPreferencesResponse struct {
	Timezone   string   `json:"timezone" example:"Asia/Shanghai"`
	DateFormat string   `json:"date_format" example:"YYYY-MM-DD"`
	TimeFormat string   `json:"time_format" example:"24h"`
	Interests  []string `json:"interests" example:"编程,音乐,旅行"`
	Skills     []string `json:"skills" example:"Go,Python,JavaScript"`
}

// UserStatsResponse 用户统计响应DTO
type UserStatsResponse struct {
	TotalChats    int `json:"total_chats" example:"15"`
	TotalMessages int `json:"total_messages" example:"450"`
	JoinedDays    int `json:"joined_days" example:"30"`
}

// UserPublicResponse 用户公开信息响应DTO - 用于公开展示
type UserPublicResponse struct {
	ID              string     `json:"id" example:"user_123"`
	Name            string     `json:"name" example:"张三"`
	ProfileImageURL string     `json:"profile_image_url" example:"https://example.com/avatar.jpg"`
	Bio             string     `json:"bio,omitempty" example:"这是我的个人简介"`
	Location        string     `json:"location,omitempty" example:"北京"`
	Website         string     `json:"website,omitempty" example:"https://example.com"`
	IsOnline        bool       `json:"is_online" example:"true"`
	LastActiveAt    *time.Time `json:"last_active_at,omitempty" example:"2024-01-01T12:00:00Z"` // 根据隐私设置可能为空
	JoinedAt        time.Time  `json:"joined_at" example:"2024-01-01T00:00:00Z"`
}

// APIKeyResponse API密钥响应DTO
type APIKeyResponse struct {
	ID          string     `json:"id" example:"key_123"`
	Name        string     `json:"name" example:"我的API密钥"`
	Description string     `json:"description" example:"用于自动化脚本"`
	KeyPreview  string     `json:"key_preview" example:"hk_****_****_abcd"` // 只显示部分密钥
	IsActive    bool       `json:"is_active" example:"true"`
	CreatedAt   time.Time  `json:"created_at" example:"2024-01-01T00:00:00Z"`
	LastUsedAt  *time.Time `json:"last_used_at,omitempty" example:"2024-01-01T12:00:00Z"`
	ExpiresAt   *time.Time `json:"expires_at,omitempty" example:"2024-12-31T23:59:59Z"`
}

// CreateAPIKeyResponse 创建API密钥响应DTO
type CreateAPIKeyResponse struct {
	APIKeyResponse
	FullKey string `json:"full_key" example:"hk_1234567890abcdef"` // 只在创建时返回完整密钥
}

// UsersListResponse 用户列表响应DTO
type UsersListResponse struct {
	Users      []UserResponse     `json:"users"`
	Pagination PaginationResponse `json:"pagination"`
}

// PaginationResponse 分页响应DTO
type PaginationResponse struct {
	Page       int   `json:"page" example:"1"`
	PageSize   int   `json:"page_size" example:"20"`
	Total      int64 `json:"total" example:"150"`
	TotalPages int   `json:"total_pages" example:"8"`
	HasNext    bool  `json:"has_next" example:"true"`
	HasPrev    bool  `json:"has_prev" example:"false"`
}

// UserSearchResponse 用户搜索响应DTO
type UserSearchResponse struct {
	Users      []UserResponse     `json:"users"`
	Query      string             `json:"query" example:"张三"`
	Filters    SearchFilters      `json:"filters"`
	Pagination PaginationResponse `json:"pagination"`
}

// SearchFilters 搜索过滤器响应DTO
type SearchFilters struct {
	Role     string `json:"role,omitempty" example:"user"`
	IsActive *bool  `json:"is_active,omitempty" example:"true"`
	SortBy   string `json:"sort_by" example:"created_at"`
	SortDir  string `json:"sort_dir" example:"desc"`
}

// UserStatsOverviewResponse 用户统计概览响应DTO
type UserStatsOverviewResponse struct {
	TotalUsers   int64 `json:"total_users" example:"1500"`
	ActiveUsers  int64 `json:"active_users" example:"1200"`
	PendingUsers int64 `json:"pending_users" example:"50"`
	AdminUsers   int64 `json:"admin_users" example:"5"`
	OnlineUsers  int64 `json:"online_users" example:"120"`
	NewToday     int64 `json:"new_today" example:"10"`
	NewThisWeek  int64 `json:"new_this_week" example:"50"`
	NewThisMonth int64 `json:"new_this_month" example:"200"`
}

// BatchOperationResponse 批量操作响应DTO
type BatchOperationResponse struct {
	Success    []string `json:"success" example:"user1,user2"`
	Failed     []string `json:"failed" example:"user3"`
	TotalCount int      `json:"total_count" example:"3"`
	Errors     []string `json:"errors,omitempty" example:"用户user3不存在"`
}

// ExportUsersResponse 导出用户响应DTO
type ExportUsersResponse struct {
	FileName string `json:"file_name" example:"users_export_20240101.csv"`
	FileURL  string `json:"file_url" example:"https://example.com/exports/users_export_20240101.csv"`
	Format   string `json:"format" example:"csv"`
	Count    int    `json:"count" example:"1500"`
	Size     int64  `json:"size" example:"1048576"` // 字节
}
