package entities

// Note note 表实体
type Note struct {
	Id	string	`gorm:"primaryKey;type:varchar(255)" json:"id"`
	UserId	*string	`gorm:"type:text" json:"user_id"`
	Title	*string	`gorm:"type:text" json:"title"`
	Data	JSON	`gorm:"type:jsonb" json:"data"`
	Meta	JSON	`gorm:"type:jsonb" json:"meta"`
	AccessControl	JSON	`gorm:"type:jsonb" json:"access_control"`
	CreatedAt	*int64	`json:"created_at"`
	UpdatedAt	*int64	`json:"updated_at"`
}

// TableName 设置表名
func (Note) TableName() string {
	return "note"
}
