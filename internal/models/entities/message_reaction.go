package entities

// MessageReaction message_reaction 表实体
type MessageReaction struct {
	Id	string	`gorm:"primaryKey;type:varchar(255)" json:"id"`
	UserId	string	`gorm:"type:text;not null" json:"user_id"`
	MessageId	string	`gorm:"type:text;not null" json:"message_id"`
	Name	string	`gorm:"type:text;not null" json:"name"`
	CreatedAt	*int64	`json:"created_at"`
}

// TableName 设置表名
func (MessageReaction) TableName() string {
	return "message_reaction"
}
