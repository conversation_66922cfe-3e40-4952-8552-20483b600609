package entities

// Function function 表实体
type Function struct {
	Id	string	`gorm:"type:text;not null" json:"id"`
	UserId	string	`gorm:"type:text;not null" json:"user_id"`
	Name	string	`gorm:"type:text;not null" json:"name"`
	Type	string	`gorm:"type:text;not null" json:"type"`
	Content	string	`gorm:"type:text;not null" json:"content"`
	Meta	JSON	`gorm:"type:jsonb;not null" json:"meta"`
	CreatedAt	int64	`gorm:"not null" json:"created_at"`
	UpdatedAt	int64	`gorm:"not null" json:"updated_at"`
	Valves	*string	`gorm:"type:text" json:"valves"`
	IsActive	bool	`gorm:"not null" json:"is_active"`
	IsGlobal	bool	`gorm:"not null" json:"is_global"`
}

// TableName 设置表名
func (Function) TableName() string {
	return "function"
}
