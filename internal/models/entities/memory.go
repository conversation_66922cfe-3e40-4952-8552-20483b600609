package entities

// Memory memory 表实体
type Memory struct {
	Id	string	`gorm:"type:text;not null" json:"id"`
	UserId	string	`gorm:"type:varchar(255);not null" json:"user_id"`
	Content	string	`gorm:"type:text;not null" json:"content"`
	UpdatedAt	int64	`gorm:"not null" json:"updated_at"`
	CreatedAt	int64	`gorm:"not null" json:"created_at"`
}

// TableName 设置表名
func (Memory) TableName() string {
	return "memory"
}
