package entities

// Channel channel 表实体
type Channel struct {
	Id	string	`gorm:"primaryKey;type:varchar(255)" json:"id"`
	UserId	*string	`gorm:"type:text" json:"user_id"`
	Name	*string	`gorm:"type:text" json:"name"`
	Description	*string	`gorm:"type:text" json:"description"`
	Data	JSON	`gorm:"type:jsonb" json:"data"`
	Meta	JSON	`gorm:"type:jsonb" json:"meta"`
	AccessControl	JSON	`gorm:"type:jsonb" json:"access_control"`
	CreatedAt	*int64	`json:"created_at"`
	UpdatedAt	*int64	`json:"updated_at"`
	Type	*string	`gorm:"type:text" json:"type"`
}

// TableName 设置表名
func (Channel) TableName() string {
	return "channel"
}
