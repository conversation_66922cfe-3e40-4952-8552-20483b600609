package entities

// Group group 表实体
type Group struct {
	Id	string	`gorm:"primaryKey;type:varchar(255)" json:"id"`
	UserId	*string	`gorm:"type:text" json:"user_id"`
	Name	*string	`gorm:"type:text" json:"name"`
	Description	*string	`gorm:"type:text" json:"description"`
	Data	JSON	`gorm:"type:jsonb" json:"data"`
	Meta	JSON	`gorm:"type:jsonb" json:"meta"`
	Permissions	JSON	`gorm:"type:jsonb" json:"permissions"`
	UserIds	JSON	`gorm:"type:jsonb" json:"user_ids"`
	CreatedAt	*int64	`json:"created_at"`
	UpdatedAt	*int64	`json:"updated_at"`
}

// TableName 设置表名
func (Group) TableName() string {
	return "group"
}
