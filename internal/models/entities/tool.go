package entities

// Tool tool 表实体
type Tool struct {
	Id	string	`gorm:"type:text;not null" json:"id"`
	UserId	string	`gorm:"type:text;not null" json:"user_id"`
	Name	string	`gorm:"type:text;not null" json:"name"`
	Content	string	`gorm:"type:text;not null" json:"content"`
	Specs	string	`gorm:"type:text;not null" json:"specs"`
	Meta	JSON	`gorm:"type:jsonb;not null" json:"meta"`
	CreatedAt	int64	`gorm:"not null" json:"created_at"`
	UpdatedAt	int64	`gorm:"not null" json:"updated_at"`
	Valves	*string	`gorm:"type:text" json:"valves"`
	AccessControl	JSON	`gorm:"type:jsonb" json:"access_control"`
}

// TableName 设置表名
func (Tool) TableName() string {
	return "tool"
}
