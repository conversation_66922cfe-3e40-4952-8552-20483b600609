package entities

// Prompt prompt 表实体
type Prompt struct {
	Id	int64	`gorm:"primaryKey" json:"id"`
	Command	string	`gorm:"type:varchar(255);not null" json:"command"`
	UserId	string	`gorm:"type:varchar(255);not null" json:"user_id"`
	Title	string	`gorm:"type:text;not null" json:"title"`
	Content	string	`gorm:"type:text;not null" json:"content"`
	Timestamp	int64	`gorm:"not null" json:"timestamp"`
	AccessControl	JSON	`gorm:"type:jsonb" json:"access_control"`
}

// TableName 设置表名
func (Prompt) TableName() string {
	return "prompt"
}
