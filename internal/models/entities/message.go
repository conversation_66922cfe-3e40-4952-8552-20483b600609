package entities

// Message message 表实体
type Message struct {
	Id	string	`gorm:"primaryKey;type:varchar(255)" json:"id"`
	UserId	*string	`gorm:"type:text" json:"user_id"`
	ChannelId	*string	`gorm:"type:text" json:"channel_id"`
	Content	*string	`gorm:"type:text" json:"content"`
	Data	JSON	`gorm:"type:jsonb" json:"data"`
	Meta	JSON	`gorm:"type:jsonb" json:"meta"`
	CreatedAt	*int64	`json:"created_at"`
	UpdatedAt	*int64	`json:"updated_at"`
	ParentId	*string	`gorm:"type:text" json:"parent_id"`
}

// TableName 设置表名
func (Message) TableName() string {
	return "message"
}
