package entities

// File file 表实体
type File struct {
	Id	string	`gorm:"type:text;not null" json:"id"`
	UserId	string	`gorm:"type:text;not null" json:"user_id"`
	Filename	string	`gorm:"type:text;not null" json:"filename"`
	Meta	JSON	`gorm:"type:jsonb" json:"meta"`
	CreatedAt	int64	`gorm:"not null" json:"created_at"`
	Hash	*string	`gorm:"type:text" json:"hash"`
	Data	JSON	`gorm:"type:jsonb" json:"data"`
	UpdatedAt	*int64	`json:"updated_at"`
	Path	*string	`gorm:"type:text" json:"path"`
	AccessControl	JSON	`gorm:"type:jsonb" json:"access_control"`
}

// TableName 设置表名
func (File) TableName() string {
	return "file"
}
