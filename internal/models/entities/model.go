package entities

// Model model 表实体
type Model struct {
	Id	string	`gorm:"type:text;not null" json:"id"`
	UserId	string	`gorm:"type:text;not null" json:"user_id"`
	BaseModelId	*string	`gorm:"type:text" json:"base_model_id"`
	Name	string	`gorm:"type:text;not null" json:"name"`
	Meta	JSON	`gorm:"type:jsonb;not null" json:"meta"`
	Params	string	`gorm:"type:text;not null" json:"params"`
	CreatedAt	int64	`gorm:"not null" json:"created_at"`
	UpdatedAt	int64	`gorm:"not null" json:"updated_at"`
	AccessControl	JSON	`gorm:"type:jsonb" json:"access_control"`
	IsActive	bool	`gorm:"not null" json:"is_active"`
}

// TableName 设置表名
func (Model) TableName() string {
	return "model"
}
