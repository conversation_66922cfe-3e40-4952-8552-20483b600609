package entities

// Chatidtag chatidtag 表实体
type Chatidtag struct {
	Id	string	`gorm:"type:text;not null" json:"id"`
	TagName	string	`gorm:"type:varchar(255);not null" json:"tag_name"`
	ChatId	string	`gorm:"type:varchar(255);not null" json:"chat_id"`
	UserId	string	`gorm:"type:varchar(255);not null" json:"user_id"`
	Timestamp	int64	`gorm:"not null" json:"timestamp"`
}

// TableName 设置表名
func (Chatidtag) TableName() string {
	return "chatidtag"
}
