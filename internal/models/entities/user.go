package entities

// User user 表实体
type User struct {
	Id              string  `gorm:"type:text;not null" json:"id"`
	Name            string  `gorm:"type:varchar(255);not null" json:"name"`
	Email           string  `gorm:"type:varchar(255);not null" json:"email"`
	Role            string  `gorm:"type:varchar(255);not null" json:"role"`
	ProfileImageUrl string  `gorm:"type:text;not null" json:"profile_image_url"`
	ApiKey          *string `gorm:"type:varchar(255)" json:"api_key"`
	CreatedAt       int64   `gorm:"not null" json:"created_at"`
	UpdatedAt       int64   `gorm:"not null" json:"updated_at"`
	LastActiveAt    int64   `gorm:"not null" json:"last_active_at"`
	Settings        JSON    `gorm:"type:jsonb" json:"settings"`
	Info            JSON    `gorm:"type:jsonb" json:"info"`
	OauthSub        *string `gorm:"type:text" json:"oauth_sub"`
}

// TableName 设置表名
func (User) TableName() string {
	return "user"
}

// ========== 接口实现方法 ==========

// GetID 获取用户ID
func (u *User) GetID() string {
	return u.Id
}

// GetName 获取用户名
func (u *User) GetName() string {
	return u.Name
}

// GetEmail 获取用户邮箱
func (u *User) GetEmail() string {
	return u.Email
}

// IsActive 判断用户是否活跃 (简单实现)
func (u *User) IsActive() bool {
	// 可以根据LastActiveAt时间判断，这里简单返回true
	return true
}

// GetRole 获取用户角色
func (u *User) GetRole() string {
	return u.Role
}

// GetLastActiveAt 获取最后活跃时间
func (u *User) GetLastActiveAt() interface{} {
	return u.LastActiveAt
}

// IsAdmin 判断是否为管理员
func (u *User) IsAdmin() bool {
	return u.Role == "admin"
}

// CanPerformAdminActions 判断是否可以执行管理员操作
func (u *User) CanPerformAdminActions() bool {
	return u.IsAdmin()
}

// CanAccessChat 判断是否可以访问聊天功能
func (u *User) CanAccessChat() bool {
	return u.Role == "user" || u.Role == "admin"
}

// GetDisplayName 获取显示名称
func (u *User) GetDisplayName() string {
	if u.Name != "" {
		return u.Name
	}
	return u.Email
}
