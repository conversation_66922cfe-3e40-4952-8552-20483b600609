package entities

// Feedback feedback 表实体
type Feedback struct {
	Id	string	`gorm:"primaryKey;type:varchar(255)" json:"id"`
	UserId	*string	`gorm:"type:text" json:"user_id"`
	Version	*string	`gorm:"type:text" json:"version"`
	Type	*string	`gorm:"type:text" json:"type"`
	Data	JSON	`gorm:"type:jsonb" json:"data"`
	Meta	JSON	`gorm:"type:jsonb" json:"meta"`
	Snapshot	JSON	`gorm:"type:jsonb" json:"snapshot"`
	CreatedAt	int64	`gorm:"not null" json:"created_at"`
	UpdatedAt	int64	`gorm:"not null" json:"updated_at"`
}

// TableName 设置表名
func (Feedback) TableName() string {
	return "feedback"
}
