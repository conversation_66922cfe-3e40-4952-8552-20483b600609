package entities

// Folder folder 表实体
type Folder struct {
	Id	string	`gorm:"primaryKey;type:varchar(255)" json:"id"`
	ParentId	*string	`gorm:"type:text" json:"parent_id"`
	UserId	string	`gorm:"primaryKey;type:varchar(255)" json:"user_id"`
	Name	string	`gorm:"type:text;not null" json:"name"`
	Items	JSON	`gorm:"type:jsonb" json:"items"`
	Meta	JSON	`gorm:"type:jsonb" json:"meta"`
	IsExpanded	bool	`gorm:"not null" json:"is_expanded"`
	CreatedAt	int64	`gorm:"not null" json:"created_at"`
	UpdatedAt	int64	`gorm:"not null" json:"updated_at"`
}

// TableName 设置表名
func (Folder) TableName() string {
	return "folder"
}
