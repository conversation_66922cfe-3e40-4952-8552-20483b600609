package entities

// Document document 表实体
type Document struct {
	Id	int64	`gorm:"primaryKey" json:"id"`
	CollectionName	string	`gorm:"type:varchar(255);not null" json:"collection_name"`
	Name	string	`gorm:"type:varchar(255);not null" json:"name"`
	Title	string	`gorm:"type:text;not null" json:"title"`
	Filename	string	`gorm:"type:text;not null" json:"filename"`
	Content	*string	`gorm:"type:text" json:"content"`
	UserId	string	`gorm:"type:varchar(255);not null" json:"user_id"`
	Timestamp	int64	`gorm:"not null" json:"timestamp"`
}

// TableName 设置表名
func (Document) TableName() string {
	return "document"
}
