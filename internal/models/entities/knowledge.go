package entities

// Knowledge knowledge 表实体
type Knowledge struct {
	Id	string	`gorm:"primaryKey;type:varchar(255)" json:"id"`
	UserId	string	`gorm:"type:text;not null" json:"user_id"`
	Name	string	`gorm:"type:text;not null" json:"name"`
	Description	*string	`gorm:"type:text" json:"description"`
	Data	JSON	`gorm:"type:jsonb" json:"data"`
	Meta	JSON	`gorm:"type:jsonb" json:"meta"`
	CreatedAt	int64	`gorm:"not null" json:"created_at"`
	UpdatedAt	*int64	`json:"updated_at"`
	AccessControl	JSON	`gorm:"type:jsonb" json:"access_control"`
}

// TableName 设置表名
func (Knowledge) TableName() string {
	return "knowledge"
}
