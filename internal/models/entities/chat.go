package entities

// Chat chat 表实体
type Chat struct {
	Id	string	`gorm:"type:text;not null" json:"id"`
	UserId	string	`gorm:"type:varchar(255);not null" json:"user_id"`
	Title	string	`gorm:"type:text;not null" json:"title"`
	ShareId	*string	`gorm:"type:varchar(255)" json:"share_id"`
	Archived	bool	`gorm:"not null" json:"archived"`
	CreatedAt	int64	`gorm:"not null" json:"created_at"`
	UpdatedAt	int64	`gorm:"not null" json:"updated_at"`
	Chat	JSON	`gorm:"type:jsonb" json:"chat"`
	Pinned	*bool	`json:"pinned"`
	Meta	JSON	`gorm:"type:jsonb;not null" json:"meta"`
	FolderId	*string	`gorm:"type:text" json:"folder_id"`
}

// TableName 设置表名
func (Chat) TableName() string {
	return "chat"
}
