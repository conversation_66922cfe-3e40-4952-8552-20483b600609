package models

import (
	"hkchat_api/internal/models/entities"
)

// GetAllModels 获取所有需要迁移的模型
// 用于 GORM AutoMigrate 自动创建表结构
func GetAllModels() []interface{} {
	return []interface{}{
		&entities.User{},     // 用户表
		&entities.Auth{},     // 认证表
		&entities.Chat{},     // 聊天表
		&entities.Message{},  // 消息表
		&entities.Channel{},  // 频道表
		&entities.Document{}, // 文档表
		&entities.Model{},    // 模型表
		&entities.Tag{},      // 标签表
		&entities.Memory{},   // 内存表
	}
}

// GetModelNames 获取所有模型名称（用于调试）
func GetModelNames() []string {
	return []string{
		"User",
		"Auth",
		"Chat",
		"Message",
		"Channel",
		"Document",
		"Model",
		"Tag",
		"Memory",
	}
}
