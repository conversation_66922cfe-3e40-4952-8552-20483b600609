package config

import "time"

// AppConfiguration 应用配置结构（对应数据库中的JSON数据）
type AppConfiguration struct {
	Version    int                 `json:"version"`
	UI         UIConfig            `json:"ui"`
	OpenAI     AppOpenAIConfig     `json:"openai"`
	Auth       AppAuthConfig       `json:"auth"`
	RAG        RAGConfig           `json:"rag"`
	Audio      AudioConfig         `json:"audio"`
	WebUI      WebUIConfig         `json:"webui"`
	Direct     DirectConfig        `json:"direct"`
	Ollama     OllamaConfig        `json:"ollama"`
	ToolServer ToolServerConfig    `json:"tool_server"`
	Webhook    string              `json:"webhook_url"`
	CodeExec   CodeExecutionConfig `json:"code_execution"`
	ImageGen   ImageGenConfig      `json:"image_generation"`
	User       UserConfig          `json:"user"`
	Channels   ChannelsConfig      `json:"channels"`
	Notes      NotesConfig         `json:"notes"`
	Evaluation EvaluationConfig    `json:"evaluation"`
	Models     ModelsConfig        `json:"models"`
}

// UIConfig UI配置
type UIConfig struct {
	Banners                   []Banner           `json:"banners"`
	Watermark                 string             `json:"watermark"`
	EnableSignup              bool               `json:"enable_signup"`
	DefaultUserRole           string             `json:"default_user_role"`
	PromptSuggestions         []PromptSuggestion `json:"prompt_suggestions"`
	EnableUserWebhooks        bool               `json:"enable_user_webhooks"`
	EnableMessageRating       bool               `json:"enable_message_rating"`
	EnableCommunitySharing    bool               `json:"enable_community_sharing"`
	PendingUserOverlayTitle   string             `json:"pending_user_overlay_title"`
	PendingUserOverlayContent string             `json:"pending_user_overlay_content"`
}

// AppOpenAIConfig OpenAI配置（避免与现有OpenAIConfig冲突）
type AppOpenAIConfig struct {
	Enable      bool                          `json:"enable"`
	APIKeys     []string                      `json:"api_keys"`
	APIBaseURLs []string                      `json:"api_base_urls"`
	APIConfigs  map[string]AppOpenAIAPIConfig `json:"api_configs"`
}

// AppOpenAIAPIConfig OpenAI API具体配置
type AppOpenAIAPIConfig struct {
	Tags           []string `json:"tags"`
	Enable         bool     `json:"enable"`
	ModelIDs       []string `json:"model_ids"`
	PrefixID       string   `json:"prefix_id"`
	ConnectionType string   `json:"connection_type"`
}

// AppAuthConfig 认证配置（避免与可能的AuthConfig冲突）
type AppAuthConfig struct {
	Admin     AdminConfig  `json:"admin"`
	APIKey    APIKeyConfig `json:"api_key"`
	JWTExpiry string       `json:"jwt_expiry"`
}

// AdminConfig 管理员配置
type AdminConfig struct {
	Show bool `json:"show"`
}

// APIKeyConfig API Key配置
type APIKeyConfig struct {
	Enable               bool   `json:"enable"`
	AllowedEndpoints     string `json:"allowed_endpoints"`
	EndpointRestrictions bool   `json:"endpoint_restrictions"`
}

// RAGConfig RAG配置
type RAGConfig struct {
	Web                         WebSearchConfig `json:"web"`
	File                        FileConfig      `json:"file"`
	TopK                        int             `json:"top_k"`
	Template                    string          `json:"template"`
	ChunkSize                   int             `json:"chunk_size"`
	FullContext                 bool            `json:"full_context"`
	ChunkOverlap                int             `json:"chunk_overlap"`
	TextSplitter                string          `json:"text_splitter"`
	TopKReranker                int             `json:"top_k_reranker"`
	EmbeddingModel              string          `json:"embedding_model"`
	RelevanceThreshold          float64         `json:"relevance_threshold"`
	EnableHybridSearch          bool            `json:"enable_hybrid_search"`
	BypassEmbeddingAndRetrieval bool            `json:"bypass_embedding_and_retrieval"`
}

// WebSearchConfig 网页搜索配置
type WebSearchConfig struct {
	Search SearchConfig `json:"search"`
	Loader LoaderConfig `json:"loader"`
}

// SearchConfig 搜索配置
type SearchConfig struct {
	Enable                      bool         `json:"enable"`
	Engine                      string       `json:"engine"`
	TavilyAPIKey                string       `json:"tavily_api_key"`
	ResultCount                 int          `json:"result_count"`
	BypassEmbeddingAndRetrieval bool         `json:"bypass_embedding_and_retrieval"`
	Domain                      DomainConfig `json:"domain"`
}

// DomainConfig 域名配置
type DomainConfig struct {
	FilterList []string `json:"filter_list"`
}

// LoaderConfig 加载器配置
type LoaderConfig struct {
	Engine                  string `json:"engine"`
	SSLVerification         bool   `json:"ssl_verification"`
	FirecrawlAPIKey         string `json:"firecrawl_api_key"`
	ExternalWebLoaderURL    string `json:"external_web_loader_url"`
	ExternalWebLoaderAPIKey string `json:"external_web_loader_api_key"`
}

// FileConfig 文件配置
type FileConfig struct {
	MaxSize           int      `json:"max_size"`
	MaxCount          int      `json:"max_count"`
	AllowedExtensions []string `json:"allowed_extensions"`
}

// AudioConfig 音频配置
type AudioConfig struct {
	STT STTConfig `json:"stt"`
	TTS TTSConfig `json:"tts"`
}

// STTConfig 语音转文本配置
type STTConfig struct {
	Engine string               `json:"engine"`
	Model  string               `json:"model"`
	OpenAI AppOpenAIAudioConfig `json:"openai"`
}

// TTSConfig 文本转语音配置
type TTSConfig struct {
	Engine string               `json:"engine"`
	Model  string               `json:"model"`
	Voice  string               `json:"voice"`
	OpenAI AppOpenAIAudioConfig `json:"openai"`
}

// AppOpenAIAudioConfig OpenAI音频配置
type AppOpenAIAudioConfig struct {
	APIKey     string `json:"api_key"`
	APIBaseURL string `json:"api_base_url"`
}

// WebUIConfig WebUI配置
type WebUIConfig struct {
	URL string `json:"url"`
}

// DirectConfig 直连配置
type DirectConfig struct {
	Enable bool `json:"enable"`
}

// OllamaConfig Ollama配置
type OllamaConfig struct {
	Enable     bool                       `json:"enable"`
	BaseURLs   []string                   `json:"base_urls"`
	APIConfigs map[string]OllamaAPIConfig `json:"api_configs"`
}

// OllamaAPIConfig Ollama API配置
type OllamaAPIConfig struct {
	// 根据需要添加具体字段
}

// ToolServerConfig 工具服务器配置
type ToolServerConfig struct {
	Connections []ToolConnection `json:"connections"`
}

// ToolConnection 工具连接配置
type ToolConnection struct {
	Key      string               `json:"key"`
	URL      string               `json:"url"`
	Path     string               `json:"path"`
	Specs    []ToolSpec           `json:"specs"`
	Config   ToolConnectionConfig `json:"config"`
	AuthType string               `json:"auth_type"`
}

// ToolSpec 工具规格
type ToolSpec struct {
	Name        string                 `json:"name"`
	Type        string                 `json:"type"`
	Parameters  map[string]interface{} `json:"parameters"`
	Description string                 `json:"description"`
}

// ToolConnectionConfig 工具连接配置
type ToolConnectionConfig struct {
	Enable        bool                   `json:"enable"`
	AccessControl map[string]interface{} `json:"access_control"`
}

// CodeExecutionConfig 代码执行配置
type CodeExecutionConfig struct {
	Enable  bool          `json:"enable"`
	Engine  string        `json:"engine"`
	Jupyter JupyterConfig `json:"jupyter"`
}

// JupyterConfig Jupyter配置
type JupyterConfig struct {
	URL          string `json:"url"`
	Auth         string `json:"auth"`
	Timeout      int    `json:"timeout"`
	AuthToken    string `json:"auth_token"`
	AuthPassword string `json:"auth_password"`
}

// ImageGenConfig 图像生成配置
type ImageGenConfig struct {
	Enable bool   `json:"enable"`
	Engine string `json:"engine"`
}

// UserConfig 用户配置
type UserConfig struct {
	Permissions UserPermissions `json:"permissions"`
}

// UserPermissions 用户权限
type UserPermissions struct {
	Chat     ChatPermissions    `json:"chat"`
	Features FeaturePermissions `json:"features"`
}

// ChatPermissions 聊天权限
type ChatPermissions struct {
	STT        bool `json:"stt"`
	TTS        bool `json:"tts"`
	Call       bool `json:"call"`
	Edit       bool `json:"edit"`
	Share      bool `json:"share"`
	Delete     bool `json:"delete"`
	Export     bool `json:"export"`
	Controls   bool `json:"controls"`
	FileUpload bool `json:"file_upload"`
}

// FeaturePermissions 功能权限
type FeaturePermissions struct {
	WebSearch       bool `json:"web_search"`
	CodeInterpreter bool `json:"code_interpreter"`
	ImageGeneration bool `json:"image_generation"`
}

// ChannelsConfig 频道配置
type ChannelsConfig struct {
	Enable bool `json:"enable"`
}

// NotesConfig 笔记配置
type NotesConfig struct {
	Enable bool `json:"enable"`
}

// EvaluationConfig 评估配置
type EvaluationConfig struct {
	Arena ArenaConfig `json:"arena"`
}

// ArenaConfig Arena配置
type ArenaConfig struct {
	Enable bool     `json:"enable"`
	Models []string `json:"models"`
}

// Banner 横幅配置
type Banner struct {
	ID          string `json:"id"`
	Type        string `json:"type"`
	Title       string `json:"title"`
	Content     string `json:"content"`
	Dismissible bool   `json:"dismissible"`
	Timestamp   int64  `json:"timestamp"`
}

// PromptSuggestion 提示建议
type PromptSuggestion struct {
	Title   interface{} `json:"title"`
	Content interface{} `json:"content"`
}

// ConfigSnapshot 配置快照（用于版本管理）
type ConfigSnapshot struct {
	ID        int64             `json:"id"`
	Config    *AppConfiguration `json:"config"`
	Version   int               `json:"version"`
	CreatedAt time.Time         `json:"created_at"`
	CreatedBy string            `json:"created_by"`
	Comment   string            `json:"comment"`
}

// ModelsConfig 模型配置
type ModelsConfig struct {
	DefaultSystemPrompt string                 `json:"default_system_prompt"`
	ModelConfigs        map[string]ModelConfig `json:"model_configs"`
}

// ModelConfig 单个模型配置
type ModelConfig struct {
	ID           string  `json:"id"`
	Name         string  `json:"name"`
	SystemPrompt string  `json:"system_prompt"`
	Description  string  `json:"description"`
	Provider     string  `json:"provider"`
	Enabled      bool    `json:"enabled"`
	MaxTokens    int     `json:"max_tokens"`
	Temperature  float64 `json:"temperature"`
}
