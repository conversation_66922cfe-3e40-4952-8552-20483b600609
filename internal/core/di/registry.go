package di

import (
	"fmt"
	"hkchat_api/internal/config"
	"hkchat_api/internal/core/di/base"
	"hkchat_api/internal/core/llm"
	"hkchat_api/internal/repository"
	"hkchat_api/pkg/clients/database"
	"hkchat_api/pkg/utils"
)

// GlobalContainer 全局容器实例
var GlobalContainer *Container

// CreateMinimalDI 创建最小化的DI容器
func CreateMinimalDI() (*Container, error) {
	utils.Debug("🏗️ 创建最小化DI容器...")

	// 初始化全局容器
	if err := InitializeContainer(); err != nil {
		return nil, err
	}

	// 使用全局容器
	return GlobalContainer, nil
}

// InitializeContainer 初始化全局容器
func InitializeContainer() error {
	utils.Info("🚀 开始初始化依赖注入容器...")

	// 创建容器
	GlobalContainer = NewContainer()

	// 注册核心组件
	if err := registerCoreComponents(); err != nil {
		return err
	}

	// 注册业务服务
	if err := RegisterBusinessServices(); err != nil {
		return err
	}

	// 注册Repository
	if err := RegisterRepositories(); err != nil {
		return err
	}

	// 注册客户端
	if err := RegisterClients(); err != nil {
		return err
	}

	// 设置依赖关系
	if err := setupDependencies(); err != nil {
		return err
	}

	// 初始化所有组件和服务
	if err := GlobalContainer.Initialize(); err != nil {
		return err
	}

	utils.Info("✅ 依赖注入容器初始化完成")
	return nil
}

// registerCoreComponents 注册核心组件
func registerCoreComponents() error {
	utils.Debug("🔧 注册核心组件...")

	// 1. 注册配置管理器
	// 首先创建配置相关的依赖链
	db := database.GetGlobalDB()
	if db == nil {
		utils.Warn("⚠️ 数据库连接未初始化，无法注册配置管理器")
		return fmt.Errorf("数据库连接未初始化，无法注册核心组件")
	}
	utils.Debug("   ✅ 数据库连接已初始化")

	// 创建配置仓储
	configRepo := repository.NewConfigRepository(db)

	// 创建配置服务
	configSvc := config.NewService(configRepo)

	// 创建配置管理器
	configManager := config.NewManager(configSvc)

	configComponent := NewConfigManagerComponent(configManager)
	globalConfigManagerComponent = configComponent // 保存全局引用

	if err := GlobalContainer.RegisterCoreComponent("ConfigManager", configComponent.BaseCoreComponent); err != nil {
		return err
	}

	utils.Debug("✅ 核心组件注册完成")
	return nil
}

// setupDependencies 设置依赖关系
func setupDependencies() error {
	utils.Debug("🔗 设置依赖关系...")

	// 设置业务服务依赖
	if err := SetupBusinessServiceDependencies(); err != nil {
		return err
	}

	utils.Debug("✅ 依赖关系设置完成")
	return nil
}

// ConfigManagerComponent 配置管理器组件包装
type ConfigManagerComponent struct {
	*base.BaseCoreComponent
	Manager *config.Manager
}

// NewConfigManagerComponent 创建配置管理器组件
func NewConfigManagerComponent(manager *config.Manager) *ConfigManagerComponent {
	return &ConfigManagerComponent{
		BaseCoreComponent: base.NewBaseCoreComponent(
			"ConfigManager",
			"1.0.0",
			base.ComponentTypeConfig,
		),
		Manager: manager,
	}
}

// Initialize 初始化配置管理器组件
func (c *ConfigManagerComponent) Initialize() error {
	return c.ExecuteOperation("Initialize", func() error {
		if err := c.BaseCoreComponent.Initialize(); err != nil {
			return err
		}

		// 加载应用配置（如果Manager有此方法）
		if c.Manager != nil {
			if err := c.Manager.LoadAppConfig(); err != nil {
				utils.Warnf("加载应用配置失败: %v", err)
				// 不影响组件初始化，只记录警告
			}
		}

		c.SetHealthy(true)
		return nil
	})
}

// GetConfigManager 获取配置管理器
func (c *ConfigManagerComponent) GetConfigManager() *config.Manager {
	return c.Manager
}

// 全局LLM网关实例（用于依赖注入）
var globalLLMGateway *llm.Gateway

// 全局配置管理器组件实例（用于依赖注入）
var globalConfigManagerComponent *ConfigManagerComponent

// RegisterBusinessServices 注册业务服务 - 移除对services的依赖
func RegisterBusinessServices() error {
	utils.Debug("🎯 注册业务服务...")

	// 注册LLM网关将在 SetupBusinessServiceDependencies 中完成
	// 因为需要先有配置管理器

	// 暂时不注册ChatService，因为它会导致循环导入
	// ChatService将通过其他方式注册

	utils.Debug("✅ 业务服务注册完成")
	return nil
}

// RegisterRepositories 注册Repository
func RegisterRepositories() error {
	utils.Debug("🗄️ 注册Repository...")

	// 获取数据库连接
	db := database.GetGlobalDB()
	if db == nil {
		utils.Warn("⚠️ 数据库连接未初始化，无法注册Repository")
		return fmt.Errorf("数据库连接未初始化，无法注册Repository")
	}
	utils.Debug("   ✅ 数据库连接已初始化")

	// 注册配置Repository
	configRepo := repository.NewConfigRepository(db)
	if err := GlobalContainer.RegisterRepository("ConfigRepository", configRepo); err != nil {
		return err
	}

	// 注册用户Repository
	userRepo := repository.NewUserRepository(db)
	if err := GlobalContainer.RegisterRepository("UserRepository", userRepo); err != nil {
		return err
	}

	// 注册模型Repository
	modelRepo := repository.NewModelRepository(db)
	if err := GlobalContainer.RegisterRepository("ModelRepository", modelRepo); err != nil {
		return err
	}

	// 注册其他常用的Repository
	chatRepo := repository.NewChatRepository(db)
	if err := GlobalContainer.RegisterRepository("ChatRepository", chatRepo); err != nil {
		return err
	}

	messageRepo := repository.NewMessageRepository(db)
	if err := GlobalContainer.RegisterRepository("MessageRepository", messageRepo); err != nil {
		return err
	}

	// 注册到内部DI容器，以便GetByName方法能够找到
	diInstance := GlobalContainer.GetDI()
	diInstance.RegisterRepository(userRepo)
	diInstance.RegisterRepository(modelRepo)
	diInstance.RegisterRepository(configRepo)
	diInstance.RegisterRepository(chatRepo)
	diInstance.RegisterRepository(messageRepo)

	utils.Debug("✅ Repository注册完成")
	return nil
}

// RegisterClients 注册客户端
func RegisterClients() error {
	utils.Debug("🔌 注册客户端...")

	// 注册数据库客户端
	// dbClient := database.NewClient()
	// if err := GlobalContainer.RegisterClient("DatabaseClient", dbClient); err != nil {
	// 	return err
	// }

	// 注册Redis客户端
	// redisClient := redis.NewClient()
	// if err := GlobalContainer.RegisterClient("RedisClient", redisClient); err != nil {
	// 	return err
	// }

	utils.Debug("✅ 客户端注册完成")
	return nil
}

// SetupBusinessServiceDependencies 设置业务服务依赖
func SetupBusinessServiceDependencies() error {
	utils.Debug("🎯 设置业务服务依赖...")

	// 使用全局配置管理器组件创建LLM网关
	utils.Debug("🔍 检查全局配置管理器组件...")
	if globalConfigManagerComponent != nil {
		utils.Debug("   ✅ 全局配置管理器组件存在")

		// 获取实际的配置管理器实例
		utils.Debug("🔍 获取配置管理器实例...")
		configManager := globalConfigManagerComponent.GetConfigManager()
		if configManager == nil {
			utils.Warn("   ❌ 配置管理器实例为空")
			return fmt.Errorf("配置管理器实例为空")
		}
		utils.Debug("   ✅ 配置管理器实例获取成功")

		utils.Debug("🔍 正在创建LLM网关...")
		globalLLMGateway = llm.NewGateway(configManager)
		if globalLLMGateway == nil {
			utils.Warn("   ❌ LLM网关创建失败")
			return fmt.Errorf("创建LLM网关失败")
		}
		utils.Debug("   ✅ LLM网关创建成功")

		// 初始化网关
		utils.Debug("🔍 正在初始化LLM网关...")
		if err := globalLLMGateway.Initialize(); err != nil {
			utils.Warnf("   ❌ LLM网关初始化失败: %v", err)
			return fmt.Errorf("初始化LLM网关失败: %v", err)
		}
		utils.Debug("   ✅ LLM网关初始化成功")

		utils.Debug("✅ LLM网关创建和初始化完成")
	} else {
		utils.Warn("⚠️ 全局配置管理器组件为空，无法创建LLM网关")
		utils.Debug("🔍 检查是否已经调用了registerCoreComponents...")
		return fmt.Errorf("全局配置管理器组件为空")
	}

	utils.Debug("✅ 业务服务依赖设置完成")
	return nil
}

// GetCoreComponent 获取核心组件（全局访问方法）
func GetCoreComponent(name string) interface{} {
	if GlobalContainer == nil {
		utils.Warn("⚠️ 全局容器未初始化")
		return nil
	}

	// 特殊处理ConfigManager，直接返回全局引用
	if name == "ConfigManager" && globalConfigManagerComponent != nil {
		utils.Debug("   ✅ 通过全局引用获取ConfigManager组件")
		return globalConfigManagerComponent
	}

	component, err := GlobalContainer.GetCoreComponent(name)
	if err != nil {
		utils.Warnf("⚠️ 获取核心组件失败: %v", err)
		return nil
	}

	return component
}

// GetBusinessService 获取业务服务（全局访问方法）
func GetBusinessService(name string) interface{} {
	if GlobalContainer == nil {
		utils.Warn("⚠️ 全局容器未初始化")
		return nil
	}

	service, err := GlobalContainer.GetBusinessService(name)
	if err != nil {
		utils.Warnf("⚠️ 获取业务服务失败: %v", err)
		return nil
	}

	return service
}

// GetGlobalLLMGateway 获取全局LLM网关（全局访问方法）
func GetGlobalLLMGateway() *llm.Gateway {
	if globalLLMGateway == nil {
		utils.Warn("⚠️ 全局LLM网关为空，可能尚未初始化")
		utils.Debug("🔍 检查全局配置管理器组件状态...")
		if globalConfigManagerComponent == nil {
			utils.Warn("   ❌ 全局配置管理器组件为空")
		} else {
			utils.Debug("   ✅ 全局配置管理器组件存在")
		}
		utils.Debug("🔍 检查全局容器状态...")
		if GlobalContainer == nil {
			utils.Warn("   ❌ 全局容器为空")
		} else {
			utils.Debug("   ✅ 全局容器存在")
		}
	}
	return globalLLMGateway
}

// GetRepositoryByName 获取Repository（全局访问方法）
func GetRepositoryByName(name string) interface{} {
	if GlobalContainer == nil {
		utils.Warn("⚠️ 全局容器未初始化")
		return nil
	}

	repo, err := GlobalContainer.GetRepository(name)
	if err != nil {
		utils.Warnf("⚠️ 获取Repository失败: %v", err)
		return nil
	}

	return repo
}

// GetClient 获取客户端（全局访问方法）
func GetClient(name string) interface{} {
	if GlobalContainer == nil {
		utils.Warn("⚠️ 全局容器未初始化")
		return nil
	}

	client, err := GlobalContainer.GetClient(name)
	if err != nil {
		utils.Warnf("⚠️ 获取客户端失败: %v", err)
		return nil
	}

	return client
}

// PrintDependencyInjectionStatus 打印完整的依赖注入状态报告
func PrintDependencyInjectionStatus() {
	utils.Info("📋 === 依赖注入状态报告 ===")

	// 1. 检查全局容器
	utils.Info("🔍 检查全局容器...")
	if GlobalContainer == nil {
		utils.Errorf("   ❌ 全局容器为空")
		return
	}
	utils.Info("   ✅ 全局容器存在")

	// 2. 检查数据库连接
	utils.Info("🔍 检查数据库连接...")
	if db := database.GetGlobalDB(); db != nil {
		utils.Info("   ✅ 数据库连接正常")
	} else {
		utils.Errorf("   ❌ 数据库连接为空")
	}

	// 3. 检查配置管理器组件
	utils.Info("🔍 检查配置管理器组件...")
	if globalConfigManagerComponent == nil {
		utils.Errorf("   ❌ 全局配置管理器组件为空")
	} else {
		utils.Info("   ✅ 全局配置管理器组件存在")
		if configManager := globalConfigManagerComponent.GetConfigManager(); configManager != nil {
			utils.Info("   ✅ 配置管理器实例存在")
		} else {
			utils.Errorf("   ❌ 配置管理器实例为空")
		}
	}

	// 4. 检查LLM网关
	utils.Info("🔍 检查LLM网关...")
	if globalLLMGateway == nil {
		utils.Errorf("   ❌ 全局LLM网关为空")
	} else {
		utils.Info("   ✅ 全局LLM网关存在")
	}

	// 5. 检查Repository
	utils.Info("🔍 检查Repository...")
	diInstance := GlobalContainer.GetDI()
	if diInstance != nil {
		if chatRepo := diInstance.GetByName("ChatRepository"); chatRepo != nil {
			utils.Info("   ✅ ChatRepository 存在")
		} else {
			utils.Errorf("   ❌ ChatRepository 为空")
		}

		if userRepo := diInstance.GetByName("UserRepository"); userRepo != nil {
			utils.Info("   ✅ UserRepository 存在")
		} else {
			utils.Errorf("   ❌ UserRepository 为空")
		}

		if modelRepo := diInstance.GetByName("ModelRepository"); modelRepo != nil {
			utils.Info("   ✅ ModelRepository 存在")
		} else {
			utils.Errorf("   ❌ ModelRepository 为空")
		}
	} else {
		utils.Errorf("   ❌ DI实例为空")
	}

	// 6. 统计信息
	if diInstance != nil {
		stats := diInstance.GetStats()
		utils.Infof("📊 DI统计信息: 仓储=%d, 服务=%d, 处理器=%d, 总计=%d",
			stats["repositories"], stats["services"], stats["handlers"], stats["total"])
	}

	utils.Info("📋 === 依赖注入状态报告结束 ===")
}
