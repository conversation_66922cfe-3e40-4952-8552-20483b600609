package di

import (
	"context"
	"fmt"
	"hkchat_api/internal/core/di/base"
	"hkchat_api/pkg/clients/database"
	"hkchat_api/pkg/utils"
	"sync"

	"gorm.io/gorm"
)

// Container 依赖注入容器
type Container struct {
	// 核心组件注册表
	coreComponents map[string]*base.BaseCoreComponent

	// 业务服务注册表（现在称为核心服务）
	coreServices map[string]*base.BaseCoreService

	// Repository注册表
	repositories map[string]interface{}

	// 客户端注册表
	clients map[string]interface{}

	// DI实例
	di *DI

	// 容器状态
	isInitialized bool
	mutex         sync.RWMutex
}

// NewContainer 创建容器实例
func NewContainer() *Container {
	// 获取全局数据库连接
	db := getGlobalDatabase()

	return &Container{
		coreComponents: make(map[string]*base.BaseCoreComponent),
		coreServices:   make(map[string]*base.BaseCoreService),
		repositories:   make(map[string]interface{}),
		clients:        make(map[string]interface{}),
		di:             NewDI(db), // 创建DI实例，传入数据库连接
		isInitialized:  false,
	}
}

// NewContainerWithDB 创建带数据库连接的容器实例
func NewContainerWithDB(db *gorm.DB) *Container {
	return &Container{
		coreComponents: make(map[string]*base.BaseCoreComponent),
		coreServices:   make(map[string]*base.BaseCoreService),
		repositories:   make(map[string]interface{}),
		clients:        make(map[string]interface{}),
		di:             NewDI(db), // 创建DI实例，传入数据库连接
		isInitialized:  false,
	}
}

// GetDI 获取DI实例
func (c *Container) GetDI() *DI {
	return c.di
}

// RegisterCoreComponent 注册核心组件
func (c *Container) RegisterCoreComponent(name string, component *base.BaseCoreComponent) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if _, exists := c.coreComponents[name]; exists {
		return fmt.Errorf("核心组件 %s 已存在", name)
	}

	c.coreComponents[name] = component
	utils.Debugf("🔧 注册核心组件: %s", name)
	return nil
}

// GetCoreComponent 获取核心组件
func (c *Container) GetCoreComponent(name string) (*base.BaseCoreComponent, error) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	component, exists := c.coreComponents[name]
	if !exists {
		return nil, fmt.Errorf("核心组件 %s 不存在", name)
	}

	return component, nil
}

// RegisterCoreService 注册核心服务（原业务服务）
func (c *Container) RegisterCoreService(name string, service *base.BaseCoreService) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if _, exists := c.coreServices[name]; exists {
		return fmt.Errorf("核心服务 %s 已存在", name)
	}

	c.coreServices[name] = service
	utils.Debugf("🎯 注册核心服务: %s", name)
	return nil
}

// GetCoreService 获取核心服务
func (c *Container) GetCoreService(name string) (*base.BaseCoreService, error) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	service, exists := c.coreServices[name]
	if !exists {
		return nil, fmt.Errorf("核心服务 %s 不存在", name)
	}

	return service, nil
}

// RegisterBusinessService 注册业务服务（向后兼容）
func (c *Container) RegisterBusinessService(name string, service *base.BaseCoreService) error {
	return c.RegisterCoreService(name, service)
}

// GetBusinessService 获取业务服务（向后兼容）
func (c *Container) GetBusinessService(name string) (*base.BaseCoreService, error) {
	return c.GetCoreService(name)
}

// RegisterRepository 注册Repository
func (c *Container) RegisterRepository(name string, repo interface{}) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if _, exists := c.repositories[name]; exists {
		return fmt.Errorf("Repository %s 已存在", name)
	}

	c.repositories[name] = repo
	utils.Debugf("🗄️ 注册Repository: %s", name)
	return nil
}

// GetRepository 获取Repository
func (c *Container) GetRepository(name string) (interface{}, error) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	repo, exists := c.repositories[name]
	if !exists {
		return nil, fmt.Errorf("Repository %s 不存在", name)
	}

	return repo, nil
}

// RegisterClient 注册客户端
func (c *Container) RegisterClient(name string, client interface{}) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if _, exists := c.clients[name]; exists {
		return fmt.Errorf("客户端 %s 已存在", name)
	}

	c.clients[name] = client
	utils.Debugf("🔌 注册客户端: %s", name)
	return nil
}

// GetClient 获取客户端
func (c *Container) GetClient(name string) (interface{}, error) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	client, exists := c.clients[name]
	if !exists {
		return nil, fmt.Errorf("客户端 %s 不存在", name)
	}

	return client, nil
}

// Initialize 初始化容器中的所有组件和服务
func (c *Container) Initialize() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.isInitialized {
		return nil
	}

	utils.Debug("🚀 开始初始化容器中的所有组件和服务...")

	// 1. 初始化核心组件（按依赖顺序）
	if err := c.initializeCoreComponents(); err != nil {
		return fmt.Errorf("初始化核心组件失败: %v", err)
	}

	// 2. 初始化核心服务
	if err := c.initializeCoreServices(); err != nil {
		return fmt.Errorf("初始化核心服务失败: %v", err)
	}

	c.isInitialized = true
	utils.Debug("✅ 容器初始化完成")
	return nil
}

// initializeCoreComponents 初始化核心组件
func (c *Container) initializeCoreComponents() error {
	utils.Debug("🔧 初始化核心组件...")

	// 简单策略：尝试多次初始化，直到所有组件都初始化完成
	maxAttempts := 3
	for attempt := 1; attempt <= maxAttempts; attempt++ {
		allInitialized := true

		for name, component := range c.coreComponents {
			if err := component.Initialize(); err != nil {
				utils.Warnf("核心组件 %s 初始化失败 (尝试 %d/%d): %v", name, attempt, maxAttempts, err)
				allInitialized = false
			}
		}

		if allInitialized {
			utils.Debug("✅ 所有核心组件初始化完成")
			return nil
		}

		if attempt < maxAttempts {
			utils.Debugf("🔄 第 %d 次尝试未完全成功，准备重试...", attempt)
		}
	}

	return fmt.Errorf("核心组件初始化失败，已尝试 %d 次", maxAttempts)
}

// initializeCoreServices 初始化核心服务（原业务服务）
func (c *Container) initializeCoreServices() error {
	utils.Debug("🎯 初始化核心服务...")

	for name, service := range c.coreServices {
		if err := service.Initialize(); err != nil {
			return fmt.Errorf("核心服务 %s 初始化失败: %v", name, err)
		}
	}

	utils.Debug("✅ 所有核心服务初始化完成")
	return nil
}

// initializeBusinessServices 初始化业务服务（向后兼容）
func (c *Container) initializeBusinessServices() error {
	return c.initializeCoreServices()
}

// HealthCheck 检查容器中所有组件和服务的健康状态
func (c *Container) HealthCheck(ctx context.Context) error {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	// 检查核心组件健康
	for name, component := range c.coreComponents {
		if err := component.HealthCheck(ctx); err != nil {
			return fmt.Errorf("核心组件 %s 不健康: %v", name, err)
		}
	}

	// 检查核心服务健康
	for name, service := range c.coreServices {
		if err := service.HealthCheck(ctx); err != nil {
			return fmt.Errorf("核心服务 %s 不健康: %v", name, err)
		}
	}

	return nil
}

// Shutdown 关闭容器中的所有组件和服务
func (c *Container) Shutdown(ctx context.Context) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	utils.Info("🔒 开始关闭容器中的所有组件和服务...")

	// 先关闭核心服务
	for name, service := range c.coreServices {
		if err := service.Shutdown(ctx); err != nil {
			utils.Errorf("关闭核心服务 %s 失败: %v", name, err)
		}
	}

	// 再关闭核心组件
	for name, component := range c.coreComponents {
		if err := component.Shutdown(ctx); err != nil {
			utils.Errorf("关闭核心组件 %s 失败: %v", name, err)
		}
	}

	utils.Info("✅ 容器关闭完成")
	return nil
}

// GetContainerStatus 获取容器状态
func (c *Container) GetContainerStatus() map[string]interface{} {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	return map[string]interface{}{
		"initialized":     c.isInitialized,
		"core_components": len(c.coreComponents),
		"core_services":   len(c.coreServices),
		"repositories":    len(c.repositories),
		"clients":         len(c.clients),
	}
}

// ListComponents 列出所有已注册的组件和服务
func (c *Container) ListComponents() map[string][]string {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	result := map[string][]string{
		"core_components": {},
		"core_services":   {},
		"repositories":    {},
		"clients":         {},
	}

	for name := range c.coreComponents {
		result["core_components"] = append(result["core_components"], name)
	}

	for name := range c.coreServices {
		result["core_services"] = append(result["core_services"], name)
	}

	for name := range c.repositories {
		result["repositories"] = append(result["repositories"], name)
	}

	for name := range c.clients {
		result["clients"] = append(result["clients"], name)
	}

	return result
}

// getGlobalDatabase 获取全局数据库连接
func getGlobalDatabase() *gorm.DB {
	// 通过database包获取全局数据库连接
	return database.GetGlobalDB()
}
