package di

import (
	"context"
	"fmt"
	"reflect"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// DI 核心依赖注入容器
type DI struct {
	services     map[reflect.Type]interface{}
	repositories map[reflect.Type]interface{}
	handlers     map[reflect.Type]interface{}
	db           *gorm.DB
	router       *gin.Engine
}

// NewDI 创建DI容器
func NewDI(db *gorm.DB) *DI {
	return &DI{
		services:     make(map[reflect.Type]interface{}),
		repositories: make(map[reflect.Type]interface{}),
		handlers:     make(map[reflect.Type]interface{}),
		db:           db,
		router:       gin.Default(),
	}
}

// RegisterService 注册服务 - 使用泛型避免硬编码
func (d *DI) RegisterService(service interface{}) {
	serviceType := reflect.TypeOf(service)
	d.services[serviceType] = service
}

// RegisterRepository 注册仓储 - 使用泛型避免硬编码
func (d *DI) RegisterRepository(repo interface{}) {
	repoType := reflect.TypeOf(repo)
	d.repositories[repoType] = repo
}

// RegisterHandler 注册处理器 - 使用泛型避免硬编码
func (d *DI) RegisterHandler(handler interface{}) {
	handlerType := reflect.TypeOf(handler)
	d.handlers[handlerType] = handler
}

// RegisterByInterface 通过接口注册组件（非泛型版本）
func (d *DI) RegisterByInterface(iface interface{}, implementation interface{}) error {
	interfaceType := reflect.TypeOf(iface).Elem()

	// 检查实现是否满足接口
	implType := reflect.TypeOf(implementation)
	if !implType.Implements(interfaceType) {
		return fmt.Errorf("类型 %v 不实现接口 %v", implType, interfaceType)
	}

	// 根据接口名称判断应该注册到哪个容器
	typeName := interfaceType.Name()
	if isRepositoryInterface(typeName) {
		d.repositories[interfaceType] = implementation
	} else if isHandlerInterface(typeName) {
		d.handlers[interfaceType] = implementation
	} else {
		// 默认注册到服务容器
		d.services[interfaceType] = implementation
	}

	return nil
}

// GetByInterface 通过接口获取组件（非泛型版本）
func (d *DI) GetByInterface(iface interface{}) interface{} {
	interfaceType := reflect.TypeOf(iface).Elem()

	// 先从仓储中查找
	if component, exists := d.repositories[interfaceType]; exists {
		return component
	}

	// 再从服务中查找
	if component, exists := d.services[interfaceType]; exists {
		return component
	}

	// 最后从处理器中查找
	if component, exists := d.handlers[interfaceType]; exists {
		return component
	}

	return nil
}

// Has 检查是否有指定名称的组件
func (d *DI) Has(name string) bool {
	// 检查仓储
	for repoType := range d.repositories {
		if getTypeName(repoType) == name {
			return true
		}
	}

	// 检查服务
	for serviceType := range d.services {
		if getTypeName(serviceType) == name {
			return true
		}
	}

	// 检查处理器
	for handlerType := range d.handlers {
		if getTypeName(handlerType) == name {
			return true
		}
	}

	return false
}

// NewError 创建DI相关错误
func (d *DI) NewError(message string) error {
	return fmt.Errorf("DI Error: %s", message)
}

// GetStats 获取注册统计信息
func (d *DI) GetStats() map[string]int {
	return map[string]int{
		"repositories": len(d.repositories),
		"services":     len(d.services),
		"handlers":     len(d.handlers),
		"total":        len(d.repositories) + len(d.services) + len(d.handlers),
	}
}

// 辅助函数：判断是否为仓储接口
func isRepositoryInterface(typeName string) bool {
	return len(typeName) > 10 && typeName[len(typeName)-10:] == "Repository"
}

// 辅助函数：判断是否为处理器接口
func isHandlerInterface(typeName string) bool {
	return len(typeName) > 7 && (typeName[len(typeName)-7:] == "Handler" ||
		len(typeName) > 16 && typeName[len(typeName)-16:] == "HandlerInterface")
}

// 辅助函数：获取类型名称
func getTypeName(t reflect.Type) string {
	// 如果是指针类型，获取指向的元素类型
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}

	name := t.Name()
	// 移除Interface后缀
	if len(name) > 9 && name[len(name)-9:] == "Interface" {
		return name[:len(name)-9]
	}
	return name
}

// GetService 获取服务 - 使用泛型返回类型安全的服务
func GetService[T any](d *DI) T {
	var zero T
	serviceType := reflect.TypeOf((*T)(nil)).Elem()

	if service, exists := d.services[serviceType]; exists {
		if typedService, ok := service.(T); ok {
			return typedService
		}
	}

	return zero
}

// GetRepository 获取仓储 - 使用泛型返回类型安全的仓储
func GetRepository[T any](d *DI) T {
	var zero T
	repoType := reflect.TypeOf((*T)(nil)).Elem()

	if repo, exists := d.repositories[repoType]; exists {
		if typedRepo, ok := repo.(T); ok {
			return typedRepo
		}
	}

	return zero
}

// GetHandler 获取处理器 - 使用泛型返回类型安全的处理器
func GetHandler[T any](d *DI) T {
	var zero T
	handlerType := reflect.TypeOf((*T)(nil)).Elem()

	if handler, exists := d.handlers[handlerType]; exists {
		if typedHandler, ok := handler.(T); ok {
			return typedHandler
		}
	}

	return zero
}

// RegisterByInterface 通过接口类型注册服务
func RegisterByInterface[T any](d *DI, implementation interface{}) error {
	interfaceType := reflect.TypeOf((*T)(nil)).Elem()

	// 检查实现是否满足接口
	implType := reflect.TypeOf(implementation)
	if !implType.Implements(interfaceType) {
		return fmt.Errorf("类型 %v 不实现接口 %v", implType, interfaceType)
	}

	d.services[interfaceType] = implementation
	return nil
}

// RegisterHandlerByInterface 通过接口类型注册处理器
func RegisterHandlerByInterface[T any](d *DI, implementation interface{}) error {
	interfaceType := reflect.TypeOf((*T)(nil)).Elem()

	// 检查实现是否满足接口
	implType := reflect.TypeOf(implementation)
	if !implType.Implements(interfaceType) {
		return fmt.Errorf("类型 %v 不实现接口 %v", implType, interfaceType)
	}

	d.handlers[interfaceType] = implementation
	return nil
}

// GetDB 获取数据库
func (d *DI) GetDB() *gorm.DB {
	return d.db
}

// GetRouter 获取路由
func (d *DI) GetRouter() *gin.Engine {
	return d.router
}

// Close 关闭容器
func (d *DI) Close(ctx context.Context) error {
	return nil
}

// ListServices 列出所有已注册的服务
func (d *DI) ListServices() []string {
	var services []string
	for serviceType := range d.services {
		services = append(services, serviceType.String())
	}
	return services
}

// ListRepositories 列出所有已注册的仓储
func (d *DI) ListRepositories() []string {
	var repositories []string
	for repoType := range d.repositories {
		repositories = append(repositories, repoType.String())
	}
	return repositories
}

// ListHandlers 列出所有已注册的处理器
func (d *DI) ListHandlers() []string {
	var handlers []string
	for handlerType := range d.handlers {
		handlers = append(handlers, handlerType.String())
	}
	return handlers
}

// GetByName 根据名称获取已注册的组件
func (d *DI) GetByName(name string) interface{} {
	// 检查仓储
	for repoType, repo := range d.repositories {
		if getTypeName(repoType) == name {
			return repo
		}
	}

	// 检查服务
	for serviceType, service := range d.services {
		if getTypeName(serviceType) == name {
			return service
		}
	}

	// 检查处理器
	for handlerType, handler := range d.handlers {
		if getTypeName(handlerType) == name {
			return handler
		}
	}

	return nil
}

// RegisterByType 根据类型注册组件
func (d *DI) RegisterByType(interfaceType reflect.Type, implementation interface{}) error {
	// 检查实现是否满足接口
	implType := reflect.TypeOf(implementation)
	if !implType.Implements(interfaceType) {
		return fmt.Errorf("类型 %v 不实现接口 %v", implType, interfaceType)
	}

	// 根据接口名称判断应该注册到哪个容器
	typeName := interfaceType.Name()
	if isRepositoryInterface(typeName) {
		d.repositories[interfaceType] = implementation
	} else if isHandlerInterface(typeName) {
		d.handlers[interfaceType] = implementation
	} else {
		// 默认注册到服务容器
		d.services[interfaceType] = implementation
	}

	return nil
}
