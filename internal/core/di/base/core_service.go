package base

import (
	"context"
	"fmt"
	"hkchat_api/pkg/utils"
	"sync"
	"time"
)

// ServiceType 服务类型
type ServiceType string

const (
	ServiceTypeChat     ServiceType = "chat"
	ServiceTypeUser     ServiceType = "user"
	ServiceTypeDocument ServiceType = "document"
	ServiceTypeWorkflow ServiceType = "workflow"
)

// ServiceMetrics 服务指标
type ServiceMetrics struct {
	RequestCount    int64         `json:"request_count"`
	BusinessOpCount int64         `json:"business_op_count"`
	ErrorCount      int64         `json:"error_count"`
	SuccessCount    int64         `json:"success_count"`
	AvgResponseTime time.Duration `json:"avg_response_time"`
	LastRequest     time.Time     `json:"last_request"`
	LastBusinessOp  time.Time     `json:"last_business_op"`
	LastError       time.Time     `json:"last_error"`
}

// BaseCoreService 核心服务基础实现（原 BaseBusinessService）
type BaseCoreService struct {
	name          string
	version       string
	serviceType   ServiceType
	coreDeps      []*BaseCoreComponent
	isHealthy     bool
	isInitialized bool
	mutex         sync.RWMutex
	metrics       *ServiceMetrics
}

// NewBaseCoreService 创建核心服务实例（原 NewBaseBusinessService）
func NewBaseCoreService(name, version string, serviceType ServiceType) *BaseCoreService {
	return &BaseCoreService{
		name:          name,
		version:       version,
		serviceType:   serviceType,
		coreDeps:      []*BaseCoreComponent{},
		isHealthy:     true,
		isInitialized: false,
		metrics:       &ServiceMetrics{},
	}
}

// GetServiceName 获取服务名称
func (s *BaseCoreService) GetServiceName() string {
	return s.name
}

// GetServiceVersion 获取服务版本
func (s *BaseCoreService) GetServiceVersion() string {
	return s.version
}

// GetServiceType 获取服务类型
func (s *BaseCoreService) GetServiceType() ServiceType {
	return s.serviceType
}

// Initialize 初始化服务
func (s *BaseCoreService) Initialize() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if s.isInitialized {
		return nil
	}

	// 检查核心组件依赖是否已初始化
	for _, dep := range s.coreDeps {
		if err := dep.Initialize(); err != nil {
			return fmt.Errorf("核心组件依赖 %s 初始化失败: %v", dep.GetComponentName(), err)
		}
	}

	s.isInitialized = true
	utils.Debugf("🎯 核心服务 %s 初始化完成", s.name)
	return nil
}

// AddCoreDependency 添加核心组件依赖
func (s *BaseCoreService) AddCoreDependency(component *BaseCoreComponent) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	s.coreDeps = append(s.coreDeps, component)
	utils.Infof("🔗 核心服务 %s 添加核心组件依赖: %s", s.GetServiceName(), component.GetComponentName())
}

// GetCoreDependencies 获取核心组件依赖
func (s *BaseCoreService) GetCoreDependencies() []*BaseCoreComponent {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	return s.coreDeps
}

// HealthCheck 健康检查
func (s *BaseCoreService) HealthCheck(ctx context.Context) error {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	if !s.isInitialized {
		return fmt.Errorf("核心服务 %s 未初始化", s.name)
	}

	if !s.isHealthy {
		return fmt.Errorf("核心服务 %s 不健康", s.name)
	}

	// 检查核心组件依赖健康
	for _, dep := range s.coreDeps {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
			if err := dep.HealthCheck(ctx); err != nil {
				return fmt.Errorf("核心组件依赖 %s 不健康: %v", dep.GetComponentName(), err)
			}
		}
	}

	return nil
}

// Shutdown 关闭服务
func (s *BaseCoreService) Shutdown(ctx context.Context) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	utils.Infof("🔒 核心服务 %s 关闭", s.name)
	return nil
}

// SetHealthy 设置健康状态
func (s *BaseCoreService) SetHealthy(healthy bool) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	s.isHealthy = healthy
	if healthy {
		utils.Infof("✅ 核心服务 %s 状态正常", s.name)
	} else {
		utils.Warnf("❌ 核心服务 %s 状态异常", s.name)
	}
}

// RecordRequest 记录请求
func (s *BaseCoreService) RecordRequest() {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	s.metrics.RequestCount++
	s.metrics.LastRequest = time.Now()
}

// RecordBusinessOp 记录业务操作
func (s *BaseCoreService) RecordBusinessOp() {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	s.metrics.BusinessOpCount++
	s.metrics.LastBusinessOp = time.Now()
}

// RecordSuccess 记录成功
func (s *BaseCoreService) RecordSuccess() {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	s.metrics.SuccessCount++
}

// RecordError 记录错误
func (s *BaseCoreService) RecordError() {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	s.metrics.ErrorCount++
	s.metrics.LastError = time.Now()
}

// GetMetrics 获取指标
func (s *BaseCoreService) GetMetrics() *ServiceMetrics {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	return &ServiceMetrics{
		RequestCount:    s.metrics.RequestCount,
		BusinessOpCount: s.metrics.BusinessOpCount,
		ErrorCount:      s.metrics.ErrorCount,
		SuccessCount:    s.metrics.SuccessCount,
		AvgResponseTime: s.metrics.AvgResponseTime,
		LastRequest:     s.metrics.LastRequest,
		LastBusinessOp:  s.metrics.LastBusinessOp,
		LastError:       s.metrics.LastError,
	}
}

// ExecuteBusinessOperation 执行业务操作（带指标记录）
func (s *BaseCoreService) ExecuteBusinessOperation(operation string, handler func() error) error {
	startTime := time.Now()
	s.RecordRequest()
	s.RecordBusinessOp()

	defer func() {
		processingTime := time.Since(startTime)
		s.updateResponseTime(processingTime)
	}()

	utils.Debugf("🎯 执行业务操作: %s.%s", s.name, operation)

	if err := handler(); err != nil {
		s.RecordError()
		utils.Errorf("❌ 业务操作失败: %s.%s, 错误: %v", s.name, operation, err)
		return err
	}

	s.RecordSuccess()
	utils.Debugf("✅ 业务操作成功: %s.%s", s.name, operation)
	return nil
}

// updateResponseTime 更新响应时间
func (s *BaseCoreService) updateResponseTime(duration time.Duration) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if s.metrics.AvgResponseTime == 0 {
		s.metrics.AvgResponseTime = duration
	} else {
		s.metrics.AvgResponseTime = (s.metrics.AvgResponseTime + duration) / 2
	}
}

// GetCoreComponent 获取核心组件依赖（辅助方法）
func (s *BaseCoreService) GetCoreComponent(componentType ComponentType) *BaseCoreComponent {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	for _, dep := range s.coreDeps {
		if dep.GetComponentType() == componentType {
			return dep
		}
	}

	return nil
}

// GetCoreComponentByName 根据名称获取核心组件依赖
func (s *BaseCoreService) GetCoreComponentByName(name string) *BaseCoreComponent {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	for _, dep := range s.coreDeps {
		if dep.GetComponentName() == name {
			return dep
		}
	}

	return nil
}
