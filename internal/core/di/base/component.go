package base

import (
	"context"
	"fmt"
	"hkchat_api/pkg/utils"
	"sync"
	"time"
)

// ComponentType 组件类型
type ComponentType string

const (
	ComponentTypeConfig       ComponentType = "config"
	ComponentTypeCache        ComponentType = "cache"
	ComponentTypeAuth         ComponentType = "auth"
	ComponentTypeLLMGateway   ComponentType = "llm_gateway"
	ComponentTypeFile         ComponentType = "file"
	ComponentTypeNotification ComponentType = "notification"
	ComponentTypeEmail        ComponentType = "email"
)

// ComponentMetrics 核心组件指标
type ComponentMetrics struct {
	RequestCount    int64         `json:"request_count"`
	ErrorCount      int64         `json:"error_count"`
	SuccessCount    int64         `json:"success_count"`
	AvgResponseTime time.Duration `json:"avg_response_time"`
	LastRequest     time.Time     `json:"last_request"`
	LastError       time.Time     `json:"last_error"`
}

// BaseCoreComponent 核心组件基础实现
type BaseCoreComponent struct {
	name          string
	version       string
	componentType ComponentType
	dependencies  []*BaseCoreComponent
	isHealthy     bool
	isInitialized bool
	mutex         sync.RWMutex
	metrics       *ComponentMetrics
}

// NewBaseCoreComponent 创建核心组件实例
func NewBaseCoreComponent(name, version string, componentType ComponentType) *BaseCoreComponent {
	return &BaseCoreComponent{
		name:          name,
		version:       version,
		componentType: componentType,
		dependencies:  []*BaseCoreComponent{},
		isHealthy:     true,
		isInitialized: false,
		metrics:       &ComponentMetrics{},
	}
}

// GetComponentName 获取组件名称
func (c *BaseCoreComponent) GetComponentName() string {
	return c.name
}

// GetComponentVersion 获取组件版本
func (c *BaseCoreComponent) GetComponentVersion() string {
	return c.version
}

// GetComponentType 获取组件类型
func (c *BaseCoreComponent) GetComponentType() ComponentType {
	return c.componentType
}

// Initialize 初始化组件
func (c *BaseCoreComponent) Initialize() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.isInitialized {
		return nil
	}

	c.isInitialized = true
	utils.Infof("🔧 核心组件 %s 初始化完成", c.name)
	return nil
}

// AddDependency 添加核心组件依赖
func (c *BaseCoreComponent) AddDependency(component *BaseCoreComponent) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	// 检查循环依赖
	if c.hasCircularDependency(component, map[string]bool{}) {
		utils.Warnf("⚠️ 检测到潜在循环依赖: %s -> %s", c.GetComponentName(), component.GetComponentName())
		return
	}

	c.dependencies = append(c.dependencies, component)
	utils.Infof("🔗 %s 添加核心组件依赖: %s", c.GetComponentName(), component.GetComponentName())
}

// GetDependencies 获取核心组件依赖
func (c *BaseCoreComponent) GetDependencies() []*BaseCoreComponent {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	return c.dependencies
}

// hasCircularDependency 检查循环依赖
func (c *BaseCoreComponent) hasCircularDependency(target *BaseCoreComponent, visited map[string]bool) bool {
	if target.GetComponentName() == c.GetComponentName() {
		return true
	}

	if visited[target.GetComponentName()] {
		return false
	}

	visited[target.GetComponentName()] = true

	for _, dep := range target.GetDependencies() {
		if c.hasCircularDependency(dep, visited) {
			return true
		}
	}

	return false
}

// HealthCheck 健康检查
func (c *BaseCoreComponent) HealthCheck(ctx context.Context) error {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	if !c.isInitialized {
		return fmt.Errorf("核心组件 %s 未初始化", c.name)
	}

	if !c.isHealthy {
		return fmt.Errorf("核心组件 %s 不健康", c.name)
	}

	// 检查依赖健康
	for _, dep := range c.dependencies {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
			if err := dep.HealthCheck(ctx); err != nil {
				return fmt.Errorf("核心组件依赖 %s 不健康: %v", dep.GetComponentName(), err)
			}
		}
	}

	return nil
}

// Shutdown 关闭组件
func (c *BaseCoreComponent) Shutdown(ctx context.Context) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	utils.Infof("🔒 核心组件 %s 关闭", c.name)
	return nil
}

// SetHealthy 设置健康状态
func (c *BaseCoreComponent) SetHealthy(healthy bool) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.isHealthy = healthy
	if healthy {
		utils.Infof("✅ 核心组件 %s 状态正常", c.name)
	} else {
		utils.Warnf("❌ 核心组件 %s 状态异常", c.name)
	}
}

// RecordRequest 记录请求
func (c *BaseCoreComponent) RecordRequest() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.metrics.RequestCount++
	c.metrics.LastRequest = time.Now()
}

// RecordSuccess 记录成功
func (c *BaseCoreComponent) RecordSuccess() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.metrics.SuccessCount++
}

// RecordError 记录错误
func (c *BaseCoreComponent) RecordError() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.metrics.ErrorCount++
	c.metrics.LastError = time.Now()
}

// GetMetrics 获取指标
func (c *BaseCoreComponent) GetMetrics() *ComponentMetrics {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	return &ComponentMetrics{
		RequestCount:    c.metrics.RequestCount,
		ErrorCount:      c.metrics.ErrorCount,
		SuccessCount:    c.metrics.SuccessCount,
		AvgResponseTime: c.metrics.AvgResponseTime,
		LastRequest:     c.metrics.LastRequest,
		LastError:       c.metrics.LastError,
	}
}

// ExecuteOperation 执行操作（带指标记录）
func (c *BaseCoreComponent) ExecuteOperation(operation string, handler func() error) error {
	startTime := time.Now()
	c.RecordRequest()

	defer func() {
		processingTime := time.Since(startTime)
		c.updateResponseTime(processingTime)
	}()

	utils.Debugf("🔧 执行核心组件操作: %s.%s", c.name, operation)

	if err := handler(); err != nil {
		c.RecordError()
		utils.Errorf("❌ 核心组件操作失败: %s.%s, 错误: %v", c.name, operation, err)
		return err
	}

	c.RecordSuccess()
	utils.Debugf("✅ 核心组件操作成功: %s.%s", c.name, operation)
	return nil
}

// updateResponseTime 更新响应时间
func (c *BaseCoreComponent) updateResponseTime(duration time.Duration) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.metrics.AvgResponseTime == 0 {
		c.metrics.AvgResponseTime = duration
	} else {
		c.metrics.AvgResponseTime = (c.metrics.AvgResponseTime + duration) / 2
	}
}
