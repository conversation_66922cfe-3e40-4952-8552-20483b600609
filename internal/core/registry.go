package core

import (
	"hkchat_api/internal/config"
	"hkchat_api/internal/core/di"
	"hkchat_api/internal/repository"
	"hkchat_api/pkg/clients/database"
	"hkchat_api/pkg/utils"
)

// CoreRegistry 核心组件注册器
type CoreRegistry struct {
	container *di.Container
}

// NewCoreRegistry 创建核心组件注册器
func NewCoreRegistry(container *di.Container) *CoreRegistry {
	return &CoreRegistry{
		container: container,
	}
}

// RegisterAll 注册所有核心组件
func (r *CoreRegistry) RegisterAll() error {
	utils.Info("🔧 开始注册核心组件...")

	// 1. 注册配置管理器（优先级最高）
	if err := r.registerConfigManager(); err != nil {
		return err
	}

	// 注意：LLMService 在 di.RegisterBusinessServices() 中注册，避免重复注册

	utils.Info("✅ 核心组件注册完成")
	return nil
}

// registerConfigManager 注册配置管理器
func (r *CoreRegistry) registerConfigManager() error {
	utils.Info("🔧 注册配置管理器...")

	// 获取数据库连接
	db := database.GetGlobalDB()
	if db == nil {
		utils.Warn("⚠️ 数据库连接未初始化，跳过配置管理器注册")
		return nil
	}

	// 创建配置仓储
	configRepo := repository.NewConfigRepository(db)

	// 创建配置服务
	configSvc := config.NewService(configRepo)

	// 创建配置管理器
	configManager := config.NewManager(configSvc)

	// 将配置管理器包装为核心组件
	configComponent := di.NewConfigManagerComponent(configManager)

	// 注册到容器
	if err := r.container.RegisterCoreComponent("ConfigManager", configComponent.BaseCoreComponent); err != nil {
		return err
	}

	utils.Info("✅ 配置管理器注册完成")
	return nil
}

// registerLLMService 已移除，LLMService 现在在 di.RegisterBusinessServices() 中注册

// SetupDependencies 设置核心组件之间的依赖关系
func (r *CoreRegistry) SetupDependencies() error {
	utils.Info("🔗 设置核心组件依赖关系...")

	// 注意：LLMService 的依赖关系在 di.SetupBusinessServiceDependencies() 中设置
	// 这里只处理纯核心组件之间的依赖关系

	utils.Info("✅ 核心组件依赖关系设置完成")
	return nil
}
