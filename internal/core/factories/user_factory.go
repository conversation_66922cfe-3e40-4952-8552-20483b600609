package factories

import (
	"hkchat_api/internal/core/interfaces"
	"hkchat_api/internal/models/entities"
)

// EntityUserFactory 基于entities模型的用户工厂
type EntityUserFactory struct{}

// NewEntityUserFactory 创建基于entities的用户工厂
func NewEntityUserFactory() interfaces.UserFactory {
	return &EntityUserFactory{}
}

// CreateSystemUser 创建系统用户
func (f *EntityUserFactory) CreateSystemUser(id, name string) interfaces.User {
	return &entities.User{
		Id:    id,
		Name:  name,
		Role:  "system",
		Email: id + "@system.internal",
	}
}
