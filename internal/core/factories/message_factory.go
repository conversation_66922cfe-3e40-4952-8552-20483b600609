package factories

import (
	"hkchat_api/internal/core/interfaces"
)

// CoreMessageFactory Core层消息工厂实现
type CoreMessageFactory struct{}

// NewCoreMessageFactory 创建Core层消息工厂
func NewCoreMessageFactory() interfaces.MessageFactory {
	return &CoreMessageFactory{}
}

// CreateMessage 创建消息
func (f *CoreMessageFactory) CreateMessage(role, content string) interfaces.Message {
	return &interfaces.CoreMessage{
		Role:    role,
		Content: content,
	}
}

// CreateChatRequest 创建聊天请求
func (f *CoreMessageFactory) CreateChatRequest(model string, messages []interfaces.Message) interfaces.ChatRequest {
	return &interfaces.CoreChatRequest{
		Model:    model,
		Messages: messages,
		Stream:   false,
	}
}
