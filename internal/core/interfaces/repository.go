package interfaces

import (
	"hkchat_api/internal/models/entities"
)

// ConfigRepository 配置仓储接口
type ConfigRepository interface {
	// 基本查询操作
	GetBy<PERSON>ey(key string) (*entities.Config, error)
	GetByCategory(category string, offset, limit int) ([]*entities.Config, error)
	GetLatestConfig() (*entities.Config, error)
	GetConfigByVersion(version int64) (*entities.Config, error)
	GetConfigHistory(limit, offset int) ([]*entities.Config, error)
	GetConfigCount() (int64, error)
	SearchByKeyword(keyword string, offset, limit int) ([]*entities.Config, error)
	
	// 配置管理
	SetValue(key, value string) error
	SaveConfig(config *entities.Config) error
	CreateConfig(config *entities.Config) error
	DeleteOldConfigs(keepCount int) error
	
	// 应用配置
	GetAppConfig() (map[string]string, error)
	GetDefaultConfig() (map[string]string, error)
	
	// 用户配置
	GetUserConfig(userID string) (map[string]string, error)
	SetUserConfig(userID, key, value string) error
}
