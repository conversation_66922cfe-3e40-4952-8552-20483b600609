package interfaces

// User 用户接口 - 定义核心业务层需要的用户契约
type User interface {
	// 基本标识
	GetID() string
	GetName() string
	GetEmail() string

	// 业务状态
	IsActive() bool
	GetRole() string

	// 活动状态
	GetLastActiveAt() interface{} // 返回时间相关信息

	// 业务判断方法
	IsAdmin() bool
	CanPerformAdminActions() bool
	CanAccessChat() bool

	// 显示信息
	GetDisplayName() string
}

// UserFactory 用户工厂接口 - 用于创建系统用户
type UserFactory interface {
	CreateSystemUser(id, name string) User
}
