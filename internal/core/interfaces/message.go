package interfaces

// Message 消息接口 - 定义核心业务层需要的消息契约
type Message interface {
	GetRole() string
	GetContent() string
	SetRole(role string)
	SetContent(content string)
}

// ChatRequest 聊天请求接口 - 定义核心业务层需要的请求契约
type ChatRequest interface {
	GetModel() string
	GetMessages() []Message
	GetStream() bool
	GetMaxTokens() *int
	GetTemperature() *float64

	SetModel(model string)
	SetMessages(messages []Message)
	SetStream(stream bool)
	SetMaxTokens(maxTokens *int)
	SetTemperature(temperature *float64)
}

// MessageFactory 消息工厂接口 - 用于创建消息
type MessageFactory interface {
	CreateMessage(role, content string) Message
	CreateChatRequest(model string, messages []Message) ChatRequest
}

// CoreMessage Core层消息实现
type CoreMessage struct {
	Role    string
	Content string
}

func (m *CoreMessage) GetRole() string {
	return m.Role
}

func (m *CoreMessage) GetContent() string {
	return m.Content
}

func (m *CoreMessage) SetRole(role string) {
	m.Role = role
}

func (m *CoreMessage) SetContent(content string) {
	m.Content = content
}

// CoreChatRequest Core层聊天请求实现
type CoreChatRequest struct {
	Model       string
	Messages    []Message
	Stream      bool
	MaxTokens   *int
	Temperature *float64
}

func (r *CoreChatRequest) GetModel() string {
	return r.Model
}

func (r *CoreChatRequest) GetMessages() []Message {
	return r.Messages
}

func (r *CoreChatRequest) GetStream() bool {
	return r.Stream
}

func (r *CoreChatRequest) GetMaxTokens() *int {
	return r.MaxTokens
}

func (r *CoreChatRequest) GetTemperature() *float64 {
	return r.Temperature
}

func (r *CoreChatRequest) SetModel(model string) {
	r.Model = model
}

func (r *CoreChatRequest) SetMessages(messages []Message) {
	r.Messages = messages
}

func (r *CoreChatRequest) SetStream(stream bool) {
	r.Stream = stream
}

func (r *CoreChatRequest) SetMaxTokens(maxTokens *int) {
	r.MaxTokens = maxTokens
}

func (r *CoreChatRequest) SetTemperature(temperature *float64) {
	r.Temperature = temperature
}
