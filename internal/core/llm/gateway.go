package llm

import (
	"context"
	"fmt"
	"hkchat_api/internal/config"
	"hkchat_api/internal/core/interfaces"
	"hkchat_api/internal/core/llm/function_calling"
	"hkchat_api/internal/models/domain"
	"hkchat_api/pkg/clients/llm"
	internalLLM "hkchat_api/pkg/clients/llm"
	"hkchat_api/pkg/utils"
	"math/rand"
	"net/url"
	"strings"
	"time"
)

// Gateway LLM网关 - 负责模型路由和请求处理
type Gateway struct {
	config         *GatewayConfig
	rng            *rand.Rand
	configManager  *config.Manager
	functionCaller *function_calling.FunctionCallingHandler
	modelClients   map[string]llm.Client
	isHealthy      bool
}

// GatewayConfig 网关配置
type GatewayConfig struct {
	BaseURLs     []string             `json:"base_urls"`
	APIConfigs   map[string]APIConfig `json:"api_configs"`
	ModelMap     map[string]ModelInfo `json:"model_map"`
	EnableOllama bool                 `json:"enable_ollama"`
}

// APIConfig API 配置
type APIConfig struct {
	Key            string   `json:"key"`
	Enable         bool     `json:"enable"`
	PrefixID       string   `json:"prefix_id,omitempty"`
	Tags           []string `json:"tags,omitempty"`
	ModelIDs       []string `json:"model_ids,omitempty"`
	ConnectionType string   `json:"connection_type"`
}

// ModelInfo 模型信息
type ModelInfo struct {
	ID             string    `json:"id"`
	Name           string    `json:"name"`
	URLs           []int     `json:"urls"`
	ConnectionType string    `json:"connection_type"`
	PrefixID       string    `json:"prefix_id,omitempty"`
	Tags           []string  `json:"tags,omitempty"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
}

// StreamCallback 流式回调函数类型
type StreamCallback func(response StreamResponse)

// StreamResponse 流式响应结构
type StreamResponse struct {
	Type       StreamResponseType `json:"type"`
	Data       string             `json:"data,omitempty"`
	Error      string             `json:"error,omitempty"`
	Done       bool               `json:"done"`
	TokenCount int                `json:"token_count,omitempty"`
}

// StreamResponseType 流式响应类型
type StreamResponseType string

const (
	StreamResponseTypeData     StreamResponseType = "data"
	StreamResponseTypeError    StreamResponseType = "error"
	StreamResponseTypeComplete StreamResponseType = "complete"
	StreamResponseTypeProgress StreamResponseType = "progress"
)

// ChatCompletionResult 聊天完成结果（统一返回结构）
type ChatCompletionResult struct {
	Response    *llm.ChatResponse     `json:"response,omitempty"`
	StreamData  <-chan StreamResponse `json:"-"`
	IsStream    bool                  `json:"is_stream"`
	TokenCount  int                   `json:"token_count"`
	ProcessTime time.Duration         `json:"process_time"`
}

// NewGateway 创建LLM网关实例
func NewGateway(configManager *config.Manager) *Gateway {
	cfg := &GatewayConfig{
		BaseURLs:     []string{},
		APIConfigs:   make(map[string]APIConfig),
		ModelMap:     make(map[string]ModelInfo),
		EnableOllama: false,
	}

	rng := rand.New(rand.NewSource(time.Now().UnixNano()))

	return &Gateway{
		config:         cfg,
		rng:            rng,
		configManager:  configManager,
		functionCaller: nil,
		modelClients:   make(map[string]llm.Client),
		isHealthy:      false,
	}
}

// Initialize 初始化网关
func (g *Gateway) Initialize() error {
	utils.Debug("初始化LLM网关...")

	// 从配置管理器加载配置
	if err := g.loadConfigFromManager(); err != nil {
		return fmt.Errorf("加载配置失败: %v", err)
	}

	// 初始化默认模型
	g.initializeDefaultModels()

	// 初始化 Function Calling 处理器
	g.functionCaller = function_calling.NewFunctionCallingHandler(g.ProcessChatCompletion)

	// 初始化模型客户端（根据配置）
	if err := g.initializeModelClients(); err != nil {
		return fmt.Errorf("初始化模型客户端失败: %v", err)
	}

	g.isHealthy = true
	utils.Debug("LLM网关初始化完成")
	return nil
}

// loadConfigFromManager 从配置管理器加载配置
func (g *Gateway) loadConfigFromManager() error {
	if g.configManager == nil {
		return fmt.Errorf("配置管理器未设置")
	}

	// 确保配置已经加载到内存中
	if !g.configManager.IsConfigLoaded() {
		utils.Debug("配置未加载，正在加载应用配置...")
		if err := g.configManager.LoadAppConfig(); err != nil {
			return fmt.Errorf("加载应用配置失败: %v", err)
		}
	}

	// 获取 OpenAI 配置
	openaiConfig := g.configManager.GetOpenAIConfig()
	if openaiConfig == nil {
		return fmt.Errorf("无法获取 OpenAI 配置")
	}

	// 清空现有配置
	g.config.BaseURLs = []string{}
	g.config.APIConfigs = make(map[string]APIConfig)

	if openaiConfig.Enable {
		// 设置 Base URLs
		g.config.BaseURLs = make([]string, len(openaiConfig.APIBaseURLs))
		copy(g.config.BaseURLs, openaiConfig.APIBaseURLs)

		// 设置 API 配置
		for i, apiKey := range openaiConfig.APIKeys {
			g.config.APIConfigs[fmt.Sprintf("%d", i)] = APIConfig{
				Key:            apiKey,
				Enable:         true,
				ConnectionType: "openai",
			}
		}

		// 处理具体的 API 配置映射
		for configKey, apiConfig := range openaiConfig.APIConfigs {
			g.config.APIConfigs[configKey] = APIConfig{
				Key:            "",
				Enable:         apiConfig.Enable,
				PrefixID:       apiConfig.PrefixID,
				Tags:           apiConfig.Tags,
				ModelIDs:       apiConfig.ModelIDs,
				ConnectionType: apiConfig.ConnectionType,
			}
		}

		utils.Infof("从配置管理器加载了 %d 个 API URLs 和 %d 个 API Keys",
			len(g.config.BaseURLs), len(openaiConfig.APIKeys))
	} else {
		utils.Warn("OpenAI 配置未启用")
	}

	// 获取 Ollama 配置
	if appConfig := g.configManager.GetAppConfig(); appConfig != nil {
		g.config.EnableOllama = appConfig.Ollama.Enable
		utils.Debugf("Ollama 启用状态: %v", g.config.EnableOllama)
	}

	return nil
}

// initializeDefaultModels 初始化默认模型
func (g *Gateway) initializeDefaultModels() {
	if len(g.config.BaseURLs) > 0 {
		defaultModels := []string{
			"gpt-3.5-turbo",
			"gpt-3.5-turbo:latest",
			"gpt-4",
			"gpt-4:latest",
			"gpt-4o",
			"gpt-4o:latest",
			"HKGAI-V1",
			"HKGAI-V1:latest",
		}

		for _, modelID := range defaultModels {
			g.config.ModelMap[modelID] = ModelInfo{
				ID:             modelID,
				Name:           modelID,
				URLs:           []int{0},
				ConnectionType: "openai",
				CreatedAt:      time.Now(),
				UpdatedAt:      time.Now(),
			}
		}

		utils.Infof("初始化了 %d 个默认模型", len(defaultModels))
	}
}

// GetModelURL 获取模型对应的 URL 和索引
func (g *Gateway) GetModelURL(modelID string, urlIdx *int) (string, int, error) {
	utils.Debugf("获取模型 URL: %s", modelID)

	// 规范化模型名称
	normalizedModelID := modelID

	var selectedIdx int

	// 如果指定了 URL 索引，直接使用
	if urlIdx != nil {
		selectedIdx = *urlIdx
	} else {
		// 从模型映射中查找
		modelInfo, exists := g.config.ModelMap[normalizedModelID]
		if !exists {
			// 如果找不到精确匹配，尝试不带版本的匹配
			baseModelID := strings.Split(modelID, ":")[0]
			modelInfo, exists = g.config.ModelMap[baseModelID]
			if !exists {
				return "", 0, fmt.Errorf("模型 %s 未找到", modelID)
			}
		}

		// 从可用 URLs 中随机选择一个
		if len(modelInfo.URLs) == 0 {
			return "", 0, fmt.Errorf("模型 %s 没有可用的 URL", modelID)
		}

		selectedIdx = modelInfo.URLs[g.rng.Intn(len(modelInfo.URLs))]
	}

	// 检查索引是否有效
	if selectedIdx < 0 || selectedIdx >= len(g.config.BaseURLs) {
		return "", 0, fmt.Errorf("无效的 URL 索引: %d", selectedIdx)
	}

	result := g.config.BaseURLs[selectedIdx]
	utils.Debugf("选择的 URL[%d]: %s", selectedIdx, result)
	return result, selectedIdx, nil
}

// GetAPIKey 获取 API Key（从动态配置）
func (g *Gateway) GetAPIKey(idx int, baseURL string) string {
	utils.Debugf("获取 API Key for idx: %d, url: %s", idx, baseURL)

	// 优先从配置管理器获取最新的 API Key
	if g.configManager != nil {
		openaiConfig := g.configManager.GetOpenAIConfig()
		if openaiConfig != nil && idx >= 0 && idx < len(openaiConfig.APIKeys) {
			utils.Debugf("从配置管理器获取 API Key[%d]", idx)
			return openaiConfig.APIKeys[idx]
		}
	}

	// 回退到本地缓存配置
	if apiConfig, exists := g.config.APIConfigs[fmt.Sprintf("%d", idx)]; exists {
		utils.Debugf("从本地缓存获取 API Key[%d]", idx)
		return apiConfig.Key
	}

	// 尝试通过 URL 查找（Legacy 支持）
	if parsedURL, err := url.Parse(baseURL); err == nil {
		baseURLKey := fmt.Sprintf("%s://%s", parsedURL.Scheme, parsedURL.Host)
		if apiConfig, exists := g.config.APIConfigs[baseURLKey]; exists {
			utils.Debugf("通过 URL 匹配获取 API Key")
			return apiConfig.Key
		}
	}

	// 最后回退到全局配置（仅作为紧急备用）
	if config.GlobalConfig != nil {
		utils.Warn("使用全局配置 API Key 作为后备")
		return config.GlobalConfig.OpenAI.APIKey
	}

	return ""
}

// CreateOpenAIClient 创建 OpenAI 客户端
func (g *Gateway) CreateOpenAIClient(modelID string, urlIdx *int) (llm.Client, error) {
	utils.Debugf("为模型 %s 创建 OpenAI 客户端", modelID)

	// 获取 URL 和索引
	baseURL, idx, err := g.GetModelURL(modelID, urlIdx)
	if err != nil {
		return nil, fmt.Errorf("获取模型 URL 失败: %v", err)
	}

	// 获取 API Key
	apiKey := g.GetAPIKey(idx, baseURL)
	if apiKey == "" {
		return nil, fmt.Errorf("未找到模型 %s 的 API Key", modelID)
	}

	// 脱敏打印配置信息
	maskedKey := apiKey
	if len(apiKey) > 8 {
		maskedKey = apiKey[:4] + "****" + apiKey[len(apiKey)-4:]
	}
	utils.Debugf("创建客户端配置: BaseURL=%s, API Key=%s, 原始模型=%s", baseURL, maskedKey, modelID)

	// 创建临时配置
	tempConfig := config.GlobalConfig.OpenAI
	tempConfig.BaseURL = baseURL
	tempConfig.APIKey = apiKey
	tempConfig.Model = modelID

	// 处理前缀 ID
	finalModel := modelID
	if apiConfig, exists := g.config.APIConfigs[fmt.Sprintf("%d", idx)]; exists {
		if apiConfig.PrefixID != "" && strings.HasPrefix(modelID, apiConfig.PrefixID+".") {
			// 移除前缀
			finalModel = strings.TrimPrefix(modelID, apiConfig.PrefixID+".")
			utils.Debugf("移除模型前缀: %s -> %s", modelID, finalModel)
		}
	}
	tempConfig.Model = finalModel

	utils.Debugf("最终客户端配置: BaseURL=%s, Model=%s", tempConfig.BaseURL, tempConfig.Model)

	// 临时保存原配置
	originalConfig := config.GlobalConfig.OpenAI
	config.GlobalConfig.OpenAI = tempConfig

	// 创建客户端
	client := internalLLM.NewOpenAIClient()

	// 恢复原配置
	config.GlobalConfig.OpenAI = originalConfig

	utils.Debugf("OpenAI 客户端创建成功")
	return client, nil
}

// ProcessChatCompletion 处理聊天完成请求（同步）
func (g *Gateway) ProcessChatCompletion(req interfaces.ChatRequest, userID string) (*internalLLM.ChatResponse, error) {
	utils.Infof("处理聊天完成请求（同步），模型: %s, 用户: %s", req.GetModel(), userID)

	startTime := time.Now()

	// 获取模型URL和索引
	utils.Debugf("开始获取模型 %s 的配置信息...", req.GetModel())
	baseURL, idx, err := g.GetModelURL(req.GetModel(), nil)
	if err != nil {
		return nil, fmt.Errorf("获取模型 URL 失败: %v", err)
	}

	// 获取 API Key
	apiKey := g.GetAPIKey(idx, baseURL)
	if apiKey == "" {
		return nil, fmt.Errorf("未找到模型 %s 的 API Key", req.GetModel())
	}

	// 打印配置信息（脱敏）
	maskedKey := apiKey
	if len(apiKey) > 8 {
		maskedKey = apiKey[:4] + "****" + apiKey[len(apiKey)-4:]
	}
	utils.Debugf("使用配置: URL[%d]=%s, API Key=%s", idx, baseURL, maskedKey)
	utils.Debugf("目标模型: %s", req.GetModel())

	// 创建 OpenAI 客户端
	client, err := g.CreateOpenAIClient(req.GetModel(), nil)
	if err != nil {
		return nil, fmt.Errorf("创建 OpenAI 客户端失败: %v", err)
	}

	// 转换请求格式
	chatReq := g.convertToChatRequest(req)
	chatReq.Stream = false

	// 打印最终发送的请求信息
	utils.Debugf("发送请求: 模型=%s, 消息数=%d", chatReq.Model, len(chatReq.Messages))

	// 调用 OpenAI 客户端
	ctx := context.Background()
	response, err := client.ChatCompletion(ctx, chatReq)
	if err != nil {
		return nil, fmt.Errorf("OpenAI 请求失败: %v", err)
	}

	processTime := time.Since(startTime)
	utils.Infof("聊天完成处理成功，耗时: %v", processTime)

	// 打印响应摘要
	if response != nil {
		utils.Infof("响应摘要: ID=%s, 模型=%s, 选择数=%d",
			response.ID, response.Model, len(response.Choices))
		if len(response.Choices) > 0 {
			contentLength := len(response.Choices[0].Message.Content)
			utils.Debugf("响应内容长度: %d 字符", contentLength)
		}
	}

	return response, nil
}

// convertToChatRequest 转换请求格式
func (g *Gateway) convertToChatRequest(req interfaces.ChatRequest) *llm.ChatRequest {
	messages := req.GetMessages()
	chatReq := &llm.ChatRequest{
		Model:    req.GetModel(),
		Messages: make([]llm.ChatMessage, len(messages)),
		Stream:   req.GetStream(),
	}

	// 处理MaxTokens指针类型
	if req.GetMaxTokens() != nil {
		chatReq.MaxTokens = req.GetMaxTokens()
	}

	// 处理Temperature指针类型
	if req.GetTemperature() != nil {
		temp := float32(*req.GetTemperature())
		chatReq.Temperature = &temp
	}

	// 转换消息格式
	for i, msg := range messages {
		chatReq.Messages[i] = llm.ChatMessage{
			Role:    msg.GetRole(),
			Content: msg.GetContent(),
		}
	}

	return chatReq
}


// ListAvailableModels 列出可用模型
func (g *Gateway) ListAvailableModels() []ModelInfo {
	var models []ModelInfo
	for _, model := range g.config.ModelMap {
		models = append(models, model)
	}
	return models
}

// GetCurrentConfig 获取当前配置
func (g *Gateway) GetCurrentConfig() map[string]interface{} {
	return map[string]interface{}{
		"base_urls":     g.config.BaseURLs,
		"api_configs":   g.config.APIConfigs,
		"model_map":     g.config.ModelMap,
		"enable_ollama": g.config.EnableOllama,
	}
}

// initializeModelClients 初始化模型客户端
func (g *Gateway) initializeModelClients() error {
	for modelID := range g.config.ModelMap {
		client, err := g.CreateOpenAIClient(modelID, nil)
		if err != nil {
			return fmt.Errorf("初始化模型客户端失败: %v", err)
		}
		g.modelClients[modelID] = client
	}
	return nil
}

// ProcessChatCompletionWithFunctionCalling 处理带有 Function Calling 的聊天完成请求
func (g *Gateway) ProcessChatCompletionWithFunctionCalling(
	ctx context.Context,
	req interfaces.ChatRequest,
	user interfaces.User,
	tools map[string]interface{},
	extraParams map[string]interface{},
) (*internalLLM.ChatResponse, map[string]interface{}, error) {
	utils.Infof("处理带有 Function Calling 的聊天完成请求，模型: %s, 函数数量: %d", req.GetModel(), len(tools))

	// 直接使用interfaces调用Function Calling处理器
	processedReq, metadata, err := g.functionCaller.ChatCompletionToolsHandler(ctx, req, user, tools, extraParams)
	if err != nil {
		return nil, nil, fmt.Errorf("Function Calling处理失败: %v", err)
	}
	
	// 处理普通的聊天完成请求
	response, err := g.ProcessChatCompletion(processedReq, user.GetID())
	if err != nil {
		return nil, nil, fmt.Errorf("处理聊天完成请求失败: %v", err)
	}

	return response, metadata, nil
}

// HasFunctionCalls 检查请求是否包含 Function Calling  
func (g *Gateway) HasFunctionCalls(req interfaces.ChatRequest) bool {
	// 将接口转换为具体类型以访问特定字段
	if domainReq, ok := req.(*domain.ChatRequest); ok {
		// 检查是否有 ToolIDs 字段
		if domainReq.ToolIDs != nil && len(domainReq.ToolIDs) > 0 {
			return true
		}
		// 检查是否有 FunctionCalling 字段
		if domainReq.FunctionCalling != "" {
			return true
		}
		return false
	}
	return false
}
