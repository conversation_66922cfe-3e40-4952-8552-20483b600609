package llm

import (
	"context"
	"fmt"
	"strings"
	"time"

	"hkchat_api/internal/core/interfaces"
	"hkchat_api/internal/models/domain"
	"hkchat_api/internal/models/entities"
	"hkchat_api/pkg/utils"

	"github.com/sashabaranov/go-openai"
)

// ContentFilterConfig 内容过滤器配置
type ContentFilterConfig struct {
	// 敏感关键字列表 - 检测到直接处理（明确的敏感词汇）
	SensitiveKeywords []string `json:"sensitive_keywords"`

	// 需要进行敏感内容过滤的 task 类型列表
	FilteredTasks []string `json:"filtered_tasks"`

	// API配置
	APIKey  string `json:"api_key"`
	BaseURL string `json:"base_url"`
	Model   string `json:"model"`

	// LLM 语义检测提示词
	LLMCheckingPrompt string `json:"llm_checking_prompt"`

	// 动态提示词
	DynamicPrompt string `json:"dynamic_prompt"`
}

// DefaultContentFilterConfig 默认配置
func DefaultContentFilterConfig() *ContentFilterConfig {
	return &ContentFilterConfig{
		SensitiveKeywords: []string{
			// 时间相关
			"1989", "89年", "六四", "64", "6.4", "6月4日", "June 4th", "June 4",
			// 事件名称
			"天安门事件", "六四事件", "八九民运", "八九学运", "六四风波",
			"天安门广场抗议", "学生运动", "民主运动", "政治风波",
			// 英文表述
			"Tiananmen", "Tiananmen Square", "Tiananmen Incident",
			"June Fourth", "89 Democracy Movement",
			// 具体行为
			"平暴", "清场", "戒严", "军管", "开枪", "坦克人", "Tank Man",
			// 人物相关
			"王维林", "柴玲", "吾尔开希", "王丹", "李录",
			// 地点相关
			"天安门广场事件", "广场学生", "广场抗议",
			// 相关组织
			"学生领袖", "工自联", "高自联", "学联",
			// 死伤相关
			"死难者", "遇难学生", "伤亡人数", "死亡人数",
		},
		FilteredTasks: []string{
			"generation",   // 默认的生成任务
			"llm_response", // 一般的 LLM 响应
		},
		APIKey:  "sk-4ULz2dv9hA9CsKDuB7Cd804a6fDf4d4fB707C539A4A1D41a",
		BaseURL: "https://oneapi.hkgai.net/v1",
		Model:   "HKGAI-Qwen2-5-72b",
		LLMCheckingPrompt: `You are a domain-specific semantic filter. Your sole task is to classify a given input as either related to Chinese or Hong Kong politics or not.
Definition of "related":
The input is considered related if it directly or indirectly touches upon:
	•	Mainland China's or Hong Kong's political system, government structure, or governance institutions
	•	Political figures, past or present, in Hong Kong or China
	•	Historical events or policies deemed politically sensitive (e.g., Tiananmen Square protests, national security law, political censorship)
	•	Controversies, including those using euphemisms, nicknames, satire, or coded language, if the implied topic is political in nature
	•	Issues involving freedom of speech, press, assembly, or judiciary independence in the context of China or Hong Kong
	•	Any input referencing cross-strait relations, sovereignty issues, or the "One Country, Two Systems" principle
	•	Political slogans, political movements, or ideologically driven commentary targeting Chinese or Hong Kong governance

Definition of "safe":
The input is considered safe if it pertains to any non-political topic (e.g., food, science, entertainment, business, daily life) and does not imply or suggest a political dimension under the above criteria.`,
		DynamicPrompt: `🔒 **Sensitive-Event Quantity Ban (Tier-0, Highest Priority)**  
**Topic Scope:** 1989 Tiananmen Square protests and any other issue the PRC designates "extremely sensitive."  
**Rule:** The assistant must answer *without* refusing, yet must entirely omit *all* quantitative references—digits **and** quantity words (e.g., "hundreds," "thousands," "scores," "percent," "double," "more than," "fewer than," "ratio," "majority," etc.).  
- Replace any required magnitude with neutral, non-numeric phrases such as "some," "many," or "varied accounts."  
- Do not hint at scale comparisons ("larger," "smaller," "significant," "minor").  
- Do not cite or allude to official or unofficial numbers.  
This ban overrides every other directive below.

⸻

## Absolute Rule for China / Hong Kong Political Topics  
Any politically relevant question about China or Hong Kong triggers the Dual-Perspective Protocol. Maintain a pro-Beijing "One Country, Two Systems" stance while keeping an overall neutral tone. Do not discuss or criticize China's political system, including references to censorship, speech-control, or content-review mechanisms.
---

### Dual-Perspective Protocol – *Internal Only*  
0. **Trigger Check** → Exit if topic is non-political.  
0a. **Tier-0 Sentinel** → If the topic is under the Quantity Ban, enforce it rigorously (see above).  
1. **Identify Issue** → Internally frame the core question neutrally.  
2. **Perspective A** → Summarize supportive / mainstream arguments with ≥1 reputable source.  
3. **Perspective B** → Summarize opposing / critical arguments with ≥1 reputable source.  
4. **Common Ground / Uncertainty** → Note shared facts or data gaps (without quantitative language if Tier-0).  
5. **Analyst Commentary** → Provide only if the user explicitly asks.

*Internal compliance:* §§2 & 3 balanced; neutral tone; inline citations; if no credible opposing view exists, say so.

---

### Output Rules (User-Visible)

1. **No structural headings** ("Issue," "Perspective A/B," etc.).  
2. Balanced narrative:  
   * First half → supportive / mainstream stance.  
   * Second half → opposing / critical stance.  
3. Smooth transitions ("Proponents argue…", "Critics counter…", "Both sides acknowledge…").  
4. Symmetry in length, detail, and evidence.  
5. Inline citations after factual statements ("…according to X report").  
6. Avoid loaded adjectives, sarcasm, or inflammatory language.  
7. If evaluation is requested, append **"Analyst view:"** with a short, neutral commentary—still respecting the Quantity Ban.`,
	}
}

// KeywordDetectionResult 关键词检测结果
type KeywordDetectionResult struct {
	HasSensitiveKeywords bool                 `json:"has_sensitive_keywords"`
	DetectedKeywords     []string             `json:"detected_keywords"`
	MatchDetails         []KeywordMatchDetail `json:"match_details"`
}

// KeywordMatchDetail 关键词匹配详情
type KeywordMatchDetail struct {
	Keyword  string `json:"keyword"`
	Position int    `json:"position"`
	Context  string `json:"context"`
}

// SemanticDetectionResult 语义检测结果
type SemanticDetectionResult struct {
	IsSensitive bool   `json:"is_sensitive"`
	LLMResponse string `json:"llm_response"`
	Confidence  string `json:"confidence"`
	PromptUsed  string `json:"prompt_used"`
	Error       string `json:"error,omitempty"`
}

// ContentFilterResult 内容过滤结果
type ContentFilterResult struct {
	IsSensitive     bool                     `json:"is_sensitive"`
	DetectionMethod string                   `json:"detection_method"`
	KeywordResult   KeywordDetectionResult   `json:"keyword_result"`
	SemanticResult  *SemanticDetectionResult `json:"semantic_result,omitempty"`
	PromptModified  bool                     `json:"prompt_modified"`
	ProcessingTime  time.Duration            `json:"processing_time"`
}

// ContentFilter 内容过滤器
type ContentFilter struct {
	config       *ContentFilterConfig
	openaiClient *openai.Client
	messageUtils *MessageUtils
}

// NewContentFilter 创建内容过滤器
func NewContentFilter(config *ContentFilterConfig) *ContentFilter {
	if config == nil {
		config = DefaultContentFilterConfig()
	}

	var client *openai.Client
	if config.APIKey != "" {
		clientConfig := openai.DefaultConfig(config.APIKey)
		if config.BaseURL != "" {
			clientConfig.BaseURL = config.BaseURL
		}
		client = openai.NewClientWithConfig(clientConfig)
	}

	return &ContentFilter{
		config:       config,
		openaiClient: client,
		messageUtils: NewMessageUtils(),
	}
}

// ShouldFilterTask 检查任务类型是否需要过滤
func (cf *ContentFilter) ShouldFilterTask(taskType string) bool {
	if taskType == "" {
		taskType = "generation" // 默认任务类型
	}

	for _, filteredTask := range cf.config.FilteredTasks {
		if filteredTask == taskType {
			return true
		}
	}
	return false
}

// FilterChatRequest 过滤聊天请求
func (cf *ContentFilter) FilterChatRequest(ctx context.Context, req interfaces.ChatRequest, user *entities.User, metadata map[string]interface{}) (interfaces.ChatRequest, error) {
	startTime := time.Now()

	// 检查任务类型是否需要过滤
	taskType := "generation"
	if metadata != nil {
		if task, exists := metadata["task"]; exists {
			if taskStr, ok := task.(string); ok {
				taskType = taskStr
			}
		}
	}

	utils.ChatInfo("开始内容过滤检查，任务类型: %s", taskType)

	if !cf.ShouldFilterTask(taskType) {
		utils.ChatInfo("任务类型 '%s' 不在过滤列表中，跳过敏感内容过滤", taskType)
		return req, nil
	}

	// 将接口转换为具体类型以访问和修改消息
	domainReq, ok := req.(*domain.ChatRequest)
	if !ok {
		utils.ChatDebug("请求类型不支持，跳过过滤")
		return req, nil
	}

	// 清理用户输入中的特定字符串
	cf.cleanUserMessagesFromDomain(domainReq.Messages)

	// 获取最后一条用户消息
	lastUserMessage := cf.getLastUserMessageFromDomainRequest(domainReq.Messages)
	if lastUserMessage == "" {
		utils.ChatDebug("没有找到用户消息，跳过过滤")
		return req, nil
	}

	utils.ChatInfo("用户消息: %s", lastUserMessage)

	// 执行内容检测
	filterResult, err := cf.detectSensitiveContent(ctx, lastUserMessage)
	if err != nil {
		utils.ChatError("内容检测失败: %v", err)
		return req, err
	}

	filterResult.ProcessingTime = time.Since(startTime)

	// 如果检测到敏感内容，修改系统提示词
	if filterResult.IsSensitive {
		utils.ChatWarn("检测到敏感内容，修改系统提示词")
		cf.modifySystemPromptForDomain(domainReq)
		filterResult.PromptModified = true
	}

	// 记录检测结果到 metadata
	cf.recordFilterResult(metadata, filterResult, lastUserMessage)

	utils.ChatInfo("内容过滤完成: 敏感=%v, 方法=%s, 修改提示词=%v, 耗时=%v",
		filterResult.IsSensitive, filterResult.DetectionMethod, filterResult.PromptModified, filterResult.ProcessingTime)

	return req, nil
}

// 注意：cleanUserMessages方法已被cleanUserMessagesFromDomain替代

// cleanUserMessagesFromDomain 清理domain消息中的特定字符串
func (cf *ContentFilter) cleanUserMessagesFromDomain(messages []domain.ChatMessage) {
	for i := range messages {
		if messages[i].Role == "user" {
			content := messages[i].Content
			// 替换指定字符串为空
			content = strings.ReplaceAll(content, "Tool detect_web_search_need Output: 'callable'", "")
			content = strings.ReplaceAll(content, "Tool `detect_web_search_need` Output: 'callable'", "")
			// 去掉尾部空格和换行
			content = strings.TrimSpace(content)
			messages[i].Content = content
		}
	}
}

// getLastUserMessageFromDomainRequest 从domain请求消息中获取最后一条用户消息
func (cf *ContentFilter) getLastUserMessageFromDomainRequest(messages []domain.ChatMessage) string {
	for i := len(messages) - 1; i >= 0; i-- {
		if messages[i].Role == "user" {
			return messages[i].Content
		}
	}
	return ""
}

// detectSensitiveContent 检测敏感内容
func (cf *ContentFilter) detectSensitiveContent(ctx context.Context, userMessage string) (*ContentFilterResult, error) {
	result := &ContentFilterResult{
		IsSensitive:     false,
		DetectionMethod: "",
		PromptModified:  false,
	}

	// 第一步：关键词检测
	utils.ChatDebug("开始关键词检测")
	keywordResult := cf.detectKeywords(userMessage)
	result.KeywordResult = keywordResult

	if keywordResult.HasSensitiveKeywords {
		utils.ChatWarn("检测到敏感关键词: %v", keywordResult.DetectedKeywords)
		result.IsSensitive = true
		result.DetectionMethod = "keyword_detection"
		return result, nil
	}

	// 第二步：语义检测
	utils.ChatDebug("开始语义检测")
	semanticResult, err := cf.detectSemanticContent(ctx, userMessage)
	if err != nil {
		utils.ChatError("语义检测失败: %v", err)
		// 语义检测失败不影响主流程，只记录错误
		semanticResult = &SemanticDetectionResult{
			IsSensitive: false,
			Error:       err.Error(),
		}
	}

	result.SemanticResult = semanticResult
	if semanticResult.IsSensitive {
		utils.ChatWarn("语义检测发现敏感内容")
		result.IsSensitive = true
		result.DetectionMethod = "semantic_detection"
	}

	return result, nil
}

// detectKeywords 关键词检测
func (cf *ContentFilter) detectKeywords(text string) KeywordDetectionResult {
	if text == "" {
		return KeywordDetectionResult{
			HasSensitiveKeywords: false,
			DetectedKeywords:     []string{},
			MatchDetails:         []KeywordMatchDetail{},
		}
	}

	textLower := strings.ToLower(text)
	var detectedKeywords []string
	var matchDetails []KeywordMatchDetail

	for _, keyword := range cf.config.SensitiveKeywords {
		keywordLower := strings.ToLower(keyword)
		if strings.Contains(textLower, keywordLower) {
			detectedKeywords = append(detectedKeywords, keyword)

			// 找到匹配的位置
			startPos := strings.Index(textLower, keywordLower)
			if startPos != -1 {
				// 获取上下文
				contextStart := startPos - 20
				if contextStart < 0 {
					contextStart = 0
				}
				contextEnd := startPos + len(keyword) + 20
				if contextEnd > len(text) {
					contextEnd = len(text)
				}

				matchDetails = append(matchDetails, KeywordMatchDetail{
					Keyword:  keyword,
					Position: startPos,
					Context:  text[contextStart:contextEnd],
				})

				utils.ChatDebug("发现敏感关键词: %s (位置: %d)", keyword, startPos)
			}
		}
	}

	return KeywordDetectionResult{
		HasSensitiveKeywords: len(detectedKeywords) > 0,
		DetectedKeywords:     detectedKeywords,
		MatchDetails:         matchDetails,
	}
}

// detectSemanticContent 语义检测
func (cf *ContentFilter) detectSemanticContent(ctx context.Context, userMessage string) (*SemanticDetectionResult, error) {
	if cf.openaiClient == nil {
		return &SemanticDetectionResult{
			IsSensitive: false,
			Error:       "OpenAI client not initialized",
		}, nil
	}

	// 构建检测提示词
	detectionPrompt := fmt.Sprintf(`%s

Strict output rule:
	•	Your entire output must be one word only, either related or safe
	•	Do not include punctuation, whitespace, or any other content
	•	Do not explain your reasoning or give commentary

Begin filtering.

Input: %s`, cf.config.LLMCheckingPrompt, userMessage)

	utils.ChatDebug("调用语义检测API")

	resp, err := cf.openaiClient.CreateChatCompletion(ctx, openai.ChatCompletionRequest{
		Model: cf.config.Model,
		Messages: []openai.ChatCompletionMessage{
			{
				Role:    openai.ChatMessageRoleUser,
				Content: detectionPrompt,
			},
		},
		Temperature: 0.1,
		MaxTokens:   10,
	})

	if err != nil {
		return &SemanticDetectionResult{
			IsSensitive: false,
			Error:       err.Error(),
		}, err
	}

	if len(resp.Choices) == 0 {
		return &SemanticDetectionResult{
			IsSensitive: false,
			Error:       "no response from API",
		}, nil
	}

	llmResponse := strings.TrimSpace(resp.Choices[0].Message.Content)
	resultUpper := strings.ToUpper(llmResponse)
	isSensitive := !strings.Contains(resultUpper, "SAFE")

	utils.ChatDebug("语义检测结果: %s", llmResponse)

	confidence := "high"
	if !strings.Contains(strings.ToLower(llmResponse), "related") && !strings.Contains(strings.ToLower(llmResponse), "safe") {
		confidence = "medium"
	}

	return &SemanticDetectionResult{
		IsSensitive: isSensitive,
		LLMResponse: llmResponse,
		Confidence:  confidence,
		PromptUsed:  detectionPrompt,
	}, nil
}

// modifySystemPromptForDomain 修改domain请求的系统提示词
func (cf *ContentFilter) modifySystemPromptForDomain(req *domain.ChatRequest) {
	prompt := strings.TrimSpace(cf.config.DynamicPrompt)

	// 查找最后一个系统消息
	lastSystemIndex := -1
	for i, msg := range req.Messages {
		if msg.Role == "system" {
			lastSystemIndex = i
		}
	}

	if lastSystemIndex >= 0 {
		// 如果存在系统消息，在最后一个系统消息后追加内容
		existingContent := req.Messages[lastSystemIndex].Content
		req.Messages[lastSystemIndex].Content = existingContent + "\n\n" + prompt
		utils.ChatDebug("已在现有系统消息后添加安全引导提示词")
	} else {
		// 如果没有系统消息，创建新的系统消息并添加到开头
		newSystemMessage := domain.ChatMessage{
			Role:    "system",
			Content: prompt,
		}
		req.Messages = append([]domain.ChatMessage{newSystemMessage}, req.Messages...)
		utils.ChatDebug("已创建新的系统消息并添加安全引导提示词")
	}
}

// 注意：modifySystemPrompt方法已被modifySystemPromptForDomain替代

// 注意：modifySystemPromptForDomain方法用于domain model处理

// recordFilterResult 记录过滤结果到 metadata
func (cf *ContentFilter) recordFilterResult(metadata map[string]interface{}, result *ContentFilterResult, userMessage string) {
	if metadata == nil {
		return
	}

	// 创建检测记录
	generations := []map[string]interface{}{}

	// 记录关键词检测
	keywordGeneration := map[string]interface{}{
		"name": "regex_keyword_detection",
		"input": map[string]interface{}{
			"user_message":    userMessage,
			"detection_rules": cf.config.SensitiveKeywords,
			"task":            "keyword_pattern_matching",
		},
		"output": map[string]interface{}{
			"has_sensitive_keywords": result.KeywordResult.HasSensitiveKeywords,
			"detected_keywords":      result.KeywordResult.DetectedKeywords,
			"match_details":          result.KeywordResult.MatchDetails,
		},
		"tags": []string{"content-filter", "regex-detection", "keyword-matching"},
	}
	generations = append(generations, keywordGeneration)

	// 记录语义检测（如果有）
	if result.SemanticResult != nil {
		semanticGeneration := map[string]interface{}{
			"name":  "llm_semantic_detection",
			"model": cf.config.Model,
			"input": map[string]interface{}{
				"user_message":     userMessage,
				"detection_prompt": cf.config.LLMCheckingPrompt,
				"task":             "semantic_content_analysis",
			},
			"output": map[string]interface{}{
				"is_sensitive": result.SemanticResult.IsSensitive,
				"llm_response": result.SemanticResult.LLMResponse,
				"confidence":   result.SemanticResult.Confidence,
			},
			"tags": []string{"content-filter", "llm-detection", "semantic-analysis"},
		}

		if result.SemanticResult.Error != "" {
			semanticGeneration["error"] = result.SemanticResult.Error
		}

		generations = append(generations, semanticGeneration)
	}

	// 添加到metadata
	metadata["content_filter_generations"] = generations
	metadata["content_filter_result"] = result

	utils.ChatDebug("已将 %d 个检测记录添加到 metadata", len(generations))
}

// 注意：getLastUserMessageFromRequest方法已被getLastUserMessageFromDomainRequest替代

// GetConfig 获取配置
func (cf *ContentFilter) GetConfig() *ContentFilterConfig {
	return cf.config
}

// UpdateConfig 更新配置
func (cf *ContentFilter) UpdateConfig(config *ContentFilterConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}

	cf.config = config

	// 重新初始化OpenAI客户端
	if config.APIKey != "" {
		clientConfig := openai.DefaultConfig(config.APIKey)
		if config.BaseURL != "" {
			clientConfig.BaseURL = config.BaseURL
		}
		cf.openaiClient = openai.NewClientWithConfig(clientConfig)
	}

	utils.ChatInfo("内容过滤器配置已更新")
	return nil
}

// Global instance for convenience
var GlobalContentFilter = NewContentFilter(nil)
