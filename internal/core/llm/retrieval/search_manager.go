package retrieval

import (
	"errors"
	"fmt"
	"log"
	"strings"

	"hkchat_api/internal/config"
	"hkchat_api/internal/core/di"
	"hkchat_api/internal/core/llm"
	"hkchat_api/internal/core/llm/retrieval/web"
)

// getLLMGateway 获取 LLM 网关实例
func getLLMGateway() (*llm.Gateway, error) {
	// 直接从 DI 容器获取全局 LLM 网关
	if gateway := di.GetGlobalLLMGateway(); gateway != nil {
		return gateway, nil
	}

	// 如果还是获取不到，说明 DI 容器可能未初始化
	return nil, fmt.Errorf("no LLM gateway available")
}

// WebSearchRequest 网络搜索请求
type WebSearchRequest struct {
	Messages []Message              `json:"messages"`
	Engine   string                 `json:"engine"`
	UserID   string                 `json:"user_id,omitempty"`
	ChatID   string                 `json:"chat_id,omitempty"`
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

// WebSearchResponse 网络搜索响应
type WebSearchResponse struct {
	Status       bool               `json:"status"`
	Results      []web.SearchResult `json:"results"`
	Queries      []string           `json:"queries"`
	TimeRange    string             `json:"time_range"`
	Topic        string             `json:"topic"`
	Engine       string             `json:"engine"`
	Error        string             `json:"error,omitempty"`
	TotalResults int                `json:"total_results"`
}

// SearchWebWithContext 根据上下文进行完整的网络搜索流程
// 1. 分析对话历史生成搜索查询
// 2. 根据查询调用相应的搜索引擎
// 3. 返回搜索结果
func SearchWebWithContext(request *WebSearchRequest) (*WebSearchResponse, error) {
	// 验证请求参数
	if len(request.Messages) == 0 {
		return &WebSearchResponse{
			Status: false,
			Error:  "No messages provided for context analysis",
		}, errors.New("No messages provided")
	}

	// 获取 LLM 网关
	llmGateway, err := getLLMGateway()
	if err != nil {
		return &WebSearchResponse{
			Status: false,
			Error:  fmt.Sprintf("Failed to get LLM gateway: %v", err),
		}, err
	}

	if request.Engine == "" {
		request.Engine = "tavily" // 默认使用 Tavily
	}

	// 验证搜索引擎是否可用
	if err := ValidateEngine(request.Engine); err != nil {
		return &WebSearchResponse{
			Status: false,
			Error:  fmt.Sprintf("Search engine validation failed: %v", err),
		}, err
	}

	log.Printf("Starting web search with context, engine: %s", request.Engine)

	// 第一步：生成搜索查询
	queryRequest := &QueryGenerationRequest{
		Type:     "web_search",
		Model:    "HKGAI-V1", //
		Messages: request.Messages,
		ChatID:   request.ChatID,
		Metadata: request.Metadata,
	}

	queryResponse, err := GenerateQueries(llmGateway, queryRequest)
	if err != nil {
		log.Printf("Failed to generate queries: %v", err)
		return &WebSearchResponse{
			Status: false,
			Error:  fmt.Sprintf("Query generation failed: %v", err),
		}, err
	}

	// 检查是否生成了有效的查询
	if len(queryResponse.Queries) == 0 {
		log.Println("No queries generated, search not needed")
		return &WebSearchResponse{
			Status:       true,
			Results:      []web.SearchResult{},
			Queries:      []string{},
			TimeRange:    queryResponse.TimeRange,
			Topic:        queryResponse.Topic,
			Engine:       request.Engine,
			TotalResults: 0,
		}, nil
	}

	log.Printf("Generated %d queries: %v", len(queryResponse.Queries), queryResponse.Queries)
	log.Printf("Time range: %s, Topic: %s", queryResponse.TimeRange, queryResponse.Topic)

	// 第二步：执行搜索
	var allResults []web.SearchResult
	var searchErrors []string

	// 将多个查询词用换行符拼接成一个查询字符串
	combinedQuery := strings.Join(queryResponse.Queries, "\n")
	log.Printf("Executing search with combined query: %s", combinedQuery)

	results, err := SearchWeb(
		request.Engine,
		combinedQuery,
		queryResponse.TimeRange,
		queryResponse.Topic,
	)

	if err != nil {
		errorMsg := fmt.Sprintf("Search failed for combined query: %v", err)
		log.Printf(errorMsg)
		searchErrors = append(searchErrors, errorMsg)
		allResults = []web.SearchResult{}
	} else {
		allResults = results
	}

	// 获取配置限制结果数量
	tavilyConfig := config.GetTavilyConfig()
	maxResults := 10 // 默认值
	if tavilyConfig != nil && tavilyConfig.MaxResults > 0 {
		maxResults = tavilyConfig.MaxResults
	}

	if len(allResults) > maxResults {
		allResults = allResults[:maxResults]
	}

	// 构建响应
	response := &WebSearchResponse{
		Status:       true,
		Results:      allResults,
		Queries:      queryResponse.Queries,
		TimeRange:    queryResponse.TimeRange,
		Topic:        queryResponse.Topic,
		Engine:       request.Engine,
		TotalResults: len(allResults),
	}

	// 如果有搜索错误但仍有结果，记录错误但不失败
	if len(searchErrors) > 0 {
		if len(allResults) == 0 {
			response.Status = false
			response.Error = strings.Join(searchErrors, "; ")
			return response, errors.New(response.Error)
		} else {
			log.Printf("Some searches failed but got %d results: %v",
				len(allResults), searchErrors)
		}
	}

	log.Printf("Web search completed successfully, got %d results", len(allResults))
	return response, nil
}

// mergeSearchResults 合并搜索结果，去除重复项
func mergeSearchResults(existing, new []web.SearchResult) []web.SearchResult {
	// 使用 map 来跟踪已存在的链接
	linkMap := make(map[string]bool)

	// 标记已存在的链接
	for _, result := range existing {
		linkMap[result.Link] = true
	}

	// 添加新结果，跳过重复的链接
	merged := make([]web.SearchResult, len(existing))
	copy(merged, existing)

	for _, result := range new {
		if !linkMap[result.Link] {
			merged = append(merged, result)
			linkMap[result.Link] = true
		}
	}

	return merged
}

// SearchWebDirect 直接搜索（不使用查询生成）
func SearchWebDirect(
	engine string,
	query string,
	timeRange string,
	topic string,
) (*WebSearchResponse, error) {
	// 验证搜索引擎
	if err := ValidateEngine(engine); err != nil {
		return &WebSearchResponse{
			Status: false,
			Error:  fmt.Sprintf("Search engine validation failed: %v", err),
		}, err
	}

	// 执行搜索
	results, err := SearchWeb(engine, query, timeRange, topic)
	if err != nil {
		return &WebSearchResponse{
			Status: false,
			Error:  fmt.Sprintf("Search failed: %v", err),
		}, err
	}

	return &WebSearchResponse{
		Status:       true,
		Results:      results,
		Queries:      []string{query},
		TimeRange:    timeRange,
		Topic:        topic,
		Engine:       engine,
		TotalResults: len(results),
	}, nil
}

// SearchWeb 根据引擎类型进行 web 搜索
func SearchWeb(
	engine string,
	query string,
	timeRange string,
	topic string,
) ([]web.SearchResult, error) {
	// 检查查询是否为空
	if strings.TrimSpace(query) == "" {
		log.Println("Empty query provided to web search")
		return []web.SearchResult{}, nil
	}

	switch strings.ToLower(engine) {
	case "tavily":
		// 从配置中获取 Tavily 配置
		tavilyConfig := config.GetTavilyConfig()
		if tavilyConfig == nil || tavilyConfig.APIKey == "" {
			return nil, errors.New("No TAVILY_API_KEY found in configuration")
		}

		// 设置默认结果数量
		count := tavilyConfig.MaxResults
		if count <= 0 {
			count = 10 // 默认返回10个结果
		}

		// 调用 Tavily 搜索
		return web.SearchTavily(
			tavilyConfig.APIKey,
			query,
			count,
			[]string{}, // 暂时不使用域名过滤，可以后续从配置中获取
			timeRange,
			topic,
		)
	default:
		return nil, fmt.Errorf("Unsupported search engine: %s", engine)
	}
}

// GetAvailableEngines 获取可用的搜索引擎列表
func GetAvailableEngines() []string {
	var engines []string

	// 检查 Tavily 配置
	tavilyConfig := config.GetTavilyConfig()
	if tavilyConfig != nil && tavilyConfig.APIKey != "" {
		engines = append(engines, "tavily")
	}

	return engines
}

// ValidateEngine 验证搜索引擎是否可用
func ValidateEngine(engine string) error {
	switch strings.ToLower(engine) {
	case "tavily":
		tavilyConfig := config.GetTavilyConfig()
		if tavilyConfig == nil || tavilyConfig.APIKey == "" {
			return errors.New("TAVILY_API_KEY is required for Tavily search")
		}
		return nil
	default:
		return fmt.Errorf("Unsupported search engine: %s", engine)
	}
}
