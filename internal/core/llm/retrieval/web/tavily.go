package web

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"strings"
	"time"
)

// SearchResult 搜索结果结构体
type SearchResult struct {
	Link    string `json:"url"`
	Title   string `json:"title"`
	Content string `json:"content"`
}

// parseTimeRange 解析时间范围字符串为 Tavily API 格式
// 只支持标准值: "day", "week", "month", "year"
func parseTimeRange(timeRangeStr string) string {
	if timeRangeStr == "" {
		return ""
	}

	// 只支持标准值
	validValues := map[string]bool{
		"day":   true,
		"week":  true,
		"month": true,
		"year":  true,
	}

	lower := strings.ToLower(timeRangeStr)
	if validValues[lower] {
		return lower
	}

	// 默认回退
	return "day"
}

// getBlacklistFilteredResults 过滤黑名单域名
func getBlacklistFilteredResults(results []SearchResult, filterList []string) []SearchResult {
	if len(filterList) == 0 {
		return results
	}

	var filtered []SearchResult
	for _, result := range results {
		skip := false
		for _, domain := range filterList {
			if strings.Contains(result.Link, domain) {
				skip = true
				break
			}
		}
		if !skip {
			filtered = append(filtered, result)
		}
	}
	return filtered
}

// SearchTavily 使用 Tavily 搜索 API 进行搜索并返回 SearchResult 对象列表
func SearchTavily(
	apiKey string,
	query string,
	count int,
	filterList []string,
	timeRange string,
	topic string,
) ([]SearchResult, error) {
	// 检查查询是否为空
	if strings.TrimSpace(query) == "" {
		log.Println("Empty query provided to Tavily search")
		return []SearchResult{}, nil
	}

	// 检查 API key 是否存在
	if strings.TrimSpace(apiKey) == "" {
		log.Println("No Tavily API key provided")
		return nil, errors.New("Tavily API key is required")
	}

	url := "https://api.tavily.com/search"
	headers := map[string]string{
		"Content-Type":  "application/json",
		"Authorization": fmt.Sprintf("Bearer %s", apiKey),
	}

	// 使用更完整的请求格式，符合 Tavily API 规范
	data := map[string]interface{}{
		"query":                      strings.TrimSpace(query),
		"max_results":                count,
		"search_depth":               "basic",
		"include_answer":             false,
		"include_raw_content":        false,
		"include_images":             false,
		"include_image_descriptions": false,
		"include_domains":            []string{},
		"exclude_domains":            []string{},
	}

	// if strings.TrimSpace(timeRange) != "" {
	// 	data["time_range"] = timeRange
	// }
	// if strings.TrimSpace(topic) != "" {
	// 	data["topic"] = topic
	// }

	// 发送请求
	jsonData, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, err
	}

	for key, value := range headers {
		req.Header.Set(key, value)
	}

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)

	if err != nil {
		log.Printf("Tavily API HTTP error: %v", err)
		// 如果有 time_range 或 topic 参数，尝试移除它们重新请求
		if _, hasTimeRange := data["time_range"]; hasTimeRange || data["topic"] != nil {
			log.Println("Retrying without time_range and topic parameters")
			// 移除可能导致问题的参数
			delete(data, "time_range")
			delete(data, "topic")

			jsonData, _ = json.Marshal(data)
			req, _ = http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
			for key, value := range headers {
				req.Header.Set(key, value)
			}

			resp, retryErr := client.Do(req)
			if retryErr != nil {
				log.Printf("Tavily API retry also failed: %v", retryErr)
				return nil, err // 抛出原始异常
			}
			defer resp.Body.Close()
		} else {
			return nil, err
		}
	} else {
		defer resp.Body.Close()
	}

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		log.Printf("Response content: %s", string(body))
		return nil, fmt.Errorf("HTTP error: %d", resp.StatusCode)
	}

	var jsonResponse struct {
		Results []map[string]interface{} `json:"results"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&jsonResponse); err != nil {
		return nil, err
	}

	results := make([]SearchResult, 0, len(jsonResponse.Results))
	for _, result := range jsonResponse.Results {
		url, _ := result["url"].(string)
		title, _ := result["title"].(string)
		content, _ := result["content"].(string)

		results = append(results, SearchResult{
			Link:    url,
			Title:   title,
			Content: content,
		})
	}

	// 打印过滤前的results
	fmt.Printf("过滤前的results: %+v\n", results)

	if len(filterList) > 0 {
		results = getBlacklistFilteredResults(results, filterList)
	}

	// 打印过滤后的results
	fmt.Printf("过滤后的results: %+v\n", results)

	return results, nil
}
