package retrieval

import (
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"strings"
	"time"

	"hkchat_api/internal/core/llm"
	"hkchat_api/internal/models/domain"
	"hkchat_api/pkg/prompts"
)

// QueryGenerationConfig 查询生成配置
type QueryGenerationConfig struct {
	EnableSearchQueryGeneration    bool
	EnableRetrievalQueryGeneration bool
	QueryGenerationPromptTemplate  string
	TaskModel                      string
	TaskModelExternal              string
}

// User 用户信息
type User struct {
	ID    string `json:"id"`
	Email string `json:"email"`
	Name  string `json:"name"`
}

// Message 消息结构
type Message struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// QueryGenerationRequest 查询生成请求
type QueryGenerationRequest struct {
	Type     string                 `json:"type"`
	Model    string                 `json:"model"`
	Messages []Message              `json:"messages"`
	ChatID   string                 `json:"chat_id,omitempty"`
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

// QueryGenerationResponse 查询生成响应
type QueryGenerationResponse struct {
	Status    bool        `json:"status"`
	Content   string      `json:"content"`
	Error     string      `json:"error,omitempty"`
	Data      interface{} `json:"data,omitempty"`
	Queries   []string    `json:"queries,omitempty"`
	TimeRange string      `json:"time_range,omitempty"`
	Topic     string      `json:"topic,omitempty"`
}

// GeneratedQueryResult 生成的查询结果结构
type GeneratedQueryResult struct {
	Queries   []string `json:"queries"`
	TimeRange string   `json:"time_range"`
	Topic     string   `json:"topic"`
}

// queryGenerationTemplate 生成查询模板内容
func queryGenerationTemplate(template string, messages []Message, currentDate string) string {
	// 获取最后3条消息
	var recentMessages []Message
	if len(messages) > 3 {
		recentMessages = messages[len(messages)-3:]
	} else {
		recentMessages = messages
	}

	// 构建消息历史
	var messageHistory strings.Builder
	for _, msg := range recentMessages {
		messageHistory.WriteString(fmt.Sprintf("%s: %s\n", msg.Role, msg.Content))
	}

	// 模板替换
	content := strings.ReplaceAll(template, "{{CURRENT_DATE}}", currentDate)
	content = strings.ReplaceAll(content, "{{MESSAGES:END:3}}", messageHistory.String())

	return content
}

// getLastUserMessage 获取最后一条用户消息
func getLastUserMessage(messages []Message) string {
	for i := len(messages) - 1; i >= 0; i-- {
		if messages[i].Role == "user" {
			return messages[i].Content
		}
	}
	return ""
}

// GenerateQueries 生成搜索查询 (简化版)
func GenerateQueries(llmGateway *llm.Gateway, request *QueryGenerationRequest) (*QueryGenerationResponse, error) {
	// 基本验证
	if len(request.Messages) == 0 {
		return &QueryGenerationResponse{
			Status: false,
			Error:  "No messages provided",
		}, errors.New("No messages provided")
	}

	if llmGateway == nil {
		return &QueryGenerationResponse{
			Status: false,
			Error:  "LLM gateway is nil",
		}, errors.New("LLM gateway is nil")
	}

	log.Printf("generating queries for type: %s", request.Type)

	// 使用默认提示模板
	template := prompts.DefaultQueryGenerationPromptTemplate

	// 生成内容，使用当前日期
	currentDate := time.Now().Format("2006-01-02")
	content := queryGenerationTemplate(template, request.Messages, currentDate)

	// 构建聊天完成请求
	chatReq := &domain.ChatRequest{
		Model: request.Model,
		Messages: []domain.ChatMessage{
			{Role: "user", Content: content},
		},
		Stream: false,
	}

	// 调用 LLM Gateway 的聊天完成API
	response, err := llmGateway.ProcessChatCompletion(chatReq, "system") // 使用系统用户ID
	if err != nil {
		return &QueryGenerationResponse{
			Status: false,
			Error:  err.Error(),
		}, err
	}

	// 提取响应内容
	responseContent := ""
	if response != nil && len(response.Choices) > 0 {
		responseContent = response.Choices[0].Message.Content
	}

	if responseContent == "" {
		return &QueryGenerationResponse{
			Status: false,
			Error:  "Empty response from chat completion",
		}, errors.New("Empty response from chat completion")
	}

	// 解析生成的查询结果
	queryResult, parseErr := ParseGeneratedQueryResult(responseContent)
	if parseErr != nil {
		log.Printf("Failed to parse query result: %v", parseErr)

		// 解析失败时的备用方案：提取最后一条用户消息作为搜索查询
		lastUserMessage := getLastUserMessage(request.Messages)
		if lastUserMessage == "" {
			lastUserMessage = "search query" // 如果没有用户消息，使用默认查询
		}

		log.Printf("Using fallback query: %s", lastUserMessage)

		return &QueryGenerationResponse{
			Status:    true,
			Content:   responseContent,
			Data:      chatReq,
			Queries:   []string{lastUserMessage},
			TimeRange: "",
			Topic:     "general",
		}, nil
	}

	return &QueryGenerationResponse{
		Status:    true,
		Content:   responseContent,
		Data:      chatReq,
		Queries:   queryResult.Queries,
		TimeRange: queryResult.TimeRange,
		Topic:     queryResult.Topic,
	}, nil
}

// ParseGeneratedQueryResult 解析生成的查询结果
func ParseGeneratedQueryResult(content string) (*GeneratedQueryResult, error) {
	// 清理内容，移除可能的markdown代码块标记
	content = strings.TrimSpace(content)
	content = strings.TrimPrefix(content, "```json")
	content = strings.TrimPrefix(content, "```")
	content = strings.TrimSuffix(content, "```")
	content = strings.TrimSpace(content)

	// 查找JSON对象的开始和结束位置
	bracketStart := strings.Index(content, "{")
	bracketEnd := strings.LastIndex(content, "}") + 1

	// 检查是否找到了有效的JSON对象
	if bracketStart == -1 || bracketEnd == -1 || bracketStart >= bracketEnd {
		return nil, fmt.Errorf("No JSON object found in the response")
	}

	// 提取JSON字符串部分
	jsonStr := content[bracketStart:bracketEnd]

	// 尝试解析JSON对象格式
	var result GeneratedQueryResult
	if err := json.Unmarshal([]byte(jsonStr), &result); err != nil {
		return nil, fmt.Errorf("failed to parse JSON: %v", err)
	}

	// 设置默认值
	if result.Topic == "" {
		result.Topic = "general"
	}

	return &result, nil
}

// ParseGeneratedQueries 解析生成的查询结果 (保持向后兼容)
func ParseGeneratedQueries(content string) ([]string, error) {
	result, err := ParseGeneratedQueryResult(content)
	if err != nil {
		// 如果新格式解析失败，尝试旧格式
		var queries []string

		// 清理内容，移除可能的markdown代码块标记
		content = strings.TrimSpace(content)
		content = strings.TrimPrefix(content, "```json")
		content = strings.TrimPrefix(content, "```")
		content = strings.TrimSuffix(content, "```")
		content = strings.TrimSpace(content)

		// 尝试解析JSON数组
		if err := json.Unmarshal([]byte(content), &queries); err != nil {
			// 如果JSON解析失败，尝试按行分割
			lines := strings.Split(content, "\n")
			for _, line := range lines {
				line = strings.TrimSpace(line)
				if line != "" && !strings.HasPrefix(line, "//") && !strings.HasPrefix(line, "#") {
					// 移除可能的引号和其他标记
					line = strings.Trim(line, "\"'")
					line = strings.TrimPrefix(line, "- ")
					line = strings.TrimPrefix(line, "* ")
					if line != "" {
						queries = append(queries, line)
					}
				}
			}
		}

		if len(queries) == 0 {
			return nil, errors.New("No valid queries found in response")
		}

		return queries, nil
	}

	return result.Queries, nil
}
