package llm

import (
	"fmt"
	"hkchat_api/pkg/utils"
	"strings"
	"time"
)

// ChatMessage 聊天消息结构
type ChatMessage struct {
	Role    string      `json:"role"`
	Content interface{} `json:"content"`
}

// ContentItem 内容项（用于处理复杂内容）
type ContentItem struct {
	Type string `json:"type"`
	Text string `json:"text"`
}

// MessageUtils 消息处理工具类
type MessageUtils struct{}

// NewMessageUtils 创建消息处理工具实例
func NewMessageUtils() *MessageUtils {
	return &MessageUtils{}
}

// GetContentFromMessage 从消息中获取内容（对应Python的get_content_from_message）
func (u *MessageUtils) GetContentFromMessage(message ChatMessage) string {
	if message.Content == nil {
		return ""
	}

	// 处理列表格式的内容
	if contentList, ok := message.Content.([]interface{}); ok {
		for _, item := range contentList {
			if itemMap, ok := item.(map[string]interface{}); ok {
				if itemType, exists := itemMap["type"]; exists && itemType == "text" {
					if text, exists := itemMap["text"]; exists {
						if textStr, ok := text.(string); ok {
							return textStr
						}
					}
				}
			}
		}
	}

	// 处理字符串格式的内容
	if contentStr, ok := message.Content.(string); ok {
		return contentStr
	}

	return ""
}

// GetLastUserMessage 获取最后一个用户消息内容（对应Python的get_last_user_message）
func (u *MessageUtils) GetLastUserMessage(messages []ChatMessage) string {
	message := u.GetLastUserMessageItem(messages)
	if message == nil {
		return ""
	}
	return u.GetContentFromMessage(*message)
}

// GetLastUserMessageItem 获取最后一个用户消息项（对应Python的get_last_user_message_item）
func (u *MessageUtils) GetLastUserMessageItem(messages []ChatMessage) *ChatMessage {
	for i := len(messages) - 1; i >= 0; i-- {
		if messages[i].Role == "user" {
			return &messages[i]
		}
	}
	return nil
}

// GetAllUserMessages 获取所有用户消息（对应Python的get_all_user_messages）
func (u *MessageUtils) GetAllUserMessages(messages []ChatMessage) []ChatMessage {
	var userMessages []ChatMessage
	for _, message := range messages {
		if message.Role == "user" {
			userMessages = append(userMessages, message)
		}
	}
	return userMessages
}

// GetAllUserMessageContents 获取所有用户消息内容（对应Python的get_all_user_message_contents）
func (u *MessageUtils) GetAllUserMessageContents(messages []ChatMessage) []string {
	var contents []string
	for _, message := range messages {
		if message.Role == "user" {
			content := u.GetContentFromMessage(message)
			if content != "" {
				contents = append(contents, strings.TrimSpace(content))
			}
		}
	}
	return contents
}

// GetLastAssistantMessage 获取最后一个助手消息内容（对应Python的get_last_assistant_message）
func (u *MessageUtils) GetLastAssistantMessage(messages []ChatMessage) string {
	message := u.GetLastAssistantMessageItem(messages)
	if message == nil {
		return ""
	}
	return u.GetContentFromMessage(*message)
}

// GetLastAssistantMessageItem 获取最后一个助手消息项（对应Python的get_last_assistant_message_item）
func (u *MessageUtils) GetLastAssistantMessageItem(messages []ChatMessage) *ChatMessage {
	for i := len(messages) - 1; i >= 0; i-- {
		if messages[i].Role == "assistant" {
			return &messages[i]
		}
	}
	return nil
}

// GetSystemMessage 获取系统消息（对应Python的get_system_message）
func (u *MessageUtils) GetSystemMessage(messages []ChatMessage) *ChatMessage {
	for _, message := range messages {
		if message.Role == "system" {
			return &message
		}
	}
	return nil
}

// RemoveSystemMessage 移除系统消息（对应Python的remove_system_message）
func (u *MessageUtils) RemoveSystemMessage(messages []ChatMessage) []ChatMessage {
	var result []ChatMessage
	for _, message := range messages {
		if message.Role != "system" {
			result = append(result, message)
		}
	}
	return result
}

// PopSystemMessage 弹出系统消息（对应Python的pop_system_message）
func (u *MessageUtils) PopSystemMessage(messages []ChatMessage) (*ChatMessage, []ChatMessage) {
	systemMessage := u.GetSystemMessage(messages)
	remainingMessages := u.RemoveSystemMessage(messages)
	return systemMessage, remainingMessages
}

// AddOrUpdateSystemMessage 添加或更新系统消息（对应Python的add_or_update_system_message）
func (u *MessageUtils) AddOrUpdateSystemMessage(content string, messages []ChatMessage, shouldAppend bool) []ChatMessage {
	if len(messages) > 0 && messages[0].Role == "system" {
		// 更新现有的系统消息
		existingContent := u.GetContentFromMessage(messages[0])
		if shouldAppend {
			messages[0].Content = fmt.Sprintf("%s\n%s", existingContent, content)
		} else {
			messages[0].Content = fmt.Sprintf("%s\n%s", content, existingContent)
		}
		return messages
	}

	// 在开头插入新的系统消息
	systemMessage := ChatMessage{
		Role:    "system",
		Content: content,
	}
	return append([]ChatMessage{systemMessage}, messages...)
}

// AddOrUpdateUserMessage 添加或更新用户消息（对应Python的add_or_update_user_message）
func (u *MessageUtils) AddOrUpdateUserMessage(content string, messages []ChatMessage) []ChatMessage {
	if len(messages) > 0 && messages[len(messages)-1].Role == "user" {
		// 更新最后一个用户消息
		lastIndex := len(messages) - 1
		existingContent := u.GetContentFromMessage(messages[lastIndex])
		messages[lastIndex].Content = fmt.Sprintf("%s\n%s", existingContent, content)
		return messages
	}

	// 在末尾添加新的用户消息
	userMessage := ChatMessage{
		Role:    "user",
		Content: content,
	}
	return append(messages, userMessage)
}

// AppendOrUpdateAssistantMessage 添加或更新助手消息（对应Python的append_or_update_assistant_message）
func (u *MessageUtils) AppendOrUpdateAssistantMessage(content string, messages []ChatMessage) []ChatMessage {
	if len(messages) > 0 && messages[len(messages)-1].Role == "assistant" {
		// 更新最后一个助手消息
		lastIndex := len(messages) - 1
		existingContent := u.GetContentFromMessage(messages[lastIndex])
		messages[lastIndex].Content = fmt.Sprintf("%s\n%s", existingContent, content)
		return messages
	}

	// 在末尾添加新的助手消息
	assistantMessage := ChatMessage{
		Role:    "assistant",
		Content: content,
	}
	return append(messages, assistantMessage)
}

// PrependToFirstUserMessageContent 在第一个用户消息前添加内容（对应Python的prepend_to_first_user_message_content）
func (u *MessageUtils) PrependToFirstUserMessageContent(content string, messages []ChatMessage) []ChatMessage {
	for i, message := range messages {
		if message.Role == "user" {
			// 处理复杂内容格式
			if contentList, ok := message.Content.([]interface{}); ok {
				for j, item := range contentList {
					if itemMap, ok := item.(map[string]interface{}); ok {
						if itemType, exists := itemMap["type"]; exists && itemType == "text" {
							if text, exists := itemMap["text"]; exists {
								if textStr, ok := text.(string); ok {
									itemMap["text"] = fmt.Sprintf("%s\n%s", content, textStr)
									contentList[j] = itemMap
									break
								}
							}
						}
					}
				}
				messages[i].Content = contentList
			} else if contentStr, ok := message.Content.(string); ok {
				// 处理字符串格式
				messages[i].Content = fmt.Sprintf("%s\n%s", content, contentStr)
			}
			break
		}
	}
	return messages
}

// GetMessagesContent 获取消息内容摘要（对应Python的get_messages_content）
func (u *MessageUtils) GetMessagesContent(messages []ChatMessage) string {
	var parts []string
	for _, message := range messages {
		content := u.GetContentFromMessage(message)
		if content != "" {
			parts = append(parts, fmt.Sprintf("%s: %s", strings.ToUpper(message.Role), content))
		}
	}
	return strings.Join(parts, "\n")
}

// GetMessagesWithLimitedAssistant 获取限制长度的助手消息（对应Python的get_messages_with_limited_assistant）
func (u *MessageUtils) GetMessagesWithLimitedAssistant(messages []ChatMessage, maxLength int) []ChatMessage {
	if maxLength <= 0 {
		maxLength = 50
	}

	var processedMessages []ChatMessage
	for _, message := range messages {
		newMessage := message // 复制消息

		if message.Role == "assistant" {
			content := u.GetContentFromMessage(message)
			if content != "" {
				content = strings.TrimSpace(content)
				// 截断助手消息为指定长度
				if len(content) > maxLength {
					content = content[:maxLength] + "..."
				}

				// 处理复杂内容格式
				if contentList, ok := message.Content.([]interface{}); ok {
					var newContent []interface{}
					for _, item := range contentList {
						if itemMap, ok := item.(map[string]interface{}); ok {
							if itemType, exists := itemMap["type"]; exists && itemType == "text" {
								newItem := make(map[string]interface{})
								for k, v := range itemMap {
									newItem[k] = v
								}
								newItem["text"] = content
								newContent = append(newContent, newItem)
							} else {
								newContent = append(newContent, item)
							}
						} else {
							newContent = append(newContent, item)
						}
					}
					newMessage.Content = newContent
				} else {
					// 处理字符串格式
					newMessage.Content = content
				}
			}
		}

		processedMessages = append(processedMessages, newMessage)
	}

	return processedMessages
}

// OpenAIChatMessageTemplate 创建OpenAI聊天消息模板（对应Python的openai_chat_message_template）
func (u *MessageUtils) OpenAIChatMessageTemplate(model string) map[string]interface{} {
	return map[string]interface{}{
		"id":      fmt.Sprintf("%s-%s", model, utils.GenerateID()),
		"created": time.Now().Unix(),
		"model":   model,
		"choices": []map[string]interface{}{
			{
				"index":         0,
				"logprobs":      nil,
				"finish_reason": nil,
			},
		},
	}
}

// OpenAIChatChunkMessageTemplate 创建OpenAI聊天块消息模板（对应Python的openai_chat_chunk_message_template）
func (u *MessageUtils) OpenAIChatChunkMessageTemplate(model string, content *string, reasoningContent *string, toolCalls []map[string]interface{}, usage map[string]interface{}) map[string]interface{} {
	template := u.OpenAIChatMessageTemplate(model)
	template["object"] = "chat.completion.chunk"

	choices := template["choices"].([]map[string]interface{})
	choices[0]["index"] = 0
	choices[0]["delta"] = map[string]interface{}{}

	delta := choices[0]["delta"].(map[string]interface{})

	if content != nil {
		delta["content"] = *content
	}

	if reasoningContent != nil {
		delta["reasoning_content"] = *reasoningContent
	}

	if toolCalls != nil {
		delta["tool_calls"] = toolCalls
	}

	if content == nil && toolCalls == nil {
		choices[0]["finish_reason"] = "stop"
	}

	if usage != nil {
		template["usage"] = usage
	}

	return template
}

// OpenAIChatCompletionMessageTemplate 创建OpenAI聊天完成消息模板（对应Python的openai_chat_completion_message_template）
func (u *MessageUtils) OpenAIChatCompletionMessageTemplate(model string, message *string, reasoningContent *string, toolCalls []map[string]interface{}, usage map[string]interface{}) map[string]interface{} {
	template := u.OpenAIChatMessageTemplate(model)
	template["object"] = "chat.completion"

	choices := template["choices"].([]map[string]interface{})

	if message != nil {
		messageMap := map[string]interface{}{
			"role":    "assistant",
			"content": *message,
		}

		if reasoningContent != nil {
			messageMap["reasoning_content"] = *reasoningContent
		}

		if toolCalls != nil {
			messageMap["tool_calls"] = toolCalls
		}

		choices[0]["message"] = messageMap
	}

	choices[0]["finish_reason"] = "stop"

	if usage != nil {
		template["usage"] = usage
	}

	return template
}

// ValidateMessages 验证消息列表的有效性
func (u *MessageUtils) ValidateMessages(messages []ChatMessage) error {
	if len(messages) == 0 {
		return fmt.Errorf("消息列表不能为空")
	}

	for i, message := range messages {
		if message.Role == "" {
			return fmt.Errorf("消息[%d]角色不能为空", i)
		}

		if message.Role != "system" && message.Role != "user" && message.Role != "assistant" {
			return fmt.Errorf("消息[%d]角色无效: %s", i, message.Role)
		}

		content := u.GetContentFromMessage(message)
		if content == "" {
			return fmt.Errorf("消息[%d]内容不能为空", i)
		}
	}

	return nil
}

// CountTokensApprox 近似计算消息的token数量（简单实现）
func (u *MessageUtils) CountTokensApprox(messages []ChatMessage) int {
	totalChars := 0
	for _, message := range messages {
		content := u.GetContentFromMessage(message)
		totalChars += len(content)
		// 每个消息还有角色等元数据
		totalChars += len(message.Role) + 10
	}

	// 简单的token估算：平均每4个字符约等于1个token
	return totalChars / 4
}

// FilterMessagesByRole 按角色过滤消息
func (u *MessageUtils) FilterMessagesByRole(messages []ChatMessage, role string) []ChatMessage {
	var filtered []ChatMessage
	for _, message := range messages {
		if message.Role == role {
			filtered = append(filtered, message)
		}
	}
	return filtered
}

// GetMessageHistory 获取消息历史摘要
func (u *MessageUtils) GetMessageHistory(messages []ChatMessage, maxLength int) string {
	if maxLength <= 0 {
		maxLength = 100
	}

	var parts []string
	for _, message := range messages {
		content := u.GetContentFromMessage(message)
		if content != "" {
			if len(content) > maxLength {
				content = content[:maxLength] + "..."
			}
			parts = append(parts, fmt.Sprintf("[%s]: %s", message.Role, content))
		}
	}

	return strings.Join(parts, "\n")
}

// Global instance for convenience
var GlobalMessageUtils = NewMessageUtils()

// Convenience functions using global instance
func GetLastUserMessage(messages []ChatMessage) string {
	return GlobalMessageUtils.GetLastUserMessage(messages)
}

func GetContentFromMessage(message ChatMessage) string {
	return GlobalMessageUtils.GetContentFromMessage(message)
}

func GetAllUserMessages(messages []ChatMessage) []ChatMessage {
	return GlobalMessageUtils.GetAllUserMessages(messages)
}

func GetLastAssistantMessage(messages []ChatMessage) string {
	return GlobalMessageUtils.GetLastAssistantMessage(messages)
}

func GetSystemMessage(messages []ChatMessage) *ChatMessage {
	return GlobalMessageUtils.GetSystemMessage(messages)
}

func ValidateMessages(messages []ChatMessage) error {
	return GlobalMessageUtils.ValidateMessages(messages)
}
