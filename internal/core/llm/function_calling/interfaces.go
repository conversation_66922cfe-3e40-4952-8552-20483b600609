package function_calling

import (
	"hkchat_api/internal/core/interfaces"
	"hkchat_api/pkg/clients/llm"
)

// FunctionCall 函数调用结构
type FunctionCall struct {
	Name       string                 `json:"name"`
	Parameters map[string]interface{} `json:"parameters"`
}

// FunctionCallingInvokerFunc Function Calling调用器函数类型
type FunctionCallingInvokerFunc func(req interfaces.ChatRequest, userID string) (*llm.ChatResponse, error)
