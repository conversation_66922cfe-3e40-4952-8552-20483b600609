package function_calling

import (
	"fmt"
	"hkchat_api/pkg/utils"
)

// functionValidator 函数验证器实现
type functionValidator struct{}

// NewFunctionValidator 创建函数验证器实例
func NewFunctionValidator() *functionValidator {
	return &functionValidator{}
}

// ValidateParameters 验证函数参数
func (v *functionValidator) ValidateParameters(params map[string]interface{}, function map[string]interface{}) map[string]interface{} {
	validatedParams := make(map[string]interface{})

	// 获取函数规格
	spec, ok := function["spec"].(map[string]interface{})
	if !ok {
		utils.Debug("函数缺少规格定义，跳过参数验证")
		return params
	}

	parameters, ok := spec["parameters"].(map[string]interface{})
	if !ok {
		utils.Debug("函数规格缺少参数定义，跳过参数验证")
		return params
	}

	properties, ok := parameters["properties"].(map[string]interface{})
	if !ok {
		utils.Debug("函数参数缺少属性定义，跳过参数验证")
		return params
	}

	// 获取必需参数列表
	requiredParams := make(map[string]bool)
	if required, ok := parameters["required"].([]interface{}); ok {
		for _, req := range required {
			if reqStr, ok := req.(string); ok {
				requiredParams[reqStr] = true
			}
		}
	}

	// 验证并过滤参数
	for key, value := range params {
		if propDef, allowed := properties[key]; allowed {
			// 验证参数类型和值
			if v.validateParameterValue(key, value, propDef) {
				validatedParams[key] = value
			} else {
				utils.Warnf("参数 %s 验证失败，跳过", key)
			}
		} else {
			utils.Debugf("参数 %s 不在允许列表中，跳过", key)
		}
	}

	// 检查必需参数
	for reqParam := range requiredParams {
		if _, exists := validatedParams[reqParam]; !exists {
			utils.Warnf("缺少必需参数: %s", reqParam)
		}
	}

	utils.Debugf("参数验证完成，原始: %d，验证后: %d", len(params), len(validatedParams))
	return validatedParams
}

// ValidateFunctionExists 验证函数是否存在
func (v *functionValidator) ValidateFunctionExists(functionName string, functions map[string]interface{}) bool {
	if _, exists := functions[functionName]; !exists {
		utils.Warnf("函数 %s 不存在", functionName)
		return false
	}

	utils.Debugf("函数 %s 验证通过", functionName)
	return true
}

// validateParameterValue 验证参数值
func (v *functionValidator) validateParameterValue(paramName string, value interface{}, propDef interface{}) bool {
	propMap, ok := propDef.(map[string]interface{})
	if !ok {
		utils.Debugf("参数 %s 的定义格式无效", paramName)
		return true // 如果定义格式无效，允许通过
	}

	// 获取参数类型
	paramType, ok := propMap["type"].(string)
	if !ok {
		utils.Debugf("参数 %s 缺少类型定义", paramName)
		return true // 如果没有类型定义，允许通过
	}

	// 根据类型验证值
	switch paramType {
	case "string":
		if _, ok := value.(string); !ok {
			utils.Warnf("参数 %s 期望字符串类型，实际: %T", paramName, value)
			return false
		}
	case "integer", "number":
		switch value.(type) {
		case int, int32, int64, float32, float64:
			// 数字类型有效
		default:
			utils.Warnf("参数 %s 期望数字类型，实际: %T", paramName, value)
			return false
		}
	case "boolean":
		if _, ok := value.(bool); !ok {
			utils.Warnf("参数 %s 期望布尔类型，实际: %T", paramName, value)
			return false
		}
	case "array":
		if _, ok := value.([]interface{}); !ok {
			utils.Warnf("参数 %s 期望数组类型，实际: %T", paramName, value)
			return false
		}
	case "object":
		if _, ok := value.(map[string]interface{}); !ok {
			utils.Warnf("参数 %s 期望对象类型，实际: %T", paramName, value)
			return false
		}
	}

	// 验证枚举值
	if enum, ok := propMap["enum"].([]interface{}); ok {
		if !v.validateEnumValue(value, enum) {
			utils.Warnf("参数 %s 的值不在允许的枚举范围内", paramName)
			return false
		}
	}

	// 验证字符串长度
	if paramType == "string" {
		if str, ok := value.(string); ok {
			if minLen, ok := propMap["minLength"].(float64); ok && float64(len(str)) < minLen {
				utils.Warnf("参数 %s 长度过短，最小: %.0f，实际: %d", paramName, minLen, len(str))
				return false
			}
			if maxLen, ok := propMap["maxLength"].(float64); ok && float64(len(str)) > maxLen {
				utils.Warnf("参数 %s 长度过长，最大: %.0f，实际: %d", paramName, maxLen, len(str))
				return false
			}
		}
	}

	utils.Debugf("参数 %s 验证通过", paramName)
	return true
}

// validateEnumValue 验证枚举值
func (v *functionValidator) validateEnumValue(value interface{}, enum []interface{}) bool {
	for _, enumValue := range enum {
		if value == enumValue {
			return true
		}
	}
	return false
}

// ValidateFunctionDefinition 验证函数定义的完整性
func (v *functionValidator) ValidateFunctionDefinition(function map[string]interface{}) error {
	// 检查必需字段
	if _, ok := function["spec"]; !ok {
		return fmt.Errorf("函数缺少 spec 定义")
	}

	spec, ok := function["spec"].(map[string]interface{})
	if !ok {
		return fmt.Errorf("函数 spec 格式无效")
	}

	// 检查函数名称
	if _, ok := spec["name"]; !ok {
		return fmt.Errorf("函数缺少名称")
	}

	// 检查函数描述
	if _, ok := spec["description"]; !ok {
		utils.Warn("函数缺少描述")
	}

	// 检查参数定义
	if params, ok := spec["parameters"]; ok {
		if paramsMap, ok := params.(map[string]interface{}); ok {
			if _, ok := paramsMap["type"]; !ok {
				utils.Warn("函数参数缺少类型定义")
			}
		}
	}

	utils.Debug("函数定义验证通过")
	return nil
}
