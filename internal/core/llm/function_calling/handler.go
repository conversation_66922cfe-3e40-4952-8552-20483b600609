package function_calling

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"hkchat_api/internal/core/factories"
	"hkchat_api/internal/core/interfaces"
	"hkchat_api/internal/models/domain"
	"hkchat_api/pkg/clients/llm"
	"hkchat_api/pkg/utils"
)

// FunctionCallingHandler Function Calling 处理器实现
type FunctionCallingHandler struct {
	executor       *functionExecutor
	validator      *functionValidator
	modelRouter    FunctionCallingInvokerFunc
	messageFactory interfaces.MessageFactory
}

// NewFunctionCallingHandler 创建 Function Calling 处理器实例
func NewFunctionCallingHandler(modelRouter FunctionCallingInvokerFunc) *FunctionCallingHandler {
	return &FunctionCallingHandler{
		executor:       NewFunctionExecutor(),
		validator:      NewFunctionValidator(),
		modelRouter:    modelRouter,
		messageFactory: factories.NewCoreMessageFactory(),
	}
}

// ChatCompletionToolsHandler 聊天完成工具处理器（核心方法，对应Python的chat_completion_tools_handler）
func (h *FunctionCallingHandler) ChatCompletionToolsHandler(
	ctx context.Context,
	req interfaces.ChatRequest,
	user interfaces.User,
	tools map[string]interface{},
	extraParams map[string]interface{},
) (interfaces.ChatRequest, map[string]interface{}, error) {
	utils.Infof("开始处理Function Calling，工具数量: %d", len(tools))

	// 转换为可修改的domain model
	domainReq := h.convertInterfaceToDomain(req)

	// 初始化变量
	skipFiles := false
	sources := []map[string]interface{}{}
	enableWebSearch := false

	// 1. 提取工具规格
	specs := h.extractToolSpecs(tools)
	if len(specs) == 0 {
		return req, map[string]interface{}{
			"sources":           sources,
			"enable_web_search": enableWebSearch,
		}, nil
	}

	// 2. 获取Core消息
	coreMessages := req.GetMessages()

	// 3. 构建Function Calling提示词
	toolsSpecs, _ := json.Marshal(specs)
	functionCallingPrompt := h.generateFunctionCallingPrompt(string(toolsSpecs))

	// 4. 构建AI请求载荷
	taskModelID := h.getTaskModelID(req.GetModel())
	corePayload := h.getToolsFunctionCallingPayload(coreMessages, taskModelID, functionCallingPrompt)

	// 5. 调用AI模型生成Function Calling决策
	response, err := h.generateChatCompletion(ctx, corePayload, user.GetID())
	if err != nil {
		utils.Errorf("Function Calling AI请求失败: %v", err)
		return req, map[string]interface{}{
			"sources":           sources,
			"enable_web_search": enableWebSearch,
		}, nil
	}

	// 6. 获取AI响应内容
	content, err := h.getContentFromResponse(response)
	if err != nil || content == "" {
		utils.Warnf("获取AI响应内容失败: %v", err)
		return req, map[string]interface{}{
			"sources":           sources,
			"enable_web_search": enableWebSearch,
		}, nil
	}

	// 7. 解析Function Calls
	functionCalls, err := h.parseFunctionCallsFromContent(content)
	if err != nil {
		utils.Warnf("解析Function Calls失败: %v", err)
		return req, map[string]interface{}{
			"sources":           sources,
			"enable_web_search": enableWebSearch,
		}, nil
	}

	// 8. 执行工具调用
	for _, functionCall := range functionCalls {
		err := h.handleToolCallDomain(ctx, functionCall, tools, domainReq, user, &sources, &skipFiles, &enableWebSearch)
		if err != nil {
			utils.Warnf("执行工具调用失败: %v", err)
			continue
		}
	}

	// 9. 处理文件跳过
	if skipFiles && len(domainReq.Files) > 0 {
		domainReq.Files = nil
	}

	utils.Infof("Function Calling处理完成，源数量: %d", len(sources))

	return domainReq, map[string]interface{}{
		"sources":           sources,
		"enable_web_search": enableWebSearch,
	}, nil
}


// extractToolSpecs 提取工具规格
func (h *FunctionCallingHandler) extractToolSpecs(tools map[string]interface{}) []map[string]interface{} {
	specs := make([]map[string]interface{}, 0, len(tools))

	for _, tool := range tools {
		if toolMap, ok := tool.(map[string]interface{}); ok {
			if spec, ok := toolMap["spec"].(map[string]interface{}); ok {
				specs = append(specs, spec)
			}
		}
	}

	return specs
}

// generateFunctionCallingPrompt 生成Function Calling提示词
func (h *FunctionCallingHandler) generateFunctionCallingPrompt(toolsSpecs string) string {
	// 默认模板（对应Python的DEFAULT_TOOLS_FUNCTION_CALLING_PROMPT_TEMPLATE）
	template := `You are a function calling AI model. You are provided with function signatures within <tools></tools> XML tags. You may call one or more functions to assist with the user query. Don't make assumptions about what values to plug into functions. Here are the available tools:

<tools>
%s
</tools>

For each function call return a json object with function name and parameters within <tool_call></tool_call> XML tags as follows:
<tool_call>
{"tool_calls": [{"name": "function_name", "parameters": {"param1": "value1", "param2": "value2"}}]}
</tool_call>`

	return fmt.Sprintf(template, toolsSpecs)
}

// getTaskModelID 获取任务模型ID
func (h *FunctionCallingHandler) getTaskModelID(originalModel string) string {
	// 简化实现，实际应该从配置中获取
	return originalModel
}

// getToolsFunctionCallingPayload 构建工具Function Calling载荷
func (h *FunctionCallingHandler) getToolsFunctionCallingPayload(messages []interfaces.Message, taskModelID, content string) *domain.ChatRequest {
	// 获取最后一个用户消息
	userMessage := h.getLastUserMessage(messages)

	// 构建历史记录（最近3条消息，倒序）
	history := []string{}
	start := len(messages) - 3
	if start < 0 {
		start = 0
	}

	for i := len(messages) - 1; i >= start; i-- {
		msg := messages[i]
		history = append(history, fmt.Sprintf("%s: \"\"\"%s\"\"\"",
			strings.ToUpper(msg.GetRole()), msg.GetContent()))
	}

	historyStr := strings.Join(history, "\n")
	prompt := fmt.Sprintf("History:\n%s\nQuery: %s", historyStr, userMessage)

	return &domain.ChatRequest{
		Model: taskModelID,
		Messages: []domain.ChatMessage{
			{
				Role:    "system",
				Content: content,
			},
			{
				Role:    "user",
				Content: fmt.Sprintf("Query: %s", prompt),
			},
		},
		Stream: false,
	}
}

// getLastUserMessage 获取最后一个用户消息
func (h *FunctionCallingHandler) getLastUserMessage(messages []interfaces.Message) string {
	for i := len(messages) - 1; i >= 0; i-- {
		if messages[i].GetRole() == "user" {
			return messages[i].GetContent()
		}
	}
	return ""
}

// generateChatCompletion 生成聊天完成
func (h *FunctionCallingHandler) generateChatCompletion(ctx context.Context, payload *domain.ChatRequest, userID string) (*llm.ChatResponse, error) {
	utils.Debugf("调用FunctionCallingInvoker处理Function Calling请求")
	// 直接使用domain model
	return h.modelRouter(payload, userID)
}

// getContentFromResponse 从响应中获取内容
func (h *FunctionCallingHandler) getContentFromResponse(response *llm.ChatResponse) (string, error) {
	if response == nil || len(response.Choices) == 0 {
		return "", fmt.Errorf("响应为空或没有选择项")
	}

	return response.Choices[0].Message.Content, nil
}

// parseFunctionCallsFromContent 从内容中解析Function Calls
func (h *FunctionCallingHandler) parseFunctionCallsFromContent(content string) ([]FunctionCall, error) {
	utils.Debugf("解析Function Calls，内容长度: %d", len(content))

	// 提取JSON部分（在<tool_call>标签之间或直接的JSON对象）
	var jsonStr string

	// 首先尝试提取<tool_call>标签内的内容
	toolCallStart := strings.Index(content, "<tool_call>")
	toolCallEnd := strings.Index(content, "</tool_call>")

	if toolCallStart != -1 && toolCallEnd != -1 && toolCallEnd > toolCallStart {
		jsonStr = content[toolCallStart+11 : toolCallEnd]
	} else {
		// 如果没有标签，尝试直接提取JSON对象
		start := strings.Index(content, "{")
		end := strings.LastIndex(content, "}") + 1

		if start == -1 || end <= start {
			return nil, fmt.Errorf("未找到有效的JSON对象")
		}

		jsonStr = content[start:end]
	}

	jsonStr = strings.TrimSpace(jsonStr)
	utils.Debugf("提取的JSON: %s", jsonStr)

	// 解析JSON
	var result map[string]interface{}
	if err := json.Unmarshal([]byte(jsonStr), &result); err != nil {
		return nil, fmt.Errorf("解析JSON失败: %v", err)
	}

	// 提取function calls
	var functionCalls []FunctionCall

	// 检查tool_calls字段
	if toolCallsData, ok := result["tool_calls"].([]interface{}); ok {
		for _, callData := range toolCallsData {
			if callMap, ok := callData.(map[string]interface{}); ok {
				functionCall := FunctionCall{}
				if name, ok := callMap["name"].(string); ok {
					functionCall.Name = name
				}
				if params, ok := callMap["parameters"].(map[string]interface{}); ok {
					functionCall.Parameters = params
				}
				functionCalls = append(functionCalls, functionCall)
			}
		}
	} else {
		// 如果没有tool_calls数组，可能是单个函数调用
		if name, ok := result["name"].(string); ok {
			functionCall := FunctionCall{Name: name}
			if params, ok := result["parameters"].(map[string]interface{}); ok {
				functionCall.Parameters = params
			}
			functionCalls = append(functionCalls, functionCall)
		}
	}

	utils.Debugf("解析完成，找到 %d 个function calls", len(functionCalls))
	return functionCalls, nil
}

// handleToolCall 处理工具调用（对应Python的tool_call_handler）
func (h *FunctionCallingHandler) handleToolCallDomain(
	ctx context.Context,
	toolCall FunctionCall,
	tools map[string]interface{},
	req *domain.ChatRequest,
	user interfaces.User,
	sources *[]map[string]interface{},
	skipFiles *bool,
	enableWebSearch *bool,
) error {
	utils.Debugf("处理工具调用: %s", toolCall.Name)

	// 检查工具是否存在
	tool, exists := tools[toolCall.Name]
	if !exists {
		return fmt.Errorf("工具 %s 不存在", toolCall.Name)
	}

	toolMap, ok := tool.(map[string]interface{})
	if !ok {
		return fmt.Errorf("工具格式无效")
	}

	// 特殊处理网络搜索检测工具
	if toolCall.Name == "detect_web_search_need" {
		needSearch, ok := toolCall.Parameters["need_search"].(string)
		if ok && needSearch == "1" {
			*enableWebSearch = true
			utils.Debug("工具调用检测到需要网络搜索，设置标志")
		}
		return nil
	}

	// 验证参数
	validatedParams := h.validateToolParams(toolCall.Parameters, toolMap)

	// 执行工具
	var toolResult interface{}
	var err error

	if isDirect, ok := toolMap["direct"].(bool); ok && isDirect {
		// 直接工具（对应Python的direct tool）
		toolResult, err = h.executeDirectTool(ctx, toolCall.Name, validatedParams, toolMap, user)
	} else {
		// 普通工具（对应Python的normal tool）
		toolResult, err = h.executeNormalTool(ctx, toolCall.Name, validatedParams, toolMap, user)
	}

	if err != nil {
		toolResult = err.Error()
	}

	// 处理工具结果
	h.processToolResultDomain(toolCall.Name, toolResult, validatedParams, toolMap, req, sources, skipFiles)

	return nil
}

// validateToolParams 验证工具参数
func (h *FunctionCallingHandler) validateToolParams(params map[string]interface{}, tool map[string]interface{}) map[string]interface{} {
	// 获取允许的参数
	spec, ok := tool["spec"].(map[string]interface{})
	if !ok {
		return params
	}

	parameters, ok := spec["parameters"].(map[string]interface{})
	if !ok {
		return params
	}

	properties, ok := parameters["properties"].(map[string]interface{})
	if !ok {
		return params
	}

	// 过滤参数，只保留允许的参数
	validatedParams := make(map[string]interface{})
	for k, v := range params {
		if _, allowed := properties[k]; allowed {
			validatedParams[k] = v
		}
	}

	return validatedParams
}

// executeDirectTool 执行直接工具
func (h *FunctionCallingHandler) executeDirectTool(ctx context.Context, name string, params map[string]interface{}, tool map[string]interface{}, user interfaces.User) (interface{}, error) {
	// 这里应该实现HTTP调用到工具服务器的逻辑
	// 对应Python中的execute_tool_server函数
	utils.Debugf("执行直接工具: %s", name)

	// 简化实现，返回模拟结果
	return fmt.Sprintf("Direct tool %s executed with params: %v", name, params), nil
}

// executeNormalTool 执行普通工具
func (h *FunctionCallingHandler) executeNormalTool(ctx context.Context, name string, params map[string]interface{}, tool map[string]interface{}, user interfaces.User) (interface{}, error) {
	// 这里应该调用工具的callable函数
	utils.Debugf("执行普通工具: %s", name)

	// 简化实现，返回模拟结果
	return fmt.Sprintf("Normal tool %s executed with params: %v", name, params), nil
}

// processToolResult 处理工具结果
func (h *FunctionCallingHandler) processToolResultDomain(
	toolName string,
	toolResult interface{},
	params map[string]interface{},
	tool map[string]interface{},
	req *domain.ChatRequest,
	sources *[]map[string]interface{},
	skipFiles *bool,
) {
	// 转换结果为字符串
	var resultStr string
	switch v := toolResult.(type) {
	case string:
		resultStr = v
	case map[string]interface{}, []interface{}:
		if jsonBytes, err := json.Marshal(v); err == nil {
			resultStr = string(jsonBytes)
		} else {
			resultStr = fmt.Sprintf("%v", v)
		}
	default:
		resultStr = fmt.Sprintf("%v", v)
	}

	// 获取工具ID
	toolID := ""
	if id, ok := tool["tool_id"].(string); ok {
		toolID = id
	}

	toolDisplayName := toolName
	if toolID != "" {
		toolDisplayName = fmt.Sprintf("%s/%s", toolID, toolName)
	}

	// 检查是否需要引用（citation）
	enableCitation := false
	if metadata, ok := tool["metadata"].(map[string]interface{}); ok {
		if citation, ok := metadata["citation"].(bool); ok && citation {
			enableCitation = true
		}
	}
	if direct, ok := tool["direct"].(bool); ok && direct {
		enableCitation = true
	}

	if enableCitation {
		// 创建引用源（对应Python的source处理）
		sourceInfo := map[string]interface{}{
			"name": fmt.Sprintf("TOOL:%s", toolDisplayName),
		}

		// 如果是直接工具，添加服务器信息
		if direct, ok := tool["direct"].(bool); ok && direct {
			if server, ok := tool["server"].(map[string]interface{}); ok {
				if info, ok := server["info"]; ok {
					sourceInfo["info"] = info
				}
			}
		}

		sourceData := map[string]interface{}{
			"source":   sourceInfo,
			"document": []string{resultStr},
			"metadata": []map[string]interface{}{
				{
					"source":     fmt.Sprintf("TOOL:%s", toolDisplayName),
					"parameters": params,
				},
			},
		}

		*sources = append(*sources, sourceData)
	} else {
		// 不使用引用，直接添加到消息中
		toolOutput := fmt.Sprintf("\nTool `%s` Output: %s", toolDisplayName, resultStr)
		h.addOrUpdateUserMessageDomain(toolOutput, req)
	}

	// 检查是否是文件处理工具
	if metadata, ok := tool["metadata"].(map[string]interface{}); ok {
		if fileHandler, ok := metadata["file_handler"].(bool); ok && fileHandler {
			*skipFiles = true
		}
	}
}

// addOrUpdateUserMessage 添加或更新用户消息（对应Python的add_or_update_user_message）
func (h *FunctionCallingHandler) addOrUpdateUserMessageDomain(content string, req *domain.ChatRequest) {
	// 查找最后一个用户消息
	for i := len(req.Messages) - 1; i >= 0; i-- {
		if req.Messages[i].Role == "user" {
			// 追加到最后一个用户消息
			req.Messages[i].Content += content
			return
		}
	}

	// 如果没有找到用户消息，创建一个新的
	newMessage := domain.ChatMessage{
		Role:    "user",
		Content: content,
	}
	req.Messages = append(req.Messages, newMessage)
}

// convertInterfaceToDomain 将interface转换为domain model
func (h *FunctionCallingHandler) convertInterfaceToDomain(req interfaces.ChatRequest) *domain.ChatRequest {
	// 如果已经是domain.ChatRequest，直接返回
	if domainReq, ok := req.(*domain.ChatRequest); ok {
		return domainReq
	}
	
	// 否则从interface创建domain model
	messages := req.GetMessages()
	domainMessages := make([]domain.ChatMessage, len(messages))
	for i, msg := range messages {
		domainMessages[i] = domain.ChatMessage{
			Role:    msg.GetRole(),
			Content: msg.GetContent(),
		}
	}

	// 创建基本的domain request
	domainReq := &domain.ChatRequest{
		Model:       req.GetModel(),
		Messages:    domainMessages,
		Stream:      req.GetStream(),
		Temperature: req.GetTemperature(),
		MaxTokens:   req.GetMaxTokens(),
	}

	return domainReq
}


// 注意：FunctionCall 和 FunctionCallingInvokerFunc 类型定义已移动到 interfaces.go
