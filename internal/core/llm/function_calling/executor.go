package function_calling

import (
	"fmt"
	"hkchat_api/pkg/utils"
)

// functionExecutor 函数执行器实现
type functionExecutor struct{}

// NewFunctionExecutor 创建函数执行器实例
func NewFunctionExecutor() *functionExecutor {
	return &functionExecutor{}
}

// ExecuteDirectFunction 执行直接函数（外部服务器）
func (e *functionExecutor) ExecuteDirectFunction(functionName string, params map[string]interface{}, function map[string]interface{}, metadata map[string]interface{}) (interface{}, error) {
	utils.Debugf("执行直接函数: %s", functionName)

	// 获取服务器信息
	server, ok := function["server"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("直接函数缺少服务器配置")
	}

	serverURL, ok := server["url"].(string)
	if !ok || serverURL == "" {
		return nil, fmt.Errorf("函数服务器 URL 未配置")
	}

	// 这里应该实现 HTTP 调用到函数服务器
	// 类似 Python 版本的 execute_tool_server 函数
	result, err := e.callFunctionServer(serverURL, functionName, params, server, metadata)
	if err != nil {
		return nil, fmt.Errorf("调用函数服务器失败: %v", err)
	}

	return result, nil
}

// ExecuteRegularFunction 执行常规函数（内部函数）
func (e *functionExecutor) ExecuteRegularFunction(functionName string, params map[string]interface{}, function map[string]interface{}, metadata map[string]interface{}) (interface{}, error) {
	utils.Debugf("执行常规函数: %s", functionName)

	// 获取可调用函数
	callable, ok := function["callable"]
	if !ok {
		return nil, fmt.Errorf("函数 %s 缺少可调用函数", functionName)
	}

	// 这里应该调用实际的函数
	// 暂时返回模拟结果
	result := e.executeInternalFunction(callable, params)

	return result, nil
}

// callFunctionServer 调用函数服务器
func (e *functionExecutor) callFunctionServer(serverURL, functionName string, params map[string]interface{}, server map[string]interface{}, metadata map[string]interface{}) (interface{}, error) {
	utils.Debugf("调用函数服务器: %s", serverURL)

	// 获取认证信息（暂时未使用，但为了兼容性保留）
	_ = server // 避免未使用变量警告

	// 这里应该实现实际的 HTTP 请求
	// 类似 Python 版本的逻辑：
	// 1. 根据 OpenAPI 规范构建正确的 URL
	// 2. 发送 HTTP 请求
	// 3. 处理响应

	// 简化的模拟结果，移除不必要的用户信息
	return fmt.Sprintf("函数服务器 %s 执行 %s 的结果: %v", serverURL, functionName, params), nil
}

// executeInternalFunction 执行内部函数
func (e *functionExecutor) executeInternalFunction(callable interface{}, params map[string]interface{}) interface{} {
	// 这里应该调用实际的内部函数
	// 根据 callable 的类型进行不同的处理

	// 暂时返回模拟结果
	return fmt.Sprintf("内部函数执行结果: %v", params)
}

// GetFunctionExecutionContext 获取函数执行上下文
func (e *functionExecutor) GetFunctionExecutionContext(metadata map[string]interface{}) map[string]interface{} {
	context := make(map[string]interface{})

	// 直接复制 metadata，无需特殊处理 user_id
	for key, value := range metadata {
		if key != "functions" {
			context[key] = value
		}
	}

	return context
}
