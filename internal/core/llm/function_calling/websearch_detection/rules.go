package websearch_detection

import (
	"fmt"
	"strings"
)

// detectByRules 基于规则的检测
func detectByRules(query string) *DetectionResult {
	result := &DetectionResult{
		NeedSearch: false,
		Confidence: "medium",
		Reason:     "",
		Keywords:   []string{},
	}

	query = strings.ToLower(query)

	// 明确需要搜索的关键词
	needSearchKeywords := []string{
		"今天", "现在", "最新", "最近", "当前", "实时",
		"天气", "股价", "汇率", "新闻", "热点",
		"营业时间", "附近", "导航", "路况",
		"今年", "本月", "这周", "昨天", "明天",
		"价格", "多少钱", "购买", "在哪买",
		"开放时间", "几点开门", "几点关门",
	}

	// 明确不需要搜索的关键词
	noSearchKeywords := []string{
		"什么是", "如何", "怎么", "为什么", "定义",
		"历史", "原理", "概念", "解释", "教程",
		"学习", "方法", "步骤", "技巧", "总结",
		"分析", "比较", "优缺点", "建议",
	}

	// 检查需要搜索的关键词
	matchedKeywords := []string{}
	for _, keyword := range needSearchKeywords {
		if strings.Contains(query, keyword) {
			result.NeedSearch = true
			result.Confidence = "high"
			result.Reason = fmt.Sprintf("包含时效性关键词: %s", keyword)
			matchedKeywords = append(matchedKeywords, keyword)
		}
	}
	result.Keywords = matchedKeywords

	// 检查不需要搜索的关键词
	for _, keyword := range noSearchKeywords {
		if strings.Contains(query, keyword) {
			if result.NeedSearch {
				// 如果既包含需要搜索的词，又包含不需要搜索的词，降低置信度
				result.Confidence = "medium"
				result.Reason = fmt.Sprintf("包含时效性关键词但也包含常识性关键词: %s", keyword)
			} else {
				result.NeedSearch = false
				result.Confidence = "high"
				result.Reason = fmt.Sprintf("包含常识性关键词: %s", keyword)
			}
		}
	}

	// 特殊情况处理
	if len(query) < 5 {
		// 非常短的查询，可能需要更多信息
		if !result.NeedSearch {
			result.NeedSearch = true
			result.Confidence = "low"
			result.Reason = "查询过短，可能需要更多信息"
		}
	}

	// 默认原因
	if result.Reason == "" {
		if result.NeedSearch {
			result.Reason = "查询可能需要实时信息"
		} else {
			result.Reason = "查询属于常识性问题"
		}
	}

	return result
}

// 预定义的检测规则
var predefinedRules = []DetectionRule{
	{
		Keywords:    []string{"今天", "现在", "当前"},
		Confidence:  "high",
		NeedSearch:  true,
		Description: "时间敏感查询",
	},
	{
		Keywords:    []string{"最新", "最近", "实时"},
		Confidence:  "high",
		NeedSearch:  true,
		Description: "实时信息查询",
	},
	{
		Keywords:    []string{"天气", "温度", "降雨"},
		Confidence:  "high",
		NeedSearch:  true,
		Description: "天气相关查询",
	},
	{
		Keywords:    []string{"什么是", "定义", "概念"},
		Confidence:  "high",
		NeedSearch:  false,
		Description: "概念性查询",
	},
	{
		Keywords:    []string{"如何", "怎么", "方法"},
		Confidence:  "medium",
		NeedSearch:  false,
		Description: "方法性查询",
	},
}

// GetPredefinedRules 获取预定义规则
func GetPredefinedRules() []DetectionRule {
	return predefinedRules
}
