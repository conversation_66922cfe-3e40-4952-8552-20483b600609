package websearch_detection

import (
	"hkchat_api/internal/core/llm"
	"hkchat_api/pkg/utils"
)

// Service 网络搜索检测服务 - 对外提供的简单接口
type Service struct {
	detector *Detector
}

// NewService 创建网络搜索检测服务
func NewService(llmGateway *llm.Gateway) *Service {
	return &Service{
		detector: NewDetector(llmGateway),
	}
}

// DetectWebSearchNeed 检测是否需要网络搜索 - 核心对外方法
func (s *Service) DetectWebSearchNeed(query string, model string) (bool, string, error) {
	utils.Debug("🔍 WebSearchDetectionService: 检测网络搜索需求")

	if s.detector == nil {
		utils.Warn("检测器未初始化，使用规则检测")
		result := detectByRules(query)
		return result.NeedSearch, result.Reason, nil
	}

	return s.detector.DetectWebSearchNeed(query, model)
}

// QuickCheck 快速检测（仅使用规则，不调用AI）
func (s *Service) QuickCheck(query string) (bool, string) {
	result := detectByRules(query)
	return result.NeedSearch, result.Reason
}

// GetDetectionRules 获取当前的检测规则
func (s *Service) GetDetectionRules() []DetectionRule {
	return GetPredefinedRules()
}
