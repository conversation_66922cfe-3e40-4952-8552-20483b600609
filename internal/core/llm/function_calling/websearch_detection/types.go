package websearch_detection

// DetectionResult 网络搜索检测结果
type DetectionResult struct {
	NeedSearch bool     `json:"need_search"`
	Confidence string   `json:"confidence"` // high, medium, low
	Reason     string   `json:"reason"`
	Keywords   []string `json:"keywords,omitempty"`
}

// DetectionRule 检测规则
type DetectionRule struct {
	Keywords    []string `json:"keywords"`
	Confidence  string   `json:"confidence"`
	NeedSearch  bool     `json:"need_search"`
	Description string   `json:"description"`
}
