package websearch_detection

import (
	"context"
	"fmt"
	"strings"

	"hkchat_api/internal/core/factories"
	"hkchat_api/internal/core/interfaces"
	"hkchat_api/internal/core/llm"
	"hkchat_api/pkg/utils"
)

// Detector 网络搜索检测器
type Detector struct {
	llmGateway     *llm.Gateway
	userFactory    interfaces.UserFactory
	messageFactory interfaces.MessageFactory
}

// NewDetector 创建网络搜索检测器
func NewDetector(llmGateway *llm.Gateway) *Detector {
	return &Detector{
		llmGateway:     llmGateway,
		userFactory:    factories.NewEntityUserFactory(),
		messageFactory: factories.NewCoreMessageFactory(),
	}
}

// DetectWebSearchNeed 检测是否需要网络搜索 - 核心方法
func (d *Detector) DetectWebSearchNeed(query string, model string) (bool, string, error) {
	utils.Debug("🔍 WebSearchDetector: 检测网络搜索需求")

	result, err := d.detect(query, model)
	if err != nil {
		return false, "", fmt.Errorf("网络搜索检测失败: %v", err)
	}

	utils.Debugf("🎯 检测结果: 需要搜索=%t, 原因=%s", result.NeedSearch, result.Reason)
	return result.NeedSearch, result.Reason, nil
}

// detect 核心检测逻辑
func (d *Detector) detect(query string, model string) (*DetectionResult, error) {
	utils.Debug("🔍 开始网络搜索检测")

	// 第一步：基于规则的快速检测
	ruleResult := detectByRules(query)
	if ruleResult.Confidence == "high" {
		utils.Debugf("🎯 规则检测确定结果: %t", ruleResult.NeedSearch)
		return ruleResult, nil
	}

	// 第二步：使用 AI 模型进行智能检测（如果LLM网关可用）
	if d.llmGateway != nil {
		aiResult, err := d.detectByAI(query, model)
		if err != nil {
			utils.Warnf("AI检测失败，使用规则检测结果: %v", err)
			return ruleResult, nil
		}

		// 合并结果
		return mergeResults(ruleResult, aiResult), nil
	}

	// 如果没有LLM服务，直接返回规则检测结果
	return ruleResult, nil
}

// detectByAI 使用 AI 模型进行 Function Calling 检测
func (d *Detector) detectByAI(query string, model string) (*DetectionResult, error) {
	utils.Debug("🔍 使用 Function Calling 进行 AI 检测")

	// 创建工具定义
	tools := d.createWebSearchDetectionTool()

	// 构建Core消息
	userMessage := d.messageFactory.CreateMessage("user", query)

	messages := []interfaces.Message{userMessage}

	// 创建Core聊天请求
	coreRequest := d.messageFactory.CreateChatRequest(model, messages)
	coreRequest.SetStream(false)

	// 使用用户工厂创建系统用户
	user := d.userFactory.CreateSystemUser("system", "websearch_detector")

	// 直接使用 interfaces.ChatRequest
	// 使用 Function Calling
	response, metadata, err := d.llmGateway.ProcessChatCompletionWithFunctionCalling(
		context.Background(),
		coreRequest,
		user,
		tools,
		map[string]interface{}{},
	)

	if err != nil {
		return nil, fmt.Errorf("Function Calling 调用失败: %v", err)
	}

	// 解析 Function Calling 结果
	return d.parseFunctionCallingResult(response, metadata)
}

// createWebSearchDetectionTool 创建网络搜索检测工具
func (d *Detector) createWebSearchDetectionTool() map[string]interface{} {
	return map[string]interface{}{
		"web_search_detection": map[string]interface{}{
			"spec": map[string]interface{}{
				"name":        "web_search_detection",
				"description": "检测用户查询是否需要网络搜索来获取最新信息",
				"parameters": map[string]interface{}{
					"type": "object",
					"properties": map[string]interface{}{
						"need_search": map[string]interface{}{
							"type":        "string",
							"description": "是否需要网络搜索，1表示需要，0表示不需要",
							"enum":        []string{"0", "1"},
						},
						"confidence": map[string]interface{}{
							"type":        "string",
							"description": "判断的置信度",
							"enum":        []string{"high", "medium", "low"},
						},
						"reason": map[string]interface{}{
							"type":        "string",
							"description": "判断的理由说明",
						},
						"keywords": map[string]interface{}{
							"type":        "array",
							"description": "相关的关键词",
							"items": map[string]interface{}{
								"type": "string",
							},
						},
					},
					"required": []string{"need_search", "confidence", "reason"},
				},
			},
			"metadata": map[string]interface{}{
				"citation": false,
			},
		},
	}
}

// parseFunctionCallingResult 解析 Function Calling 结果
func (d *Detector) parseFunctionCallingResult(response interface{}, metadata map[string]interface{}) (*DetectionResult, error) {
	utils.Debug("🔍 解析 Function Calling 结果")

	// 检查是否有 sources（工具调用结果）
	if sources, ok := metadata["sources"].([]map[string]interface{}); ok && len(sources) > 0 {
		// 查找网络搜索检测工具的结果
		for _, source := range sources {
			if sourceInfo, ok := source["source"].(map[string]interface{}); ok {
				if name, ok := sourceInfo["name"].(string); ok && strings.Contains(name, "web_search_detection") {
					// 解析工具结果
					if metadataList, ok := source["metadata"].([]map[string]interface{}); ok && len(metadataList) > 0 {
						if params, ok := metadataList[0]["parameters"].(map[string]interface{}); ok {
							return d.parseToolParameters(params)
						}
					}
				}
			}
		}
	}

	// 如果没有工具调用结果，尝试从响应中解析
	return d.parseFromResponse(response)
}

// parseToolParameters 解析工具参数
func (d *Detector) parseToolParameters(params map[string]interface{}) (*DetectionResult, error) {
	result := &DetectionResult{
		NeedSearch: false,
		Confidence: "medium",
		Reason:     "AI Function Calling 分析结果",
		Keywords:   []string{},
	}

	// 解析 need_search
	if needSearch, ok := params["need_search"].(string); ok {
		result.NeedSearch = needSearch == "1"
	}

	// 解析 confidence
	if confidence, ok := params["confidence"].(string); ok {
		result.Confidence = confidence
	}

	// 解析 reason
	if reason, ok := params["reason"].(string); ok {
		result.Reason = reason
	}

	// 解析 keywords
	if keywords, ok := params["keywords"].([]interface{}); ok {
		for _, keyword := range keywords {
			if keywordStr, ok := keyword.(string); ok {
				result.Keywords = append(result.Keywords, keywordStr)
			}
		}
	}

	utils.Debugf("🎯 Function Calling 解析结果: need_search=%t, confidence=%s", result.NeedSearch, result.Confidence)
	return result, nil
}

// parseFromResponse 从响应中解析（备用方法）
func (d *Detector) parseFromResponse(response interface{}) (*DetectionResult, error) {
	// 如果 Function Calling 没有结果，使用默认的中等置信度规则检测
	utils.Warn("Function Calling 没有返回预期结果，使用备用解析")

	result := &DetectionResult{
		NeedSearch: false,
		Confidence: "low",
		Reason:     "Function Calling 解析失败，使用备用判断",
		Keywords:   []string{},
	}

	// 尝试从响应文本中查找线索
	if responseStr := fmt.Sprintf("%v", response); responseStr != "" {
		content := strings.ToLower(responseStr)
		if strings.Contains(content, "need_search") && strings.Contains(content, "1") {
			result.NeedSearch = true
			result.Reason = "从响应文本中检测到搜索需求"
		}
	}

	return result, nil
}

// mergeResults 合并规则检测和AI检测结果
func mergeResults(ruleResult, aiResult *DetectionResult) *DetectionResult {
	// 如果规则检测置信度高，优先使用规则结果
	if ruleResult.Confidence == "high" {
		return ruleResult
	}

	// 如果AI检测置信度高，使用AI结果
	if aiResult.Confidence == "high" {
		return aiResult
	}

	// 综合判断
	result := &DetectionResult{
		NeedSearch: ruleResult.NeedSearch || aiResult.NeedSearch,
		Confidence: "medium",
		Reason:     fmt.Sprintf("规则检测: %s; AI检测: %s", ruleResult.Reason, aiResult.Reason),
		Keywords:   append(ruleResult.Keywords, aiResult.Keywords...),
	}

	// 如果两者一致，提高置信度
	if ruleResult.NeedSearch == aiResult.NeedSearch {
		result.Confidence = "high"
	}

	return result
}

