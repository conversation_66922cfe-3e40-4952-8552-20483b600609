# 网络搜索检测功能

这个模块提供了智能的网络搜索需求检测功能，能够判断用户查询是否需要进行网络搜索来获取最新信息。

## 📁 文件结构

```
websearch_detection/
├── detector.go    # 核心检测器实现
├── types.go       # 类型定义
├── rules.go       # 基于规则的检测逻辑
├── service.go     # 对外服务接口
└── README.md      # 说明文档
```

## 🚀 使用方法

### 基础用法

```go
import "hkchat_api/internal/core/llm/function_calling/websearch_detection"

// 创建检测服务（不需要LLM服务，仅使用规则检测）
service := websearch_detection.NewService(nil)

// 检测查询
needSearch, reason := service.QuickCheck("今天天气怎么样？")
fmt.Printf("需要搜索: %t, 原因: %s\n", needSearch, reason)
```

### 完整用法（包含AI检测）

```go
// 如果有LLM服务，可以同时使用规则和AI检测
service := websearch_detection.NewService(llmService)

needSearch, reason, err := service.DetectWebSearchNeed(
    "今天天气怎么样？", 
    "gpt-4",
)
```

## 🎯 检测规则

### 需要网络搜索的情况
- **时间敏感信息**: 今天、现在、最新、实时等
- **地理位置相关**: 天气、交通、营业时间等
- **实时数据**: 股价、汇率、新闻等
- **价格信息**: 商品价格、费用查询等

### 不需要网络搜索的情况
- **概念性查询**: 什么是、定义、概念等
- **方法性查询**: 如何、怎么、方法等
- **历史信息**: 历史、原理、总结等
- **技术知识**: 学习、教程、技巧等

## 📊 检测逻辑

1. **规则检测**: 基于关键词的快速检测
2. **AI检测**: 使用Function Calling机制的智能判断（可选）
3. **结果合并**: 综合两种检测结果，提供最终判断

### AI检测升级

AI检测现在使用**Function Calling**机制，相比之前的文本解析方式有以下优势：
- 📈 **更高准确率**: 结构化输出避免解析错误
- 🎯 **标准化结果**: 统一的JSON格式输出
- 🔧 **更好维护**: 清晰的工具定义和参数规范
- ⚡ **更可靠**: 减少因文本格式变化导致的解析失败

## ⚡ 性能特点

- **快速响应**: 规则检测毫秒级响应
- **高准确率**: 规则+AI双重保障
- **轻量级**: 可独立运行，不强制依赖LLM服务
- **易扩展**: 规则和AI检测可独立配置

## 🔧 配置选项

检测规则在 `rules.go` 中定义，可以根据需要调整：

```go
// 需要搜索的关键词
needSearchKeywords := []string{
    "今天", "现在", "最新", "最近", "当前", "实时",
    "天气", "股价", "汇率", "新闻", "热点",
    // ... 更多关键词
}

// 不需要搜索的关键词  
noSearchKeywords := []string{
    "什么是", "如何", "怎么", "为什么", "定义",
    "历史", "原理", "概念", "解释", "教程",
    // ... 更多关键词
}
```

## 🧪 测试

在 `tests/test_websearch_detection/` 目录下有完整的测试用例：

```bash
# 运行简单演示
cd tests/test_websearch_detection
go run simple_demo.go

# 运行完整测试
go run main.go

# 运行Go测试
go test -v
```

## 📈 返回结果

```go
type DetectionResult struct {
    NeedSearch bool     `json:"need_search"`  // 是否需要搜索
    Confidence string   `json:"confidence"`   // 置信度: high/medium/low
    Reason     string   `json:"reason"`       // 判断原因
    Keywords   []string `json:"keywords"`     // 匹配的关键词
}
``` 