package services

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"hkchat_api/internal/config"
	"hkchat_api/internal/core/di/base"
	"hkchat_api/internal/core/llm"
	"hkchat_api/internal/core/llm/function_calling/websearch_detection"
	"hkchat_api/internal/core/llm/retrieval"
	"hkchat_api/internal/core/llm/retrieval/web"
	"hkchat_api/internal/models/domain"
	"hkchat_api/internal/models/dto/response"
	"hkchat_api/internal/models/entities"
	"hkchat_api/internal/repository"
	llmClient "hkchat_api/pkg/clients/llm"
	"hkchat_api/pkg/clients/web_search"
	"hkchat_api/pkg/prompts"
	"hkchat_api/pkg/utils"

	"gorm.io/gorm"
)

// ChatService 聊天服务类型别名（向后兼容）
type ChatService = ChatServiceImpl

// ChatServiceImpl 聊天业务服务实现
type ChatServiceImpl struct {
	*base.BaseCoreService

	// 核心服务依赖
	llmGateway        *llm.Gateway
	websearchDetector *websearch_detection.Detector
	websearchClient   web_search.Client
	chatRepository    *repository.ChatRepository
	modelRepository   *repository.ModelRepository
	configManager     *config.Manager
}

// NewChatService 创建聊天业务服务
func NewChatService(
	llmGateway *llm.Gateway,
	chatRepository *repository.ChatRepository,
	modelRepository *repository.ModelRepository,
	configManager *config.Manager,
) *ChatServiceImpl {
	// 验证关键依赖
	if llmGateway == nil {
		utils.Warn("🚨 LLM网关为空，聊天服务可能无法正常工作")
	}
	if chatRepository == nil {
		utils.Warn("🚨 ChatRepository为空，聊天历史功能将被禁用")
	}
	if modelRepository == nil {
		utils.Warn("🚨 ModelRepository为空，模型系统提示功能将被禁用")
	}
	if configManager == nil {
		utils.Warn("🚨 ConfigManager为空，配置功能将被禁用")
	}

	return &ChatServiceImpl{
		BaseCoreService: base.NewBaseCoreService(
			"ChatService",
			"1.0.0",
			base.ServiceTypeChat,
		),
		llmGateway: llmGateway,
		websearchDetector: func() *websearch_detection.Detector {
			if llmGateway != nil {
				return websearch_detection.NewDetector(llmGateway)
			}
			return nil
		}(),
		websearchClient: web_search.NewClient(),
		chatRepository:  chatRepository,
		modelRepository: modelRepository,
		configManager:   configManager,
	}
}

// Initialize 初始化服务
func (s *ChatServiceImpl) Initialize() error {
	return s.ExecuteBusinessOperation("Initialize", func() error {
		if err := s.BaseCoreService.Initialize(); err != nil {
			return err
		}

		s.SetHealthy(true)
		utils.Debug("💬 聊天业务服务初始化完成")
		return nil
	})
}

// ProcessChatCompletion 处理聊天完成请求（API层调用的主要方法）
func (s *ChatServiceImpl) ProcessChatCompletion(ctx context.Context, req *domain.ChatRequest, user *entities.User) (*response.ChatCompletionResponse, error) {
	var result *response.ChatCompletionResponse
	var err error

	s.ExecuteBusinessOperation("ProcessChatCompletion", func() error {
		utils.Infof("聊天服务处理请求，模型: %s, 用户: %s", req.Model, user.Id)

		// 空指针检查
		if s.llmGateway == nil {
			err = fmt.Errorf("LLM网关未初始化")
			return err
		}

		// 验证请求
		if validationErr := s.validateChatRequest(req); validationErr != nil {
			err = fmt.Errorf("请求验证失败: %v", validationErr)
			return err
		}
		
		// 调用LLM网关处理
		llmResponse, err := s.llmGateway.ProcessChatCompletion(req, user.Id)
		if err != nil {
			return fmt.Errorf("LLM网关处理失败: %v", err)
		}

		// 转换为API响应格式
		result = s.convertToAPIResponse(llmResponse, req)

		utils.Infof("聊天服务处理完成，响应ID: %s", result.ID)
		return nil
	})

	return result, err
}

// ProcessChatPayload 处理聊天载荷（公共方法）
func (s *ChatServiceImpl) ProcessChatPayload(ctx context.Context, req *domain.ChatRequest, user *entities.User, metadata map[string]interface{}) (*domain.ChatRequest, map[string]interface{}, []interface{}, error) {
	var events []interface{}

	// 1. 先通过会话id拿到历史消息
	if err := s.loadChatHistory(req, user); err != nil {
		utils.Warnf("加载聊天历史失败: %v", err)
		// 历史消息加载失败不中断流程，继续处理
	}

	// 2. 获取模型的system拼接
	if err := s.appendModelSystemPrompt(req); err != nil {
		utils.Warnf("添加模型系统提示失败: %v", err)
		// 系统提示添加失败不中断流程，继续处理
	}

	// 检查Features配置是否启用了网络搜索
	// webSearchEnabled := s.isWebSearchEnabled(req.Features)
	// if !webSearchEnabled {
	// 	utils.Debug("🔍 网络搜索功能未启用，跳过搜索检测")
	// 	return req, metadata, events, nil
	// }

	// 获取最后一条用户消息作为查询内容
	lastUserMessage := s.getLastUserMessage(req.Messages)
	if lastUserMessage == "" {
		utils.Debug("🔍 未找到用户消息，跳过网络搜索")
		return req, metadata, events, nil
	}

	// 注释掉网络搜索检测过程，直接执行网络搜索
	utils.Debug("🔍 跳过网络搜索检测，直接执行网络搜索")

	needSearch, reason, err := s.websearchDetector.DetectWebSearchNeed(lastUserMessage, req.Model)
	if err != nil {
		utils.Warnf("网络搜索检测失败: %v", err)
		return req, metadata, events, nil
	}

	utils.Debugf("🎯 网络搜索检测结果: 需要搜索=%t, 原因=%s", needSearch, reason)

	//直接执行网络搜索（跳过检测步骤）
	if needSearch {
		utils.Info("🔍 开始执行网络搜索")
		searchResults, searchErr := s.performWebSearchWithContext(ctx, req, user.Id)
		if searchErr != nil {
			utils.Warnf("网络搜索执行失败，跳过搜索: %v", searchErr)
			// 网络搜索失败时不中断流程，继续处理原始请求
			return req, metadata, events, nil
		} else {
			utils.Infof("✅ 网络搜索完成，获得 %d 条结果", len(searchResults))

			// 将搜索结果整合到消息中
			enrichedDomainReq := s.enrichRequestWithWebSearchResults(req, searchResults, lastUserMessage)
			return enrichedDomainReq, metadata, events, nil
		}
	}

	return req, metadata, events, nil
}

// convertToAPIResponse 转换LLM响应为API响应格式
func (s *ChatServiceImpl) convertToAPIResponse(llmResponse *llmClient.ChatResponse, req *domain.ChatRequest) *response.ChatCompletionResponse {
	apiResponse := &response.ChatCompletionResponse{
		ID:      llmResponse.ID,
		Object:  "chat.completion",
		Created: llmResponse.Created,
		Model:   llmResponse.Model,
		Choices: make([]response.ChatCompletionChoice, len(llmResponse.Choices)),
		Usage: response.ChatCompletionUsage{
			PromptTokens:     llmResponse.Usage.PromptTokens,
			CompletionTokens: llmResponse.Usage.CompletionTokens,
			TotalTokens:      llmResponse.Usage.TotalTokens,
		},
	}

	// 转换选择项
	for i, choice := range llmResponse.Choices {
		apiResponse.Choices[i] = response.ChatCompletionChoice{
			Index: choice.Index,
			Message: response.ChatMessage{
				Role:    choice.Message.Role,
				Content: choice.Message.Content,
			},
			FinishReason: choice.FinishReason,
		}
	}

	return apiResponse
}

// performWebSearchWithContext 使用 search_manager 执行网络搜索
func (s *ChatServiceImpl) performWebSearchWithContext(ctx context.Context, req *domain.ChatRequest, userID string) ([]web.SearchResult, error) {
	// 将请求消息转换为 retrieval.Message 格式
	retrievalMessages := make([]retrieval.Message, len(req.Messages))
	for i, msg := range req.Messages {
		retrievalMessages[i] = retrieval.Message{
			Role:    msg.Role,
			Content: msg.Content,
		}
	}

	// 构建搜索请求
	searchRequest := &retrieval.WebSearchRequest{
		Messages: retrievalMessages,
		Engine:   "tavily", // 使用 Tavily 搜索引擎
		UserID:   userID,
		ChatID:   req.ChatID,
		Metadata: map[string]interface{}{
			"model":      req.Model,
			"session_id": req.SessionID,
		},
	}

	// 调用 search_manager 的 SearchWebWithContext 方法
	searchResponse, err := retrieval.SearchWebWithContext(searchRequest)
	if err != nil {
		return nil, fmt.Errorf("搜索失败: %v", err)
	}

	if !searchResponse.Status {
		return nil, fmt.Errorf("搜索失败: %s", searchResponse.Error)
	}

	// 转换搜索结果格式
	results := make([]web.SearchResult, len(searchResponse.Results))
	copy(results, searchResponse.Results)

	return results, nil
}

// enrichRequestWithWebSearchResults 使用网络搜索结果丰富请求
func (s *ChatServiceImpl) enrichRequestWithWebSearchResults(req *domain.ChatRequest, searchResults []web.SearchResult, originalQuery string) *domain.ChatRequest {
	// 格式化搜索结果为上下文信息
	searchContext := s.formatWebSearchResultsAsContext(searchResults)

	// 使用新的网络搜索提示词模板
	systemPrompt := prompts.GetWebSearchRAGPrompt(searchContext, originalQuery)

	// 创建新的请求副本
	var features *domain.ChatFeatures
	if req.Features != nil {
		features = &domain.ChatFeatures{
			CodeInterpreter: req.Features.CodeInterpreter,
			ImageGeneration: req.Features.ImageGeneration,
			Memory:          req.Features.Memory,
			WebSearch:       req.Features.WebSearch,
		}
	}

	enrichedReq := &domain.ChatRequest{
		Model:       req.Model,
		Messages:    make([]domain.ChatMessage, len(req.Messages)),
		MaxTokens:   req.MaxTokens,
		Temperature: req.Temperature,
		Stream:      req.Stream,
		SessionID:   req.SessionID,
		ChatID:      req.ChatID,
		Features:    features,
	}

	// 复制原始消息
	for i, msg := range req.Messages {
		enrichedReq.Messages[i] = domain.ChatMessage{
			Role:    msg.Role,
			Content: msg.Content,
		}
	}

	// 找到第一个 system 消息并追加搜索结果
	systemFound := false
	for i := range enrichedReq.Messages {
		if enrichedReq.Messages[i].Role == "system" && !systemFound {
			enrichedReq.Messages[i].Content += "\n\n" + systemPrompt
			systemFound = true
			break
		}
	}

	// 如果没有找到 system 消息，创建一个新的
	if !systemFound {
		newMessages := make([]domain.ChatMessage, len(enrichedReq.Messages)+1)
		newMessages[0] = domain.ChatMessage{
			Role:    "system",
			Content: systemPrompt,
		}
		copy(newMessages[1:], enrichedReq.Messages)
		enrichedReq.Messages = newMessages
	}

	return enrichedReq
}

// formatWebSearchResultsAsContext 格式化网络搜索结果为上下文信息
func (s *ChatServiceImpl) formatWebSearchResultsAsContext(searchResults []web.SearchResult) string {
	if len(searchResults) == 0 {
		return "未找到相关信息。"
	}

	var builder strings.Builder

	for i, result := range searchResults {
		builder.WriteString(fmt.Sprintf("<source id=\"%d\">\n", i+1))
		builder.WriteString(fmt.Sprintf("Title: %s\n", result.Title))
		builder.WriteString(fmt.Sprintf("URL: %s\n", result.Link))
		builder.WriteString(fmt.Sprintf("Content: %s\n", result.Content))
		builder.WriteString("</source>\n\n")
	}

	return builder.String()
}

// formatWebSearchResults 格式化网络搜索结果为文本（保留原方法以备向后兼容）
func (s *ChatServiceImpl) formatWebSearchResults(searchResults []web.SearchResult, originalQuery string) string {
	if len(searchResults) == 0 {
		return "搜索结果：未找到相关信息。"
	}

	var builder strings.Builder
	builder.WriteString(fmt.Sprintf("根据用户查询「%s」，我搜索到以下最新信息：\n\n", originalQuery))

	for i, result := range searchResults {
		builder.WriteString(fmt.Sprintf("搜索结果 %d：\n", i+1))
		builder.WriteString(fmt.Sprintf("标题：%s\n", result.Title))
		builder.WriteString(fmt.Sprintf("来源：%s\n", result.Link))
		builder.WriteString(fmt.Sprintf("内容：%s\n", result.Content))
		builder.WriteString("\n")
	}

	builder.WriteString("请基于以上搜索结果和你的知识来回答用户的问题。如果搜索结果中包含了答案，请优先参考最新的信息。")

	return builder.String()
}

// isWebSearchEnabled 检查是否启用了网络搜索功能
func (s *ChatServiceImpl) isWebSearchEnabled(features *domain.ChatFeatures) bool {
	if features == nil {
		return false
	}
	return features.WebSearch
}

// isTavilyConfigured 检查 Tavily 是否已配置
func (s *ChatServiceImpl) isTavilyConfigured() bool {
	// 空指针检查
	if s.configManager == nil {
		utils.Warn("🚨 ConfigManager 未初始化，无法检查 Tavily 配置")
		return false
	}

	// 从配置中获取 Tavily 配置
	tavilyConfig := config.GetTavilyConfig()
	if tavilyConfig == nil {
		return false
	}

	// 检查 API Key 是否存在且不为空
	if tavilyConfig.APIKey == "" {
		return false
	}

	return true
}

// getLastUserMessage 从domain消息中获取最后一条用户消息内容
func (s *ChatServiceImpl) getLastUserMessage(messages []domain.ChatMessage) string {
	for i := len(messages) - 1; i >= 0; i-- {
		if messages[i].Role == "user" {
			return messages[i].Content
		}
	}
	return ""
}

// validateChatRequest 验证聊天请求
func (s *ChatServiceImpl) validateChatRequest(req *domain.ChatRequest) error {
	if req == nil {
		return fmt.Errorf("请求不能为空")
	}

	if req.Model == "" {
		return fmt.Errorf("模型名称不能为空")
	}

	if len(req.Messages) == 0 {
		return fmt.Errorf("消息列表不能为空")
	}

	// 检查消息内容
	for i, msg := range req.Messages {
		if msg.Role == "" {
			return fmt.Errorf("消息[%d]角色不能为空", i)
		}

		if msg.Content == "" {
			return fmt.Errorf("消息[%d]内容不能为空", i)
		}

		// 检查角色有效性
		if !isValidRole(msg.Role) {
			return fmt.Errorf("消息[%d]角色无效: %s", i, msg.Role)
		}
	}

	// 检查参数范围
	if req.Temperature != nil && (*req.Temperature < 0 || *req.Temperature > 2) {
		return fmt.Errorf("Temperature 参数范围应在 0-2 之间")
	}

	if req.MaxTokens != nil && (*req.MaxTokens < 0 || *req.MaxTokens > 32000) {
		return fmt.Errorf("MaxTokens 参数范围应在 0-32000 之间")
	}

	return nil
}

// isValidRole 检查角色是否有效
func isValidRole(role string) bool {
	validRoles := []string{"user", "assistant", "system"}
	for _, validRole := range validRoles {
		if role == validRole {
			return true
		}
	}
	return false
}

// loadChatHistory 加载聊天历史记录
func (s *ChatServiceImpl) loadChatHistory(req *domain.ChatRequest, user *entities.User) error {
	// 空指针检查
	if s.chatRepository == nil {
		utils.Warn("🚨 ChatRepository 未初始化，跳过历史记录加载")
		return nil
	}

	// 如果没有 ChatID，跳过历史记录加载
	if req.ChatID == "" {
		utils.Debug("🔍 没有会话ID，跳过历史记录加载")
		return nil
	}

	// 从数据库获取聊天历史
	chatHistory, err := s.chatRepository.GetChatHistoryByUserIDAndChatID(user.Id, req.ChatID)
	if err != nil {
		return fmt.Errorf("获取聊天历史失败: %v", err)
	}

	// 解析聊天历史JSON并添加到消息中
	if chatHistory.Chat != nil {
		// 这里需要根据你的JSON结构来解析历史消息
		// 假设Chat字段包含历史消息数组
		var historicalMessages []domain.ChatMessage

		// 将JSON数据转换为字节数组，然后解析
		chatData, err := json.Marshal(chatHistory.Chat)
		if err != nil {
			return fmt.Errorf("序列化聊天历史失败: %v", err)
		}

		if err := json.Unmarshal(chatData, &historicalMessages); err != nil {
			return fmt.Errorf("解析历史消息失败: %v", err)
		}

		// 将历史消息添加到当前请求的消息前面
		if len(historicalMessages) > 0 {
			// 保留最后N条历史消息（避免上下文过长）
			maxHistoryMessages := 10
			if len(historicalMessages) > maxHistoryMessages {
				historicalMessages = historicalMessages[len(historicalMessages)-maxHistoryMessages:]
			}

			// 合并历史消息和当前消息
			allMessages := append(historicalMessages, req.Messages...)
			req.Messages = allMessages

			utils.ChatDebug("📚 加载了 %d 条历史消息", len(historicalMessages))
		}
	}

	return nil
}

// appendModelSystemPrompt 添加模型系统提示
func (s *ChatServiceImpl) appendModelSystemPrompt(req *domain.ChatRequest) error {
	// 空指针检查
	if s.modelRepository == nil {
		utils.Warn("🚨 ModelRepository 未初始化，跳过模型系统提示")
		return nil
	}

	// 从数据库查询模型记录
	var model entities.Model
	err := s.modelRepository.GetByID(&model, req.Model)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.Debugf("未找到模型 %s", req.Model)
			return nil
		}
		utils.Errorf("获取模型 %s 失败: %v", req.Model, err)
		return nil // 不中断流程，继续处理
	}

	// 解析params中的system字段
	var params map[string]interface{}
	if err := json.Unmarshal([]byte(model.Params), &params); err != nil {
		utils.Errorf("解析模型 %s 的参数失败: %v", req.Model, err)
		return nil
	}

	// 获取system prompt
	var systemPrompt string
	if systemPromptValue, exists := params["system"]; exists {
		if systemPromptStr, ok := systemPromptValue.(string); ok && systemPromptStr != "" {
			systemPrompt = systemPromptStr
		}
	}

	if systemPrompt == "" {
		utils.Debug("🤖 模型 %s 没有配置系统提示", req.Model)
		return nil
	}

	// 处理系统提示模板中的占位符
	originalPrompt := systemPrompt
	systemPrompt = s.processSystemPromptTemplate(systemPrompt, req)

	// 记录模板替换日志
	if originalPrompt != systemPrompt {
		utils.ChatDebug("🔧 系统提示模板替换完成，占位符已处理")
	}

	// 查找是否已有系统消息
	systemMessageIndex := -1
	for i, msg := range req.Messages {
		if msg.Role == "system" {
			systemMessageIndex = i
			break
		}
	}

	if systemMessageIndex >= 0 {
		// 如果已有系统消息，追加到现有系统消息中
		req.Messages[systemMessageIndex].Content += "\n\n" + systemPrompt
		utils.ChatDebug("🤖 追加模型系统提示到现有系统消息")
	} else {
		// 如果没有系统消息，创建新的系统消息并插入到开头
		systemMessage := domain.ChatMessage{
			Role:    "system",
			Content: systemPrompt,
		}
		req.Messages = append([]domain.ChatMessage{systemMessage}, req.Messages...)
		utils.ChatDebug("🤖 创建新的系统消息")
	}

	return nil
}

// processSystemPromptTemplate 处理系统提示模板中的占位符
func (s *ChatServiceImpl) processSystemPromptTemplate(systemPrompt string, req *domain.ChatRequest) string {
	if systemPrompt == "" {
		return systemPrompt
	}

	processedPrompt := systemPrompt

	// 1. 替换日期相关占位符
	currentDate := time.Now().Format("2006-01-02")
	currentTime := time.Now().Format("2006-01-02 15:04:05")
	currentYear := time.Now().Format("2006")
	currentMonth := time.Now().Format("01")
	currentDay := time.Now().Format("02")

	processedPrompt = strings.ReplaceAll(processedPrompt, "{{CURRENT_DATE}}", currentDate)
	processedPrompt = strings.ReplaceAll(processedPrompt, "{{CURRENT_TIME}}", currentTime)
	processedPrompt = strings.ReplaceAll(processedPrompt, "{{CURRENT_YEAR}}", currentYear)
	processedPrompt = strings.ReplaceAll(processedPrompt, "{{CURRENT_MONTH}}", currentMonth)
	processedPrompt = strings.ReplaceAll(processedPrompt, "{{CURRENT_DAY}}", currentDay)

	// 2. 替换请求相关占位符
	if req.SessionID != "" {
		processedPrompt = strings.ReplaceAll(processedPrompt, "{{SESSION_ID}}", req.SessionID)
	}
	if req.ChatID != "" {
		processedPrompt = strings.ReplaceAll(processedPrompt, "{{CHAT_ID}}", req.ChatID)
	}
	if req.Model != "" {
		processedPrompt = strings.ReplaceAll(processedPrompt, "{{MODEL}}", req.Model)
	}

	// 3. 替换系统相关占位符
	processedPrompt = strings.ReplaceAll(processedPrompt, "{{SYSTEM_NAME}}", "HKChat")
	processedPrompt = strings.ReplaceAll(processedPrompt, "{{SYSTEM_VERSION}}", "1.0.0")
	processedPrompt = strings.ReplaceAll(processedPrompt, "{{SYSTEM_PROVIDER}}", "Hong Kong Generative AI Center (HKGAI)")

	// 4. 移除空占位符和处理剩余的占位符
	processedPrompt = strings.ReplaceAll(processedPrompt, "{{}}", "")

	// 5. 处理特殊占位符（如果有的话）
	// 例如：{{MESSAGES:END:3}} 等复杂占位符可以在这里处理

	return processedPrompt
}

