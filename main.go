package main

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
)

func main() {
	if len(os.Args) < 2 {
		showHelp()
		return
	}

	command := os.Args[1]

	// 执行对应的子脚本
	scriptPath := filepath.Join("scripts", command, "main.go")

	// 检查脚本是否存在
	if _, err := os.Stat(scriptPath); os.IsNotExist(err) {
		if command == "help" {
			showHelp()
			return
		}
		fmt.Printf("❌ 未知命令: %s\n", command)
		showHelp()
		return
	}

	// 执行子脚本，传递剩余参数
	args := []string{"run", scriptPath}
	if len(os.Args) > 2 {
		args = append(args, os.Args[2:]...)
	}

	cmd := exec.Command("go", args...)
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr
	cmd.Stdin = os.Stdin

	if err := cmd.Run(); err != nil {
		fmt.Printf("❌ 执行失败: %v\n", err)
		os.Exit(1)
	}
}

func showHelp() {
	fmt.Println("🚀 HKChat API 开发工具")
	fmt.Println()
	fmt.Println("用法: go run main.go <命令> [参数...]")
	fmt.Println()
	fmt.Println("可用命令:")
	fmt.Println("  run      - 启动开发服务器")

	fmt.Println("  build    - 构建项目")
	fmt.Println("  fmt      - 格式化代码")
	fmt.Println("  deps     - 整理依赖")
	fmt.Println("  coverage - 生成测试覆盖率")
	fmt.Println("  clean    - 清理构建文件")
	fmt.Println("  help     - 显示帮助")
	fmt.Println()
	fmt.Println("示例:")
	fmt.Println("  go run main.go run")

	fmt.Println("  go run main.go build")
}
