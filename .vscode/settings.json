{
    // Go 开发环境配置
    "go.useLanguageServer": true,
    "go.lintOnSave": "package",
    "go.vetOnSave": "package",
    "go.formatTool": "goimports",
    "go.buildOnSave": "off",
    "go.lintTool": "golint",
    "go.vetFlags": [],
    "go.testFlags": ["-v"],
    "go.coverOnSave": false,
    "go.coverageDecorator": {
        "type": "gutter",
        "coveredHighlightColor": "rgba(64,128,128,0.5)",
        "uncoveredHighlightColor": "rgba(128,64,64,0.25)"
    },
    
    // 调试器配置
    "go.delveConfig": {
        "dlvLoadConfig": {
            "followPointers": true,
            "maxVariableRecurse": 1,
            "maxStringLen": 64,
            "maxArrayValues": 64,
            "maxStructFields": -1
        },
        "apiVersion": 2,
        "showGlobalVariables": true
    },
    
    // 文件监控
    "files.watcherExclude": {
        "**/.git/objects/**": true,
        "**/.git/subtree-cache/**": true,
        "**/node_modules/*/**": true,
        "**/logs/**": true,
        "**/tmp/**": true,
        "**/uploads/**": true,
        "**/*.exe": true,
        "**/*.log": true
    },
    
    // 文件关联
    "files.associations": {
        "*.go": "go",
        "*.mod": "go.mod",
        "*.sum": "go.sum",
        "*.yaml": "yaml",
        "*.yml": "yaml",
        ".env*": "dotenv"
    },
    
    // 编辑器配置
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.organizeImports": "explicit"
    },
    "editor.tabSize": 4,
    "editor.insertSpaces": false,
    
    // 终端配置
    "terminal.integrated.cwd": "${workspaceFolder}",
    "terminal.integrated.env.windows": {
        "CGO_ENABLED": "0",
        "GOOS": "windows"
    },
    
    // 搜索排除
    "search.exclude": {
        "**/logs": true,
        "**/tmp": true,
        "**/uploads": true,
        "**/*.exe": true,
        "**/*.log": true,
        "**/vendor": true
    },
    
    // 语言特定设置
    "[go]": {
        "editor.formatOnSave": true,
        "editor.codeActionsOnSave": {
            "source.organizeImports": "explicit"
        },
        "editor.suggest.snippetsPreventQuickSuggestions": false
    },
    
    "[yaml]": {
        "editor.tabSize": 2,
        "editor.insertSpaces": true
    },
    
    "[json]": {
        "editor.tabSize": 2,
        "editor.insertSpaces": true
    }
} 