# VS Code 调试配置说明

本目录包含了用于调试和开发 HKChat API 项目的 VS Code 配置文件。

## 🚀 快速开始

1. **安装推荐扩展**
   - 打开 VS Code
   - 按 `Ctrl+Shift+P` 打开命令面板
   - 输入 `Extensions: Show Recommended Extensions`
   - 安装推荐的扩展（特别是 `golang.go`）

2. **配置 Go 环境**
   - 确保已安装 Go 1.19+
   - 确保 `GOPATH` 和 `GOROOT` 环境变量正确设置
   - 安装 Go 工具：按 `Ctrl+Shift+P`，输入 `Go: Install/Update Tools`

3. **准备环境变量**
   - 确保项目根目录有 `.env` 文件
   - 检查数据库连接配置

## 🐛 调试配置

### 主要调试选项

#### 🚀 启动服务器 (直接调试)
- **推荐使用**
- 直接调试 `cmd/server/main.go`
- 绕过脚本调用，调试更直接
- 适合调试服务器启动和运行时问题

#### 🔄 通过main.go启动 (模拟命令行)
- 模拟 `go run main.go run` 命令
- 包含完整的脚本调用链
- 适合调试脚本执行流程

#### 🧪 调试测试
- 运行和调试测试代码
- 支持单个测试文件或整个项目测试

#### 🔍 调试当前测试文件
- 只调试当前打开的测试文件
- 按 `F5` 快速调试当前测试

#### 🐛 附加到正在运行的进程
- 附加到已运行的进程进行调试
- 用于调试已启动的服务

### 如何开始调试

1. **设置断点**
   - 在代码行号左侧点击设置断点
   - 红点表示断点已设置

2. **启动调试**
   - 按 `F5` 启动调试
   - 或者按 `Ctrl+Shift+D` 打开调试面板，选择配置后点击绿色三角形

3. **调试控制**
   - `F5`: 继续执行
   - `F10`: 单步跳过
   - `F11`: 单步进入
   - `Shift+F11`: 单步跳出
   - `Ctrl+Shift+F5`: 重启调试

## 🔧 任务配置

按 `Ctrl+Shift+P` 输入 `Tasks: Run Task` 可以运行预定义任务：

- **🚀 运行服务器**: 非调试模式启动服务器
- **🔨 构建项目**: 构建可执行文件
- **🧪 运行所有测试**: 执行所有测试
- **📊 测试覆盖率**: 生成测试覆盖率报告
- **🔍 代码检查**: 运行 `go vet` 检查代码
- **📝 格式化代码**: 格式化所有 Go 代码
- **📦 整理依赖**: 运行 `go mod tidy`
- **🧹 清理构建文件**: 清理构建产物

## ⚙️ 编辑器设置

### 自动格式化
- 保存时自动格式化代码
- 自动整理导入语句
- 使用 `goimports` 工具

### 代码检查
- 保存时自动运行 lint 检查
- 显示代码问题和建议

### 文件监控
- 排除日志、临时文件等不必要的文件监控
- 提升性能

## 🔍 调试技巧

### 1. 查看变量
- 在调试时，左侧面板显示当前作用域的变量
- 可以展开复杂对象查看详细信息
- 支持监视表达式

### 2. 调用栈
- 显示当前函数调用链
- 可以点击切换到不同的调用层级

### 3. 断点管理
- 支持条件断点：右键断点设置条件
- 支持日志断点：断点触发时输出日志而不暂停

### 4. 控制台
- 调试控制台支持执行 Go 表达式
- 可以动态查看和修改变量值

## 🛠️ 常见问题

### 1. 调试器无法启动
- 检查是否安装了 `dlv` (Delve) 调试器
- 运行：`go install github.com/go-delve/delve/cmd/dlv@latest`

### 2. 断点不生效
- 确保是在调试模式下运行（不是普通运行）
- 检查代码是否被优化掉了

### 3. 环境变量问题
- 检查 `.env` 文件是否存在且格式正确
- 确保敏感信息不要提交到 Git

### 4. 数据库连接失败
- 检查数据库服务是否启动
- 验证 `.env` 中的数据库配置

## 📚 扩展推荐

安装推荐扩展后，您将获得：

- **Go 语言支持**: 语法高亮、智能提示、调试
- **Git 集成**: 可视化 Git 历史和差异
- **数据库工具**: 直接在 VS Code 中连接和查询数据库
- **API 测试**: REST 客户端测试 API
- **代码质量**: 拼写检查、TODO 高亮
- **美化界面**: 图标主题、颜色主题

## 🎯 性能优化

- 文件监控排除了不必要的目录
- 搜索排除了构建产物和日志文件
- 合理的调试器配置平衡性能和功能

享受调试吧！🎉 