{"version": "0.2.0", "configurations": [{"name": "🚀 启动服务器 (直接调试)", "type": "go", "request": "launch", "mode": "debug", "program": "${workspaceFolder}/cmd/server/main.go", "cwd": "${workspaceFolder}", "envFile": "${workspaceFolder}/.env", "args": [], "showLog": true, "trace": "verbose", "logOutput": "rpc", "dlvFlags": ["--check-go-version=false"], "console": "integratedTerminal"}, {"name": "🔄 通过main.go启动 (模拟命令行)", "type": "go", "request": "launch", "mode": "debug", "program": "${workspaceFolder}/main.go", "cwd": "${workspaceFolder}", "env": {"CGO_ENABLED": "0", "GOOS": "windows"}, "envFile": "${workspaceFolder}/.env", "args": ["run"], "showLog": true, "trace": "verbose", "logOutput": "rpc", "dlvFlags": ["--check-go-version=false"], "console": "integratedTerminal"}, {"name": "🧪 调试测试", "type": "go", "request": "launch", "mode": "test", "program": "${workspaceFolder}", "cwd": "${workspaceFolder}", "env": {"CGO_ENABLED": "0", "GOOS": "windows"}, "envFile": "${workspaceFolder}/.env", "args": ["-test.v", "-test.run", "<PERSON><PERSON>ain"], "showLog": true, "console": "integratedTerminal"}, {"name": "🔍 调试当前测试文件", "type": "go", "request": "launch", "mode": "test", "program": "${fileDirname}", "cwd": "${workspaceFolder}", "env": {"CGO_ENABLED": "0", "GOOS": "windows"}, "envFile": "${workspaceFolder}/.env", "args": ["-test.v"], "showLog": true, "console": "integratedTerminal"}, {"name": "🐛 附加到正在运行的进程", "type": "go", "request": "attach", "mode": "local", "processId": 0, "cwd": "${workspaceFolder}", "showLog": true, "trace": "verbose"}]}