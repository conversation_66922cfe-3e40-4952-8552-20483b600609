{
	"recommendations": [
		// Go 开发必备
		"golang.go",
		// 代码质量和格式化
		"ms-vscode.vscode-json",
		"redhat.vscode-yaml",
		"ms-vscode.hexdump",
		// Git 相关
		"mhutchie.git-graph",
		"eamodio.gitlens",
		// 数据库工具
		"mtxr.sqltools",
		"mtxr.sqltools-driver-pg",
		// API 开发
		"humao.rest-client",
		"42crunch.vscode-openapi",
		"arjun.swagger-viewer",
		// 容器和部署
		"ms-azuretools.vscode-docker",
		"ms-kubernetes-tools.vscode-kubernetes-tools",
		// 代码片段和效率工具
		"formulahendry.code-runner",
		"streetsidesoftware.code-spell-checker",
		"ms-vscode.vscode-todo-highlight",
		"gruntfuggly.todo-tree",
		// 主题和美化
		"pkief.material-icon-theme",
		"dracula-theme.theme-dracula",
		// 环境变量支持
		"mikestead.dotenv",
		// 配置文件支持
		"ms-vscode.vscode-json5",
		"tamasfe.even-better-toml",
		// 代码注释和文档
		"aaron-bond.better-comments",
		"shd101wyy.markdown-preview-enhanced",
		// 终端增强
		"ms-vscode.powershell"
	],
	"unwantedRecommendations": [
		// 避免冲突的扩展
		"ms-vscode.go"
	]
}