{"version": "2.0.0", "tasks": [{"label": "🔍 检查配置文件", "type": "shell", "command": "echo", "args": ["检查配置文件..."], "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared"}, "options": {"cwd": "${workspaceFolder}"}, "dependsOn": ["📋 显示当前目录和配置文件状态"]}, {"label": "📋 显示当前目录和配置文件状态", "type": "shell", "command": "cmd", "args": ["/c", "echo 当前工作目录: %CD% && echo. && echo 检查配置文件: && dir config\\*.yaml /B 2>nul || echo 配置文件不存在！"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "options": {"cwd": "${workspaceFolder}"}}, {"label": "🚀 运行服务器", "type": "shell", "command": "go", "args": ["run", "main.go", "run"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": ["$go"], "runOptions": {"runOn": "folderOpen"}}, {"label": "🔨 构建项目", "type": "shell", "command": "go", "args": ["build", "-o", "hkchat_api.exe", "cmd/server/main.go"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": ["$go"]}, {"label": "🧪 运行所有测试", "type": "shell", "command": "go", "args": ["test", "./...", "-v"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": ["$go"]}, {"label": "📊 测试覆盖率", "type": "shell", "command": "go", "args": ["test", "./...", "-v", "-cover", "-coverprofile=coverage.out"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": ["$go"]}, {"label": "🔍 代码检查", "type": "shell", "command": "go", "args": ["vet", "./..."], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": ["$go"]}, {"label": "📝 格式化代码", "type": "shell", "command": "go", "args": ["fmt", "./..."], "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared"}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}, {"label": "📦 整理依赖", "type": "shell", "command": "go", "args": ["mod", "tidy"], "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared"}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}, {"label": "🧹 清理构建文件", "type": "shell", "command": "go", "args": ["clean"], "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared"}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": [], "dependsOrder": "sequence", "dependsOn": []}, {"label": "🔄 重新加载配置", "type": "shell", "command": "echo", "args": ["重新加载VS Code窗口来应用配置更改"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "options": {"cwd": "${workspaceFolder}"}}, {"label": "📋 生成代码", "type": "shell", "command": "go", "args": ["generate", "./..."], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": ["$go"]}]}