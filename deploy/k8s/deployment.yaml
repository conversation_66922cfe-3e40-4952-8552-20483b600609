apiVersion: apps/v1
kind: Deployment
metadata:
  name: hkchat-api
  labels:
    app: hkchat-api
spec:
  replicas: 2
  selector:
    matchLabels:
      app: hkchat-api
  template:
    metadata:
      labels:
        app: hkchat-api
    spec:
      containers:
      - name: hkchat-api
        image: hkchat-api:latest
        ports:
        - containerPort: 8080
        env:
        - name: APP_ENV
          value: "prod"
        - name: DB_HOST
          value: "postgres-service"
        - name: DB_PORT
          value: "5432"
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: username
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: password
        - name: DB_NAME
          value: "hkchat"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "256Mi"
            cpu: "200m"

---
apiVersion: v1
kind: Service
metadata:
  name: hkchat-api-service
spec:
  selector:
    app: hkchat-api
  ports:
    - protocol: TCP
      port: 80
      targetPort: 8080
  type: LoadBalancer 