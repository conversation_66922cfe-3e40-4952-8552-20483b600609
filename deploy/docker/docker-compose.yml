version: '3.8'

services:
  # HKChat API 应用
  hkchat-api:
    build:
      context: ../..
      dockerfile: deploy/docker/Dockerfile
    container_name: hkchat_api
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      - APP_ENV=dev
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USER=root
      - DB_PASSWORD=root
      - DB_NAME=hkchat
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - hkchat_network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: hkchat_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: hkchat
      POSTGRES_USER: root
      POSTGRES_PASSWORD: root
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --locale=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - hkchat_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U root -d hkchat"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存 (可选)
  redis:
    image: redis:7-alpine
    container_name: hkchat_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - hkchat_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

networks:
  hkchat_network:
    driver: bridge

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local 