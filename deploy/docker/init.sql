-- 数据库初始化脚本
-- 创建UUID扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 创建时间戳更新函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 这里可以添加其他初始化数据或配置
-- 例如：创建默认管理员用户、初始化设置等

-- 示例：插入默认数据（可选）
-- INSERT INTO users (id, name, email, role) VALUES
-- ('admin-001', 'Administrator', '<EMAIL>', 'admin'); 