package main

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"
)

// BuildTarget 构建目标
type BuildTarget struct {
	OS       string
	Arch     string
	FileName string
}

// 支持的构建目标
var supportedTargets = []BuildTarget{
	{"windows", "amd64", "hkchat_api.exe"},
	{"windows", "386", "hkchat_api.exe"},
	{"windows", "arm64", "hkchat_api.exe"},
	{"darwin", "amd64", "hkchat_api"},
	{"darwin", "arm64", "hkchat_api"},
	{"linux", "amd64", "hkchat_api"},
	{"linux", "386", "hkchat_api"},
	{"linux", "arm64", "hkchat_api"},
}

var versionDir string

func main() {
	fmt.Println("🔨 构建项目...")

	// 生成版本目录名
	versionDir = generateVersionDir()
	fmt.Printf("📅 版本目录: %s\n", versionDir)

	// 解析命令行参数
	var targets []BuildTarget
	if len(os.Args) < 2 {
		// 没有参数，构建当前平台
		targets = getCurrentPlatformTargets()
	} else {
		platform := strings.ToLower(os.Args[1])
		targets = getTargetsByPlatform(platform)
	}

	if len(targets) == 0 {
		fmt.Println("❌ 未找到匹配的构建目标")
		showHelp()
		os.Exit(1)
	}

	// 创建bin目录和版本目录
	versionPath := filepath.Join("bin", versionDir)
	if err := os.MkdirAll(versionPath, os.ModePerm); err != nil {
		fmt.Printf("❌ 创建版本目录失败: %v\n", err)
		os.Exit(1)
	}

	// 构建所有目标
	successCount := 0
	for _, target := range targets {
		if buildTarget(target) {
			successCount++
		}
	}

	fmt.Printf("\n🎉 构建完成！成功构建 %d/%d 个目标\n", successCount, len(targets))
	fmt.Printf("📦 构建结果保存在: %s\n", versionPath)
}

// generateVersionDir 生成版本目录名
func generateVersionDir() string {
	now := time.Now()
	dateStr := now.Format("2006-01-02")

	// 尝试从环境变量或git获取版本号
	version := getVersion()

	return fmt.Sprintf("%s-%s", dateStr, version)
}

// getVersion 获取版本号
func getVersion() string {
	// 首先尝试从环境变量获取
	if version := os.Getenv("VERSION"); version != "" {
		return strings.TrimSpace(version)
	}

	// 尝试从git tag获取
	cmd := exec.Command("git", "describe", "--tags", "--abbrev=0")
	if output, err := cmd.Output(); err == nil {
		return strings.TrimSpace(string(output))
	}

	// 尝试从git commit获取短hash
	cmd = exec.Command("git", "rev-parse", "--short", "HEAD")
	if output, err := cmd.Output(); err == nil {
		return fmt.Sprintf("git-%s", strings.TrimSpace(string(output)))
	}

	// 默认版本号
	return "v1.0.0"
}

// getCurrentPlatformTargets 获取当前平台的构建目标
func getCurrentPlatformTargets() []BuildTarget {
	currentOS := os.Getenv("GOOS")
	if currentOS == "" {
		// 如果没有设置GOOS环境变量，使用runtime.GOOS
		cmd := exec.Command("go", "env", "GOOS")
		output, err := cmd.Output()
		if err != nil {
			fmt.Printf("❌ 获取当前操作系统失败: %v\n", err)
			return nil
		}
		currentOS = strings.TrimSpace(string(output))
	}

	currentArch := os.Getenv("GOARCH")
	if currentArch == "" {
		// 如果没有设置GOARCH环境变量，使用runtime.GOARCH
		cmd := exec.Command("go", "env", "GOARCH")
		output, err := cmd.Output()
		if err != nil {
			fmt.Printf("❌ 获取当前架构失败: %v\n", err)
			return nil
		}
		currentArch = strings.TrimSpace(string(output))
	}

	for _, target := range supportedTargets {
		if target.OS == currentOS && target.Arch == currentArch {
			return []BuildTarget{target}
		}
	}

	// 如果没有找到完全匹配的，返回当前OS的默认架构
	for _, target := range supportedTargets {
		if target.OS == currentOS && target.Arch == "amd64" {
			return []BuildTarget{target}
		}
	}

	return nil
}

// getTargetsByPlatform 根据平台参数获取构建目标
func getTargetsByPlatform(platform string) []BuildTarget {
	switch platform {
	case "all":
		return supportedTargets
	case "windows", "win":
		return filterTargetsByOS("windows")
	case "macos", "darwin", "mac":
		return filterTargetsByOS("darwin")
	case "linux":
		return filterTargetsByOS("linux")
	default:
		// 尝试解析具体的平台/架构组合
		parts := strings.Split(platform, "/")
		if len(parts) == 2 {
			os := parts[0]
			arch := parts[1]
			for _, target := range supportedTargets {
				if target.OS == os && target.Arch == arch {
					return []BuildTarget{target}
				}
			}
		}
		return nil
	}
}

// filterTargetsByOS 按操作系统过滤构建目标
func filterTargetsByOS(targetOS string) []BuildTarget {
	var targets []BuildTarget
	for _, target := range supportedTargets {
		if target.OS == targetOS {
			targets = append(targets, target)
		}
	}
	return targets
}

// buildTarget 构建指定目标
func buildTarget(target BuildTarget) bool {
	// 创建目标目录：bin/日期-版本/平台-架构/
	dirName := fmt.Sprintf("%s-%s", target.OS, target.Arch)
	outputDir := filepath.Join("bin", versionDir, dirName)
	if err := os.MkdirAll(outputDir, os.ModePerm); err != nil {
		fmt.Printf("❌ 创建目录 %s 失败: %v\n", outputDir, err)
		return false
	}

	// 输出文件路径
	outputPath := filepath.Join(outputDir, target.FileName)

	fmt.Printf("📦 构建 %s/%s -> %s\n", target.OS, target.Arch, outputPath)

	// 构建命令
	cmd := exec.Command("go", "build", "-o", outputPath, "cmd/server/main.go")
	cmd.Env = append(os.Environ(),
		"GOOS="+target.OS,
		"GOARCH="+target.Arch,
	)
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	if err := cmd.Run(); err != nil {
		fmt.Printf("❌ 构建 %s/%s 失败: %v\n", target.OS, target.Arch, err)
		return false
	}

	fmt.Printf("✅ 构建 %s/%s 成功\n", target.OS, target.Arch)
	return true
}

// showHelp 显示帮助信息
func showHelp() {
	fmt.Println("\n📖 使用说明:")
	fmt.Println("  go run main.go build [平台]")
	fmt.Println()
	fmt.Println("支持的平台参数:")
	fmt.Println("  (无参数)     - 构建当前平台")
	fmt.Println("  all          - 构建所有平台")
	fmt.Println("  windows      - 构建所有Windows平台")
	fmt.Println("  macos/darwin - 构建所有macOS平台")
	fmt.Println("  linux        - 构建所有Linux平台")
	fmt.Println()
	fmt.Println("具体平台/架构组合:")
	for _, target := range supportedTargets {
		fmt.Printf("  %s/%s\n", target.OS, target.Arch)
	}
	fmt.Println()
	fmt.Println("环境变量:")
	fmt.Println("  VERSION      - 指定版本号 (默认从git tag获取或使用v1.0.0)")
	fmt.Println()
	fmt.Println("示例:")
	fmt.Println("  go run main.go build")
	fmt.Println("  go run main.go build all")
	fmt.Println("  go run main.go build windows")
	fmt.Println("  go run main.go build linux/amd64")
	fmt.Println("  VERSION=v2.0.0 go run main.go build all")
}
