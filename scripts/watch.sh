#!/bin/bash

# 自动监听文件变化并重新编译运行（需要安装 air）

set -e

echo "🔧 启动文件监听模式"

# 检查是否安装了 air
if ! command -v air &> /dev/null; then
    echo "📦 正在安装 air..."
    go install github.com/cosmtrek/air@latest
fi

# 检查是否在正确的目录
if [ ! -f "go.mod" ]; then
    echo "❌ 请在项目根目录运行此脚本"
    exit 1
fi

# 创建 .air.toml 配置文件
cat > .air.toml << 'EOF'
root = "."
testdata_dir = "testdata"
tmp_dir = "tmp"

[build]
  args_bin = []
  bin = "./tmp/main"
  cmd = "cd src && go build -o ../tmp/main ."
  delay = 1000
  exclude_dir = ["assets", "tmp", "vendor", "docs"]
  exclude_file = []
  exclude_regex = [""]
  exclude_unchanged = false
  follow_symlink = false
  full_bin = ""
  include_dir = []
  include_ext = ["go", "tpl", "tmpl", "html"]
  kill_delay = "0s"
  log = "build-errors.log"
  send_interrupt = false
  stop_on_root = false

[color]
  app = ""
  build = "yellow"
  main = "magenta"
  runner = "green"
  watcher = "cyan"

[log]
  time = false

[misc]
  clean_on_exit = false

[screen]
  clear_on_rebuild = false
EOF

echo "🚀 启动自动重载服务器..."
air