@echo off
chcp 65001 >nul

echo 🚀 开始生成数据库实体类...

rem 检查是否存在 go.mod
if not exist "go.mod" (
    echo ❌ 请在项目根目录下运行此脚本
    pause
    exit /b 1
)

rem 安装必要的依赖
echo 📦 检查依赖...
go mod tidy

rem 运行生成脚本
echo 🏗️开始生成实体类...
cd scripts
go run generate_entities.go

if %errorlevel% equ 0 (
    echo ✅ 实体类生成完成！
    echo 📁 生成的文件位于: internal\models\entities\
) else (
    echo ❌ 实体类生成失败！
    pause
    exit /b 1
)

pause 