# 数据库实体类生成脚本

## 功能说明

这个脚本可以自动连接到你的 PostgreSQL 数据库，读取所有表的结构，并为每个表生成对应的 Go 实体类。

## 文件说明

- `generate_entities.go` - 主要的生成脚本
- `run_generate_entities.sh` - Linux/Mac 运行脚本
- `run_generate_entities.bat` - Windows 运行脚本

## 使用方法

### 前提条件

1. 确保你的 Go 环境已经安装
2. 确保项目根目录有 `go.mod` 文件
3. 确保数据库连接配置在 `config/dev.yaml` 中是正确的

### 运行脚本

#### Linux/Mac 用户

```bash
# 在项目根目录下执行
chmod +x scripts/run_generate_entities.sh
./scripts/run_generate_entities.sh
```

#### Windows 用户

```cmd
# 在项目根目录下执行
scripts\run_generate_entities.bat
```

#### 直接运行 Go 脚本

```bash
# 在项目根目录下执行
cd scripts
go run generate_entities.go
```

## 生成的实体类特点

### 1. 自动类型映射

| PostgreSQL 类型 | Go 类型 | 说明 |
|----------------|---------|------|
| integer/int4 | int | 整数类型 |
| bigint/int8 | int64 | 长整数类型 |
| smallint/int2 | int16 | 短整数类型 |
| real | float32 | 单精度浮点数 |
| double precision/float8 | float64 | 双精度浮点数 |
| boolean | bool | 布尔类型 |
| text/varchar | string | 字符串类型 |
| timestamp | time.Time | 时间类型 |
| json/jsonb | JSON | JSON 类型（自定义） |
| uuid | string | UUID 类型 |
| bytea | []byte | 字节数组 |

### 2. 可空类型处理

- 对于可空的字段（除主键外），会自动生成指针类型（如 `*string`, `*int`）
- 主键字段即使可空也不会生成指针类型

### 3. GORM 标签

自动生成的 GORM 标签包括：
- `primaryKey` - 主键标识
- `column:列名` - 列名映射
- `type:数据类型` - 数据类型约束
- `not null` - 非空约束
- `default:默认值` - 默认值

### 4. JSON 标签

自动生成 JSON 标签，使用蛇形命名（snake_case）

## 生成示例

假设数据库中有一个 `users` 表：

```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    is_active BOOLEAN DEFAULT true,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

生成的实体类 `internal/models/entities/users.go`：

```go
package entities

import (
	"time"
	"database/sql/driver"
	"encoding/json"
	"errors"
)

// Users users 表实体
type Users struct {
	Id           string     `gorm:"primaryKey;type:uuid;default:gen_random_uuid()" json:"id"`
	Username     string     `gorm:"type:varchar(50);not null" json:"username"`
	Email        string     `gorm:"type:varchar(100);not null" json:"email"`
	PasswordHash string     `gorm:"type:text;not null" json:"password_hash"`
	IsActive     *bool      `gorm:"type:boolean;default:true" json:"is_active"`
	Metadata     JSON       `gorm:"type:jsonb" json:"metadata"`
	CreatedAt    *time.Time `gorm:"type:timestamptz;default:NOW()" json:"created_at"`
	UpdatedAt    *time.Time `gorm:"type:timestamptz;default:NOW()" json:"updated_at"`
}

// TableName 设置表名
func (Users) TableName() string {
	return "users"
}
```

## 注意事项

1. **备份现有文件**：脚本会覆盖 `internal/models/entities/` 目录下的同名文件，请先备份重要的自定义代码

2. **数据库连接**：确保数据库连接配置正确，脚本需要能够连接到数据库

3. **权限要求**：数据库用户需要有查询 `information_schema` 的权限

4. **JSON 类型**：使用了自定义的 `JSON` 类型，确保 `types.go` 文件存在

5. **命名规范**：
   - 表名转换为 Pascal 命名（如 `user_profiles` → `UserProfiles`）
   - 字段名转换为 Pascal 命名（如 `created_at` → `CreatedAt`）
   - JSON 标签使用蛇形命名（如 `CreatedAt` → `"created_at"`）

## 自定义修改

如果需要自定义生成逻辑，可以修改 `generate_entities.go` 中的以下函数：

- `mapPostgresToGoType()` - 修改类型映射规则
- `generateGormTag()` - 修改 GORM 标签生成规则
- `generateJsonTag()` - 修改 JSON 标签生成规则
- `entityTemplate` - 修改实体类模板

## 故障排除

### 连接数据库失败

1. 检查 `config/dev.yaml` 中的数据库配置
2. 确保数据库服务正在运行
3. 检查网络连接和防火墙设置

### 权限不足

确保数据库用户有以下权限：
```sql
GRANT SELECT ON information_schema.tables TO your_user;
GRANT SELECT ON information_schema.columns TO your_user;
```

### 依赖缺失

运行以下命令安装依赖：
```bash
go mod tidy
```

如果仍有问题，手动添加依赖：
```bash
go get github.com/lib/pq
go get gopkg.in/yaml.v3
``` 