package main

import (
	"database/sql"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"
	"text/template"

	_ "github.com/lib/pq"
	"gopkg.in/yaml.v3"
)

// Config 配置结构
type Config struct {
	Database DatabaseConfig `yaml:"database"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Host     string `yaml:"host"`
	Port     int    `yaml:"port"`
	User     string `yaml:"user"`
	Password string `yaml:"password"`
	DBName   string `yaml:"dbname"`
	SSLMode  string `yaml:"sslmode"`
}

// TableInfo 表信息
type TableInfo struct {
	TableName string
	Columns   []ColumnInfo
}

// ColumnInfo 列信息
type ColumnInfo struct {
	ColumnName    string
	DataType      string
	IsNullable    string
	ColumnDefault sql.NullString
	CharMaxLength sql.NullInt64
	NumericScale  sql.NullInt64
	IsPrimaryKey  bool
	GoType        string
	GormTag       string
	JsonTag       string
}

// 实体类模板
const entityTemplate = `package entities
{{- if .HasTime }}

import (
	"time"
)
{{- end }}

// {{.TableName | ToCamelCase}} {{.TableName}} 表实体
type {{.TableName | ToCamelCase}} struct {
{{- range .Columns }}
	{{.ColumnName | ToCamelCase}}	{{.GoType}}	` + "`" + `{{if .GormTag}}{{.GormTag}} {{end}}{{.JsonTag}}` + "`" + `
{{- end }}
}

// TableName 设置表名
func ({{.TableName | ToCamelCase}}) TableName() string {
	return "{{.TableName}}"
}
`

func main() {
	// 读取配置文件
	config, err := loadConfig("../config/dev.yaml")
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 连接数据库
	db, err := connectDB(config.Database)
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}
	defer db.Close()

	fmt.Println("🔗 成功连接到数据库")

	// 获取所有表信息
	tables, err := getTables(db)
	if err != nil {
		log.Fatalf("获取表信息失败: %v", err)
	}

	fmt.Printf("📋 发现 %d 个表\n", len(tables))

	// 为每个表生成实体类
	for _, table := range tables {
		fmt.Printf("🏗️  正在生成 %s 实体类...\n", table.TableName)

		err := generateEntity(table)
		if err != nil {
			log.Printf("生成 %s 实体类失败: %v", table.TableName, err)
			continue
		}

		fmt.Printf("✅ %s 实体类生成成功\n", table.TableName)
	}

	fmt.Println("🎉 所有实体类生成完成！")
}

// loadConfig 加载配置文件
func loadConfig(configPath string) (*Config, error) {
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, err
	}

	var config Config
	err = yaml.Unmarshal(data, &config)
	if err != nil {
		return nil, err
	}

	return &config, nil
}

// connectDB 连接数据库
func connectDB(cfg DatabaseConfig) (*sql.DB, error) {
	dsn := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
		cfg.Host, cfg.Port, cfg.User, cfg.Password, cfg.DBName, cfg.SSLMode)

	return sql.Open("postgres", dsn)
}

// getTables 获取所有表信息
func getTables(db *sql.DB) ([]TableInfo, error) {
	// 查询所有用户创建的表
	query := `
		SELECT table_name 
		FROM information_schema.tables 
		WHERE table_schema = 'public' 
		AND table_type = 'BASE TABLE'
		ORDER BY table_name
	`

	rows, err := db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var tables []TableInfo
	for rows.Next() {
		var tableName string
		if err := rows.Scan(&tableName); err != nil {
			return nil, err
		}

		// 获取表的列信息
		columns, err := getTableColumns(db, tableName)
		if err != nil {
			return nil, err
		}

		tables = append(tables, TableInfo{
			TableName: tableName,
			Columns:   columns,
		})
	}

	return tables, rows.Err()
}

// getTableColumns 获取表的列信息
func getTableColumns(db *sql.DB, tableName string) ([]ColumnInfo, error) {
	query := `
		SELECT 
			c.column_name,
			c.data_type,
			c.is_nullable,
			c.column_default,
			c.character_maximum_length,
			c.numeric_scale,
			CASE WHEN pk.column_name IS NOT NULL THEN true ELSE false END as is_primary_key
		FROM information_schema.columns c
		LEFT JOIN (
			SELECT ku.column_name
			FROM information_schema.table_constraints tc
			JOIN information_schema.key_column_usage ku ON tc.constraint_name = ku.constraint_name
			WHERE tc.table_name = $1 AND tc.constraint_type = 'PRIMARY KEY'
		) pk ON c.column_name = pk.column_name
		WHERE c.table_name = $1
		ORDER BY c.ordinal_position
	`

	rows, err := db.Query(query, tableName)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var columns []ColumnInfo
	for rows.Next() {
		var col ColumnInfo
		err := rows.Scan(
			&col.ColumnName,
			&col.DataType,
			&col.IsNullable,
			&col.ColumnDefault,
			&col.CharMaxLength,
			&col.NumericScale,
			&col.IsPrimaryKey,
		)
		if err != nil {
			return nil, err
		}

		// 映射 PostgreSQL 类型到 Go 类型
		col.GoType = mapPostgresToGoType(col)
		col.GormTag = generateGormTag(col)
		col.JsonTag = generateJsonTag(col)

		columns = append(columns, col)
	}

	return columns, rows.Err()
}

// mapPostgresToGoType 映射 PostgreSQL 类型到 Go 类型
func mapPostgresToGoType(col ColumnInfo) string {
	nullable := col.IsNullable == "YES" && !col.IsPrimaryKey

	switch col.DataType {
	case "integer", "int4":
		if nullable {
			return "*int"
		}
		return "int"
	case "bigint", "int8":
		if nullable {
			return "*int64"
		}
		return "int64"
	case "smallint", "int2":
		if nullable {
			return "*int16"
		}
		return "int16"
	case "real":
		if nullable {
			return "*float32"
		}
		return "float32"
	case "double precision", "float8":
		if nullable {
			return "*float64"
		}
		return "float64"
	case "boolean":
		if nullable {
			return "*bool"
		}
		return "bool"
	case "text", "varchar", "character varying", "char", "character":
		if nullable {
			return "*string"
		}
		return "string"
	case "timestamp", "timestamp with time zone", "timestamp without time zone":
		if nullable {
			return "*time.Time"
		}
		return "time.Time"
	case "date":
		if nullable {
			return "*time.Time"
		}
		return "time.Time"
	case "time", "time with time zone", "time without time zone":
		if nullable {
			return "*time.Time"
		}
		return "time.Time"
	case "json", "jsonb":
		return "JSON"
	case "uuid":
		if nullable {
			return "*string"
		}
		return "string"
	case "bytea":
		return "[]byte"
	default:
		// 默认使用 string 类型
		if nullable {
			return "*string"
		}
		return "string"
	}
}

// generateGormTag 生成 GORM 标签
func generateGormTag(col ColumnInfo) string {
	var tags []string

	// 主键
	if col.IsPrimaryKey {
		tags = append(tags, "primaryKey")
	}

	// 列名
	if col.ColumnName != toSnakeCase(toCamelCase(col.ColumnName)) {
		tags = append(tags, fmt.Sprintf("column:%s", col.ColumnName))
	}

	// 数据类型
	switch col.DataType {
	case "varchar", "character varying":
		if col.CharMaxLength.Valid {
			tags = append(tags, fmt.Sprintf("type:varchar(%d)", col.CharMaxLength.Int64))
		} else {
			tags = append(tags, "type:varchar(255)")
		}
	case "char", "character":
		if col.CharMaxLength.Valid {
			tags = append(tags, fmt.Sprintf("type:char(%d)", col.CharMaxLength.Int64))
		}
	case "text":
		tags = append(tags, "type:text")
	case "json":
		tags = append(tags, "type:json")
	case "jsonb":
		tags = append(tags, "type:jsonb")
	case "uuid":
		tags = append(tags, "type:uuid")
	case "timestamp with time zone":
		tags = append(tags, "type:timestamptz")
	case "timestamp without time zone":
		tags = append(tags, "type:timestamp")
	}

	// 非空约束
	if col.IsNullable == "NO" && !col.IsPrimaryKey {
		tags = append(tags, "not null")
	}

	// 默认值
	if col.ColumnDefault.Valid && col.ColumnDefault.String != "" {
		defaultVal := col.ColumnDefault.String
		// 处理一些常见的默认值
		if !strings.Contains(defaultVal, "nextval") { // 排除序列默认值
			tags = append(tags, fmt.Sprintf("default:%s", defaultVal))
		}
	}

	if len(tags) == 0 {
		return ""
	}

	return fmt.Sprintf("gorm:\"%s\"", strings.Join(tags, ";"))
}

// generateJsonTag 生成 JSON 标签
func generateJsonTag(col ColumnInfo) string {
	jsonName := toSnakeCase(col.ColumnName)
	return fmt.Sprintf("json:\"%s\"", jsonName)
}

// generateEntity 生成实体类文件
func generateEntity(table TableInfo) error {
	// 创建模板函数
	funcMap := template.FuncMap{
		"ToCamelCase": toCamelCase,
		"ToSnakeCase": toSnakeCase,
	}

	// 解析模板
	tmpl, err := template.New("entity").Funcs(funcMap).Parse(entityTemplate)
	if err != nil {
		return err
	}

	// 检查是否包含 JSON 类型
	hasJSON := false
	hasTime := false
	for _, col := range table.Columns {
		if col.GoType == "JSON" {
			hasJSON = true
		}
		if strings.Contains(col.GoType, "time.Time") {
			hasTime = true
		}
	}

	// 准备模板数据
	data := struct {
		TableInfo
		HasJSON bool
		HasTime bool
	}{
		TableInfo: table,
		HasJSON:   hasJSON,
		HasTime:   hasTime,
	}

	// 创建输出目录
	outputDir := "../internal/models/entities"
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		return err
	}

	// 创建文件
	fileName := fmt.Sprintf("%s.go", toSnakeCase(table.TableName))
	filePath := filepath.Join(outputDir, fileName)

	file, err := os.Create(filePath)
	if err != nil {
		return err
	}
	defer file.Close()

	// 执行模板
	return tmpl.Execute(file, data)
}

// toCamelCase 转换为驼峰命名
func toCamelCase(s string) string {
	words := strings.FieldsFunc(s, func(r rune) bool {
		return r == '_' || r == '-' || r == ' '
	})

	result := ""
	for _, word := range words {
		if len(word) > 0 {
			result += strings.ToUpper(string(word[0])) + strings.ToLower(word[1:])
		}
	}
	return result
}

// toSnakeCase 转换为蛇形命名
func toSnakeCase(s string) string {
	var result []rune
	for i, r := range s {
		if i > 0 && r >= 'A' && r <= 'Z' {
			result = append(result, '_')
		}
		result = append(result, r)
	}
	return strings.ToLower(string(result))
}
