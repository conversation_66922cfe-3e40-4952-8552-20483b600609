package main

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"text/template"
)

// RepositoryInfo repository 信息
type RepositoryInfo struct {
	EntityName      string           // 实体名称，如 Document
	PackageName     string           // 包名称，如 document
	TableName       string           // 表名，如 document
	Fields          string           // 字段列表（简化版，避免JSON字段）
	SearchFields    []string         // 搜索字段
	BusinessMethods []BusinessMethod // 业务方法
}

// BusinessMethod 业务方法定义
type BusinessMethod struct {
	Name           string   // 方法名
	Description    string   // 方法描述
	Params         []string // 参数列表
	ReturnType     string   // 返回类型
	Implementation string   // 实现代码
}

// Repository 接口模板
const interfaceTemplate = `package interfaces

import (
	"hkchat_api/internal/models/entities"
)

// {{.EntityName}}Repository {{.EntityName}} 仓储接口
type {{.EntityName}}Repository interface {
	// 嵌入基础接口，可以直接使用 BaseRepository 的所有方法
	BaseRepositoryInterface

	// {{.EntityName}} 特有的业务查询方法
{{- range .BusinessMethods }}
	{{.Name}}({{range $i, $param := .Params}}{{if $i}}, {{end}}{{$param}}{{end}}) {{.ReturnType}}
{{- end }}
{{- if len .SearchFields }}
	SearchByKeyword(keyword string, offset, limit int) ([]*entities.{{.EntityName}}, error)
{{- end }}
}
`

// Repository 实现模板
const implementationTemplate = `package repository

import (
	"hkchat_api/internal/models/entities"
	"hkchat_api/internal/repository/interfaces"

	"gorm.io/gorm"
)

// {{.PackageName}}Repository {{.EntityName}} 仓储实现
type {{.PackageName}}Repository struct {
	*BaseRepository        // 组合基础仓储
	{{.PackageName}}Fields      string // {{.EntityName}} 字段列表（避免复杂JSON字段的问题）
}

// {{.EntityName}}Repository {{.EntityName}} 仓储接口类型（重新定义以避免循环引用）
type {{.EntityName}}Repository = interfaces.{{.EntityName}}Repository

// New{{.EntityName}}Repository 创建 {{.EntityName}} 仓储实例
func New{{.EntityName}}Repository(db *gorm.DB) {{.EntityName}}Repository {
	return &{{.PackageName}}Repository{
		BaseRepository: NewBaseRepository(db),
		{{.PackageName}}Fields:     "{{.Fields}}",
	}
}

// Get{{.EntityName}}Fields 获取 {{.EntityName}} 字段列表（供外部使用）
func (r *{{.PackageName}}Repository) Get{{.EntityName}}Fields() string {
	return r.{{.PackageName}}Fields
}

// ==================== 业务特有方法 ====================

{{- range .BusinessMethods }}
// {{.Name}} {{.Description}}
func (r *{{$.PackageName}}Repository) {{.Name}}({{range $i, $param := .Params}}{{if $i}}, {{end}}{{$param}}{{end}}) {{.ReturnType}} {
	{{.Implementation}}
}

{{- end }}

{{- if len .SearchFields }}
// SearchByKeyword 根据关键词搜索{{.EntityName}}
func (r *{{.PackageName}}Repository) SearchByKeyword(keyword string, offset, limit int) ([]*entities.{{.EntityName}}, error) {
	var entities []*entities.{{.EntityName}}
	searchFields := []string{
	{{- range .SearchFields }}
		"{{.}}",
	{{- end }}
	}
	err := r.BaseRepository.SearchWithSelect(&entities, r.{{.PackageName}}Fields, searchFields, keyword, offset, limit)
	return entities, err
}
{{- end }}
`

func main2() {
	// 定义需要生成的所有 repository
	repositories := []RepositoryInfo{
		{
			EntityName:   "Channel",
			PackageName:  "channel",
			TableName:    "channel",
			Fields:       "id, user_id, name, description, created_at, updated_at, type",
			SearchFields: []string{"name", "description"},
			BusinessMethods: []BusinessMethod{
				{
					Name:        "GetByUserID",
					Description: "根据用户ID获取频道列表",
					Params:      []string{"userID string", "offset, limit int"},
					ReturnType:  "([]*entities.Channel, error)",
					Implementation: `var entities []*entities.Channel
	err := r.BaseRepository.WhereWithSelectAndPagination(&entities, r.channelFields, "user_id = ?", offset, limit, userID)
	return entities, err`,
				},
				{
					Name:        "GetByName",
					Description: "根据名称获取频道",
					Params:      []string{"name string"},
					ReturnType:  "(*entities.Channel, error)",
					Implementation: `var entity entities.Channel
	err := r.BaseRepository.First(&entity, "name = ?", name)
	if err != nil {
		return nil, err
	}
	return &entity, nil`,
				},
				{
					Name:        "GetByType",
					Description: "根据类型获取频道列表",
					Params:      []string{"channelType string", "offset, limit int"},
					ReturnType:  "([]*entities.Channel, error)",
					Implementation: `var entities []*entities.Channel
	err := r.BaseRepository.WhereWithSelectAndPagination(&entities, r.channelFields, "type = ?", offset, limit, channelType)
	return entities, err`,
				},
			},
		},
		{
			EntityName:   "Group",
			PackageName:  "group",
			TableName:    "group",
			Fields:       "id, user_id, name, description, created_at, updated_at",
			SearchFields: []string{"name", "description"},
			BusinessMethods: []BusinessMethod{
				{
					Name:        "GetByUserID",
					Description: "根据用户ID获取群组列表",
					Params:      []string{"userID string", "offset, limit int"},
					ReturnType:  "([]*entities.Group, error)",
					Implementation: `var entities []*entities.Group
	err := r.BaseRepository.WhereWithSelectAndPagination(&entities, r.groupFields, "user_id = ?", offset, limit, userID)
	return entities, err`,
				},
				{
					Name:        "GetByName",
					Description: "根据名称获取群组",
					Params:      []string{"name string"},
					ReturnType:  "(*entities.Group, error)",
					Implementation: `var entity entities.Group
	err := r.BaseRepository.First(&entity, "name = ?", name)
	if err != nil {
		return nil, err
	}
	return &entity, nil`,
				},
			},
		},
		{
			EntityName:   "Document",
			PackageName:  "document",
			TableName:    "document",
			Fields:       "id, collection_name, name, title, filename, created_at, updated_at",
			SearchFields: []string{"name", "title", "filename"},
			BusinessMethods: []BusinessMethod{
				{
					Name:        "GetByCollectionName",
					Description: "根据集合名称获取文档列表",
					Params:      []string{"collectionName string", "offset, limit int"},
					ReturnType:  "([]*entities.Document, error)",
					Implementation: `var entities []*entities.Document
	err := r.BaseRepository.WhereWithSelectAndPagination(&entities, r.documentFields, "collection_name = ?", offset, limit, collectionName)
	return entities, err`,
				},
				{
					Name:        "GetByTitle",
					Description: "根据标题获取文档",
					Params:      []string{"title string"},
					ReturnType:  "(*entities.Document, error)",
					Implementation: `var entity entities.Document
	err := r.BaseRepository.First(&entity, "title = ?", title)
	if err != nil {
		return nil, err
	}
	return &entity, nil`,
				},
			},
		},
		{
			EntityName:   "File",
			PackageName:  "file",
			TableName:    "file",
			Fields:       "id, user_id, filename, data, created_at, updated_at",
			SearchFields: []string{"filename"},
			BusinessMethods: []BusinessMethod{
				{
					Name:        "GetByUserID",
					Description: "根据用户ID获取文件列表",
					Params:      []string{"userID string", "offset, limit int"},
					ReturnType:  "([]*entities.File, error)",
					Implementation: `var entities []*entities.File
	err := r.BaseRepository.WhereWithSelectAndPagination(&entities, r.fileFields, "user_id = ?", offset, limit, userID)
	return entities, err`,
				},
				{
					Name:        "GetByFilename",
					Description: "根据文件名获取文件",
					Params:      []string{"filename string"},
					ReturnType:  "(*entities.File, error)",
					Implementation: `var entity entities.File
	err := r.BaseRepository.First(&entity, "filename = ?", filename)
	if err != nil {
		return nil, err
	}
	return &entity, nil`,
				},
			},
		},
		{
			EntityName:   "Folder",
			PackageName:  "folder",
			TableName:    "folder",
			Fields:       "id, user_id, name, parent_id, created_at, updated_at",
			SearchFields: []string{"name"},
			BusinessMethods: []BusinessMethod{
				{
					Name:        "GetByUserID",
					Description: "根据用户ID获取文件夹列表",
					Params:      []string{"userID string", "offset, limit int"},
					ReturnType:  "([]*entities.Folder, error)",
					Implementation: `var entities []*entities.Folder
	err := r.BaseRepository.WhereWithSelectAndPagination(&entities, r.folderFields, "user_id = ?", offset, limit, userID)
	return entities, err`,
				},
				{
					Name:        "GetByParentID",
					Description: "根据父文件夹ID获取子文件夹列表",
					Params:      []string{"parentID string", "offset, limit int"},
					ReturnType:  "([]*entities.Folder, error)",
					Implementation: `var entities []*entities.Folder
	err := r.BaseRepository.WhereWithSelectAndPagination(&entities, r.folderFields, "parent_id = ?", offset, limit, parentID)
	return entities, err`,
				},
				{
					Name:        "GetRootFolders",
					Description: "获取根文件夹列表",
					Params:      []string{"userID string", "offset, limit int"},
					ReturnType:  "([]*entities.Folder, error)",
					Implementation: `var entities []*entities.Folder
	err := r.BaseRepository.WhereWithSelectAndPagination(&entities, r.folderFields, "user_id = ? AND parent_id IS NULL", offset, limit, userID)
	return entities, err`,
				},
			},
		},
		{
			EntityName:   "Note",
			PackageName:  "note",
			TableName:    "note",
			Fields:       "id, user_id, title, content, created_at, updated_at",
			SearchFields: []string{"title", "content"},
			BusinessMethods: []BusinessMethod{
				{
					Name:        "GetByUserID",
					Description: "根据用户ID获取笔记列表",
					Params:      []string{"userID string", "offset, limit int"},
					ReturnType:  "([]*entities.Note, error)",
					Implementation: `var entities []*entities.Note
	err := r.BaseRepository.WhereWithSelectAndPagination(&entities, r.noteFields, "user_id = ?", offset, limit, userID)
	return entities, err`,
				},
				{
					Name:        "GetByTitle",
					Description: "根据标题获取笔记",
					Params:      []string{"title string"},
					ReturnType:  "(*entities.Note, error)",
					Implementation: `var entity entities.Note
	err := r.BaseRepository.First(&entity, "title = ?", title)
	if err != nil {
		return nil, err
	}
	return &entity, nil`,
				},
			},
		},
		{
			EntityName:   "Prompt",
			PackageName:  "prompt",
			TableName:    "prompt",
			Fields:       "id, command, user_id, title, content, created_at, updated_at",
			SearchFields: []string{"command", "title", "content"},
			BusinessMethods: []BusinessMethod{
				{
					Name:        "GetByUserID",
					Description: "根据用户ID获取提示词列表",
					Params:      []string{"userID string", "offset, limit int"},
					ReturnType:  "([]*entities.Prompt, error)",
					Implementation: `var entities []*entities.Prompt
	err := r.BaseRepository.WhereWithSelectAndPagination(&entities, r.promptFields, "user_id = ?", offset, limit, userID)
	return entities, err`,
				},
				{
					Name:        "GetByCommand",
					Description: "根据命令获取提示词",
					Params:      []string{"command string"},
					ReturnType:  "(*entities.Prompt, error)",
					Implementation: `var entity entities.Prompt
	err := r.BaseRepository.First(&entity, "command = ?", command)
	if err != nil {
		return nil, err
	}
	return &entity, nil`,
				},
			},
		},
		{
			EntityName:   "Tool",
			PackageName:  "tool",
			TableName:    "tool",
			Fields:       "id, user_id, name, content, specs, created_at, updated_at",
			SearchFields: []string{"name"},
			BusinessMethods: []BusinessMethod{
				{
					Name:        "GetByUserID",
					Description: "根据用户ID获取工具列表",
					Params:      []string{"userID string", "offset, limit int"},
					ReturnType:  "([]*entities.Tool, error)",
					Implementation: `var entities []*entities.Tool
	err := r.BaseRepository.WhereWithSelectAndPagination(&entities, r.toolFields, "user_id = ?", offset, limit, userID)
	return entities, err`,
				},
				{
					Name:        "GetByName",
					Description: "根据名称获取工具",
					Params:      []string{"name string"},
					ReturnType:  "(*entities.Tool, error)",
					Implementation: `var entity entities.Tool
	err := r.BaseRepository.First(&entity, "name = ?", name)
	if err != nil {
		return nil, err
	}
	return &entity, nil`,
				},
			},
		},
		{
			EntityName:   "Model",
			PackageName:  "model",
			TableName:    "model",
			Fields:       "id, user_id, name, model, base_model_id, created_at, updated_at",
			SearchFields: []string{"name", "model"},
			BusinessMethods: []BusinessMethod{
				{
					Name:        "GetByUserID",
					Description: "根据用户ID获取模型列表",
					Params:      []string{"userID string", "offset, limit int"},
					ReturnType:  "([]*entities.Model, error)",
					Implementation: `var entities []*entities.Model
	err := r.BaseRepository.WhereWithSelectAndPagination(&entities, r.modelFields, "user_id = ?", offset, limit, userID)
	return entities, err`,
				},
				{
					Name:        "GetByName",
					Description: "根据名称获取模型",
					Params:      []string{"name string"},
					ReturnType:  "(*entities.Model, error)",
					Implementation: `var entity entities.Model
	err := r.BaseRepository.First(&entity, "name = ?", name)
	if err != nil {
		return nil, err
	}
	return &entity, nil`,
				},
				{
					Name:        "GetByBaseModelID",
					Description: "根据基础模型ID获取模型列表",
					Params:      []string{"baseModelID string", "offset, limit int"},
					ReturnType:  "([]*entities.Model, error)",
					Implementation: `var entities []*entities.Model
	err := r.BaseRepository.WhereWithSelectAndPagination(&entities, r.modelFields, "base_model_id = ?", offset, limit, baseModelID)
	return entities, err`,
				},
			},
		},
		{
			EntityName:   "Function",
			PackageName:  "function",
			TableName:    "function",
			Fields:       "id, user_id, name, type, content, created_at, updated_at",
			SearchFields: []string{"name", "type"},
			BusinessMethods: []BusinessMethod{
				{
					Name:        "GetByUserID",
					Description: "根据用户ID获取函数列表",
					Params:      []string{"userID string", "offset, limit int"},
					ReturnType:  "([]*entities.Function, error)",
					Implementation: `var entities []*entities.Function
	err := r.BaseRepository.WhereWithSelectAndPagination(&entities, r.functionFields, "user_id = ?", offset, limit, userID)
	return entities, err`,
				},
				{
					Name:        "GetByType",
					Description: "根据类型获取函数列表",
					Params:      []string{"funcType string", "offset, limit int"},
					ReturnType:  "([]*entities.Function, error)",
					Implementation: `var entities []*entities.Function
	err := r.BaseRepository.WhereWithSelectAndPagination(&entities, r.functionFields, "type = ?", offset, limit, funcType)
	return entities, err`,
				},
				{
					Name:        "GetByName",
					Description: "根据名称获取函数",
					Params:      []string{"name string"},
					ReturnType:  "(*entities.Function, error)",
					Implementation: `var entity entities.Function
	err := r.BaseRepository.First(&entity, "name = ?", name)
	if err != nil {
		return nil, err
	}
	return &entity, nil`,
				},
			},
		},
		{
			EntityName:   "Knowledge",
			PackageName:  "knowledge",
			TableName:    "knowledge",
			Fields:       "id, user_id, name, description, created_at, updated_at",
			SearchFields: []string{"name", "description"},
			BusinessMethods: []BusinessMethod{
				{
					Name:        "GetByUserID",
					Description: "根据用户ID获取知识库列表",
					Params:      []string{"userID string", "offset, limit int"},
					ReturnType:  "([]*entities.Knowledge, error)",
					Implementation: `var entities []*entities.Knowledge
	err := r.BaseRepository.WhereWithSelectAndPagination(&entities, r.knowledgeFields, "user_id = ?", offset, limit, userID)
	return entities, err`,
				},
				{
					Name:        "GetByName",
					Description: "根据名称获取知识库",
					Params:      []string{"name string"},
					ReturnType:  "(*entities.Knowledge, error)",
					Implementation: `var entity entities.Knowledge
	err := r.BaseRepository.First(&entity, "name = ?", name)
	if err != nil {
		return nil, err
	}
	return &entity, nil`,
				},
			},
		},
		{
			EntityName:   "Feedback",
			PackageName:  "feedback",
			TableName:    "feedback",
			Fields:       "id, user_id, version, type, content, created_at, updated_at",
			SearchFields: []string{"content"},
			BusinessMethods: []BusinessMethod{
				{
					Name:        "GetByUserID",
					Description: "根据用户ID获取反馈列表",
					Params:      []string{"userID string", "offset, limit int"},
					ReturnType:  "([]*entities.Feedback, error)",
					Implementation: `var entities []*entities.Feedback
	err := r.BaseRepository.WhereWithSelectAndPagination(&entities, r.feedbackFields, "user_id = ?", offset, limit, userID)
	return entities, err`,
				},
				{
					Name:        "GetByType",
					Description: "根据类型获取反馈列表",
					Params:      []string{"feedbackType string", "offset, limit int"},
					ReturnType:  "([]*entities.Feedback, error)",
					Implementation: `var entities []*entities.Feedback
	err := r.BaseRepository.WhereWithSelectAndPagination(&entities, r.feedbackFields, "type = ?", offset, limit, feedbackType)
	return entities, err`,
				},
				{
					Name:        "GetByVersion",
					Description: "根据版本获取反馈列表",
					Params:      []string{"version string", "offset, limit int"},
					ReturnType:  "([]*entities.Feedback, error)",
					Implementation: `var entities []*entities.Feedback
	err := r.BaseRepository.WhereWithSelectAndPagination(&entities, r.feedbackFields, "version = ?", offset, limit, version)
	return entities, err`,
				},
			},
		},
		{
			EntityName:   "Tag",
			PackageName:  "tag",
			TableName:    "tag",
			Fields:       "id, user_id, name, created_at",
			SearchFields: []string{"name"},
			BusinessMethods: []BusinessMethod{
				{
					Name:        "GetByUserID",
					Description: "根据用户ID获取标签列表",
					Params:      []string{"userID string", "offset, limit int"},
					ReturnType:  "([]*entities.Tag, error)",
					Implementation: `var entities []*entities.Tag
	err := r.BaseRepository.WhereWithSelectAndPagination(&entities, r.tagFields, "user_id = ?", offset, limit, userID)
	return entities, err`,
				},
				{
					Name:        "GetByName",
					Description: "根据名称获取标签",
					Params:      []string{"name string"},
					ReturnType:  "(*entities.Tag, error)",
					Implementation: `var entity entities.Tag
	err := r.BaseRepository.First(&entity, "name = ?", name)
	if err != nil {
		return nil, err
	}
	return &entity, nil`,
				},
			},
		},
		{
			EntityName:   "Memory",
			PackageName:  "memory",
			TableName:    "memory",
			Fields:       "id, user_id, content, updated_at, created_at",
			SearchFields: []string{"content"},
			BusinessMethods: []BusinessMethod{
				{
					Name:        "GetByUserID",
					Description: "根据用户ID获取记忆列表",
					Params:      []string{"userID string", "offset, limit int"},
					ReturnType:  "([]*entities.Memory, error)",
					Implementation: `var entities []*entities.Memory
	err := r.BaseRepository.WhereWithSelectAndPagination(&entities, r.memoryFields, "user_id = ?", offset, limit, userID)
	return entities, err`,
				},
			},
		},
		{
			EntityName:   "Config",
			PackageName:  "config",
			TableName:    "config",
			Fields:       "id, data, updated_at, created_at",
			SearchFields: []string{},
			BusinessMethods: []BusinessMethod{
				{
					Name:        "GetLatest",
					Description: "获取最新配置",
					Params:      []string{},
					ReturnType:  "(*entities.Config, error)",
					Implementation: `var entity entities.Config
	err := r.BaseRepository.GetDB().Order("updated_at DESC").First(&entity).Error
	if err != nil {
		return nil, err
	}
	return &entity, nil`,
				},
			},
		},
		{
			EntityName:   "Auth",
			PackageName:  "auth",
			TableName:    "auth",
			Fields:       "id, email, password, active, created_at",
			SearchFields: []string{"email"},
			BusinessMethods: []BusinessMethod{
				{
					Name:        "GetByEmail",
					Description: "根据邮箱获取认证信息",
					Params:      []string{"email string"},
					ReturnType:  "(*entities.Auth, error)",
					Implementation: `var entity entities.Auth
	err := r.BaseRepository.First(&entity, "email = ?", email)
	if err != nil {
		return nil, err
	}
	return &entity, nil`,
				},
				{
					Name:        "GetActiveAuth",
					Description: "获取活跃的认证列表",
					Params:      []string{"offset, limit int"},
					ReturnType:  "([]*entities.Auth, error)",
					Implementation: `var entities []*entities.Auth
	err := r.BaseRepository.WhereWithSelectAndPagination(&entities, r.authFields, "active = ?", offset, limit, true)
	return entities, err`,
				},
			},
		},
		{
			EntityName:   "ChannelMember",
			PackageName:  "channelMember",
			TableName:    "channel_member",
			Fields:       "id, channel_id, user_id, created_at",
			SearchFields: []string{},
			BusinessMethods: []BusinessMethod{
				{
					Name:        "GetByChannelID",
					Description: "根据频道ID获取成员列表",
					Params:      []string{"channelID string", "offset, limit int"},
					ReturnType:  "([]*entities.ChannelMember, error)",
					Implementation: `var entities []*entities.ChannelMember
	err := r.BaseRepository.WhereWithSelectAndPagination(&entities, r.channelMemberFields, "channel_id = ?", offset, limit, channelID)
	return entities, err`,
				},
				{
					Name:        "GetByUserID",
					Description: "根据用户ID获取频道成员关系列表",
					Params:      []string{"userID string", "offset, limit int"},
					ReturnType:  "([]*entities.ChannelMember, error)",
					Implementation: `var entities []*entities.ChannelMember
	err := r.BaseRepository.WhereWithSelectAndPagination(&entities, r.channelMemberFields, "user_id = ?", offset, limit, userID)
	return entities, err`,
				},
				{
					Name:        "IsMember",
					Description: "检查用户是否是频道成员",
					Params:      []string{"channelID, userID string"},
					ReturnType:  "(bool, error)",
					Implementation: `var count int64
	err := r.BaseRepository.GetDB().Model(&entities.ChannelMember{}).Where("channel_id = ? AND user_id = ?", channelID, userID).Count(&count).Error
	return count > 0, err`,
				},
			},
		},
		{
			EntityName:   "MessageReaction",
			PackageName:  "messageReaction",
			TableName:    "message_reaction",
			Fields:       "id, message_id, user_id, emoji, created_at",
			SearchFields: []string{},
			BusinessMethods: []BusinessMethod{
				{
					Name:        "GetByMessageID",
					Description: "根据消息ID获取反应列表",
					Params:      []string{"messageID string", "offset, limit int"},
					ReturnType:  "([]*entities.MessageReaction, error)",
					Implementation: `var entities []*entities.MessageReaction
	err := r.BaseRepository.WhereWithSelectAndPagination(&entities, r.messageReactionFields, "message_id = ?", offset, limit, messageID)
	return entities, err`,
				},
				{
					Name:        "GetByChatID",
					Description: "根据聊天ID获取反应列表（通过消息关联）",
					Params:      []string{"chatID string", "offset, limit int"},
					ReturnType:  "([]*entities.MessageReaction, error)",
					Implementation: `var entities []*entities.MessageReaction
	err := r.BaseRepository.GetDB().Select(r.messageReactionFields).
		Joins("JOIN message ON message_reaction.message_id = message.id").
		Where("message.chat_id = ?", chatID).
		Offset(offset).Limit(limit).Find(&entities).Error
	return entities, err`,
				},
				{
					Name:        "GetByUserID",
					Description: "根据用户ID获取反应列表",
					Params:      []string{"userID string", "offset, limit int"},
					ReturnType:  "([]*entities.MessageReaction, error)",
					Implementation: `var entities []*entities.MessageReaction
	err := r.BaseRepository.WhereWithSelectAndPagination(&entities, r.messageReactionFields, "user_id = ?", offset, limit, userID)
	return entities, err`,
				},
				{
					Name:        "GetByEmoji",
					Description: "根据表情获取反应列表",
					Params:      []string{"emoji string", "offset, limit int"},
					ReturnType:  "([]*entities.MessageReaction, error)",
					Implementation: `var entities []*entities.MessageReaction
	err := r.BaseRepository.WhereWithSelectAndPagination(&entities, r.messageReactionFields, "emoji = ?", offset, limit, emoji)
	return entities, err`,
				},
			},
		},
		{
			EntityName:   "Chatidtag",
			PackageName:  "chatidtag",
			TableName:    "chatidtag",
			Fields:       "id, tag_name, chat_id, user_id, created_at",
			SearchFields: []string{"tag_name"},
			BusinessMethods: []BusinessMethod{
				{
					Name:        "GetByChatID",
					Description: "根据聊天ID获取标签列表",
					Params:      []string{"chatID string", "offset, limit int"},
					ReturnType:  "([]*entities.Chatidtag, error)",
					Implementation: `var entities []*entities.Chatidtag
	err := r.BaseRepository.WhereWithSelectAndPagination(&entities, r.chatidtagFields, "chat_id = ?", offset, limit, chatID)
	return entities, err`,
				},
				{
					Name:        "GetByUserID",
					Description: "根据用户ID获取聊天标签列表",
					Params:      []string{"userID string", "offset, limit int"},
					ReturnType:  "([]*entities.Chatidtag, error)",
					Implementation: `var entities []*entities.Chatidtag
	err := r.BaseRepository.WhereWithSelectAndPagination(&entities, r.chatidtagFields, "user_id = ?", offset, limit, userID)
	return entities, err`,
				},
				{
					Name:        "GetByTagName",
					Description: "根据标签名获取聊天标签列表",
					Params:      []string{"tagName string", "offset, limit int"},
					ReturnType:  "([]*entities.Chatidtag, error)",
					Implementation: `var entities []*entities.Chatidtag
	err := r.BaseRepository.WhereWithSelectAndPagination(&entities, r.chatidtagFields, "tag_name = ?", offset, limit, tagName)
	return entities, err`,
				},
			},
		},
	}

	fmt.Println("🚀 开始生成剩余的 Repository 文件...")

	// 生成接口
	for _, repo := range repositories {
		fmt.Printf("🏗️  正在生成 %s Repository 接口...\n", repo.EntityName)
		err := generateInterface(repo)
		if err != nil {
			fmt.Printf("❌ 生成 %s Repository 接口失败: %v\n", repo.EntityName, err)
			continue
		}
		fmt.Printf("✅ %s Repository 接口生成成功\n", repo.EntityName)
	}

	// 生成实现
	for _, repo := range repositories {
		fmt.Printf("🏗️  正在生成 %s Repository 实现...\n", repo.EntityName)
		err := generateImplementation(repo)
		if err != nil {
			fmt.Printf("❌ 生成 %s Repository 实现失败: %v\n", repo.EntityName, err)
			continue
		}
		fmt.Printf("✅ %s Repository 实现生成成功\n", repo.EntityName)
	}

	fmt.Println("🎉 所有剩余的 Repository 文件生成完成！")
}

// generateInterface 生成 repository 接口
func generateInterface(repo RepositoryInfo) error {
	// 创建模板
	tmpl, err := template.New("interface").Parse(interfaceTemplate)
	if err != nil {
		return err
	}

	// 创建输出目录
	outputDir := "../internal/repository/interfaces"
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		return err
	}

	// 创建文件
	fileName := fmt.Sprintf("%s_repository.go", strings.ToLower(repo.EntityName))
	filePath := filepath.Join(outputDir, fileName)

	file, err := os.Create(filePath)
	if err != nil {
		return err
	}
	defer file.Close()

	// 执行模板
	return tmpl.Execute(file, repo)
}

// generateImplementation 生成 repository 实现
func generateImplementation(repo RepositoryInfo) error {
	// 创建模板
	tmpl, err := template.New("implementation").Parse(implementationTemplate)
	if err != nil {
		return err
	}

	// 创建输出目录
	outputDir := "../internal/repository"
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		return err
	}

	// 创建文件
	fileName := fmt.Sprintf("%s_repository.go", strings.ToLower(repo.EntityName))
	filePath := filepath.Join(outputDir, fileName)

	file, err := os.Create(filePath)
	if err != nil {
		return err
	}
	defer file.Close()

	// 执行模板
	return tmpl.Execute(file, repo)
}
