package main

import (
	"fmt"
	"os"
	"os/exec"
)

func main() {
	fmt.Println("🧹 清理构建文件...")

	// 要清理的文件和目录
	targets := []string{
		"bin/",
		"coverage.out",
		"coverage.html",
	}

	for _, target := range targets {
		if _, err := os.Stat(target); err == nil {
			if err := os.RemoveAll(target); err != nil {
				fmt.Printf("⚠️  删除 %s 失败: %v\n", target, err)
			} else {
				fmt.Printf("✅ 删除 %s\n", target)
			}
		}
	}

	// 清理Go测试缓存
	fmt.Println("清理Go测试缓存...")
	if err := exec.Command("go", "clean").Run(); err != nil {
		fmt.Printf("⚠️  清理测试缓存失败: %v\n", err)
	} else {
		fmt.Println("✅ 清理测试缓存完成")
	}

	fmt.Println("🎉 清理完成!")
}
