package main

import (
	"fmt"
	"log"
	"os"

	"hkchat_api/internal/config"
	"hkchat_api/internal/models"
	"hkchat_api/pkg/clients/database"

	"gorm.io/gorm"
)

func main() {
	fmt.Println("🗃️ 开始数据库重置...")

	// 初始化配置
	if err := config.InitConfig(); err != nil {
		log.Fatalf("❌ 配置初始化失败: %v", err)
	}

	// 获取数据库配置
	dbConfig := config.GetDatabaseConfig()
	if dbConfig == nil {
		log.Fatal("❌ 数据库配置获取失败")
	}

	// 初始化数据库管理器
	if err := database.InitManager(dbConfig); err != nil {
		log.Fatalf("❌ 数据库初始化失败: %v", err)
	}

	db := database.GetGlobalDB()
	if db == nil {
		log.Fatal("❌ 无法获取数据库连接")
	}

	// 询问用户确认
	fmt.Print("⚠️  这将删除所有现有表并重新创建。确认继续? (y/N): ")
	var input string
	fmt.Scanln(&input)

	if input != "y" && input != "Y" {
		fmt.Println("❌ 操作已取消")
		os.Exit(0)
	}

	// 删除现有表
	if err := dropAllTables(db); err != nil {
		log.Fatalf("❌ 删除表失败: %v", err)
	}

	// 重新创建表
	allModels := models.GetAllModels()
	if err := database.AutoMigrateGlobal(allModels...); err != nil {
		log.Fatalf("❌ 数据库表结构同步失败: %v", err)
	}

	fmt.Printf("✅ 数据库重置完成，共创建了 %d 个表\n", len(allModels))

	// 列出创建的表
	modelNames := models.GetModelNames()
	fmt.Println("🗃️  已创建的数据表:")
	for _, name := range modelNames {
		fmt.Printf("   - %s\n", name)
	}

	// 关闭数据库连接
	if err := database.CloseGlobal(); err != nil {
		log.Printf("⚠️ 数据库关闭失败: %v", err)
	}

	fmt.Println("🎉 数据库重置成功完成！")
}

// dropAllTables 删除所有表
func dropAllTables(db *gorm.DB) error {
	fmt.Println("🗑️  删除现有表...")

	// 要删除的表名列表（包括可能存在的旧表）
	tables := []string{
		"users", // domain.User创建的表
		"user",  // entities.User创建的表
		"auth",
		"chat",
		"message",
		"channel",
		"document",
		"model",
		"tag",
		"memory",
		"groups", // 可能的复数形式
		"group",
		"file",
		"files",
		"note",
		"notes",
		"folder",
		"folders",
		"knowledge",
		"knowledges",
		"channel_member",
		"chatidtag",
		"message_reaction",
	}

	for _, table := range tables {
		// 检查表是否存在
		if db.Migrator().HasTable(table) {
			fmt.Printf("   删除表: %s\n", table)
			if err := db.Migrator().DropTable(table); err != nil {
				fmt.Printf("   ⚠️ 删除表 %s 失败: %v\n", table, err)
				// 继续处理其他表，不终止程序
			}
		}
	}

	fmt.Println("✅ 表删除完成")
	return nil
}
