definitions:
  api.ChatCompletionV2Request:
    properties:
      conversation_id:
        type: string
      model_id:
        type: string
      query:
        type: string
    required:
    - model_id
    - query
    type: object
  response.ErrorResponse:
    properties:
      code:
        type: string
      details:
        type: string
      error:
        type: string
      message:
        type: string
      success:
        type: boolean
    type: object
info:
  contact: {}
paths:
  /chat_v2/completions:
    post:
      consumes:
      - application/json
      description: 支持流式响应的聊天完成接口，使用SSE格式返回结果
      parameters:
      - description: 聊天完成请求
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/api.ChatCompletionV2Request'
      produces:
      - text/event-stream
      responses:
        "200":
          description: 流式响应事件
          schema:
            type: string
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/response.ErrorResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/response.ErrorResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/response.ErrorResponse'
      summary: V2版本聊天完成接口
      tags:
      - 聊天
swagger: "2.0"
