<mxfile host="app.diagrams.net" modified="2025-01-12T12:00:00.000Z" agent="5.0" etag="xxx" version="24.7.17">
  <diagram name="HKChat-API-Detailed-Architecture" id="hkchat-api-detailed-arch">
    <mxGraphModel dx="1800" dy="1200" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1600" pageHeight="1200" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 标题 -->
        <mxCell id="title" value="HKChat API 详细系统架构流程图" style="text;strokeColor=none;fillColor=none;html=1;fontSize=24;fontStyle=1;verticalAlign=middle;align=center;" vertex="1" parent="1">
          <mxGeometry x="700" y="20" width="400" height="40" as="geometry" />
        </mxCell>
        
        <!-- 客户端层 -->
        <mxCell id="client-layer-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;strokeWidth=2;opacity=20;" vertex="1" parent="1">
          <mxGeometry x="50" y="80" width="300" height="100" as="geometry" />
        </mxCell>
        
        <mxCell id="client-layer-title" value="客户端层" style="text;strokeColor=none;fillColor=none;html=1;fontSize=16;fontStyle=1;verticalAlign=middle;align=center;" vertex="1" parent="1">
          <mxGeometry x="50" y="85" width="100" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="web-client" value="Web客户端" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="70" y="120" width="70" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="mobile-client" value="移动客户端" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="160" y="120" width="70" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="api-client" value="API客户端" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="250" y="120" width="70" height="40" as="geometry" />
        </mxCell>
        
        <!-- API网关层 -->
        <mxCell id="api-gateway-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;strokeWidth=2;opacity=20;" vertex="1" parent="1">
          <mxGeometry x="400" y="80" width="400" height="200" as="geometry" />
        </mxCell>
        
        <mxCell id="api-gateway-title" value="API网关层" style="text;strokeColor=none;fillColor=none;html=1;fontSize=16;fontStyle=1;verticalAlign=middle;align=center;" vertex="1" parent="1">
          <mxGeometry x="400" y="85" width="100" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="gin-router" value="Gin Router" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="420" y="120" width="100" height="40" as="geometry" />
        </mxCell>
        
        <!-- 中间件层 -->
        <mxCell id="middleware-bg" value="中间件层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="540" y="110" width="240" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="auth-middleware" value="认证中间件<br/>auth_middleware.go" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="550" y="140" width="90" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="cors-middleware" value="CORS中间件" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="650" y="140" width="60" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="log-middleware" value="日志中间件" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="720" y="140" width="60" height="40" as="geometry" />
        </mxCell>
        
        <!-- 路由层 -->
        <mxCell id="routes-bg" value="路由层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="420" y="190" width="360" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="chat-routes" value="聊天API路由<br/>chat_api_routes.go" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="430" y="220" width="100" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="prompt-routes" value="提示词路由<br/>prompt_config_routes.go" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="550" y="220" width="100" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="api-registry" value="路由注册器<br/>registry.go" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="670" y="220" width="100" height="40" as="geometry" />
        </mxCell>
        
        <!-- 业务服务层 -->
        <mxCell id="service-layer-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;strokeWidth=2;opacity=20;" vertex="1" parent="1">
          <mxGeometry x="50" y="320" width="750" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="service-layer-title" value="业务服务层" style="text;strokeColor=none;fillColor=none;html=1;fontSize=16;fontStyle=1;verticalAlign=middle;align=center;" vertex="1" parent="1">
          <mxGeometry x="50" y="325" width="120" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="chat-service" value="聊天服务<br/>chat_service.go" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="80" y="370" width="100" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="config-service" value="配置服务<br/>config/" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="200" y="370" width="100" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="prompt-service" value="提示词服务<br/>prompt_service.go" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="320" y="370" width="100" height="50" as="geometry" />
        </mxCell>
        
        <!-- 核心框架层 -->
        <mxCell id="core-framework-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;strokeWidth=2;opacity=20;" vertex="1" parent="1">
          <mxGeometry x="850" y="80" width="300" height="360" as="geometry" />
        </mxCell>
        
        <mxCell id="core-framework-title" value="核心框架层" style="text;strokeColor=none;fillColor=none;html=1;fontSize=16;fontStyle=1;verticalAlign=middle;align=center;" vertex="1" parent="1">
          <mxGeometry x="850" y="85" width="120" height="30" as="geometry" />
        </mxCell>
        
        <!-- 依赖注入容器 -->
        <mxCell id="di-container-bg" value="依赖注入容器" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="870" y="120" width="260" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="di-core" value="DI容器<br/>di/container.go" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="880" y="150" width="80" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="di-registry" value="DI注册器<br/>di/registry.go" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="970" y="150" width="80" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="base-component" value="基础组件<br/>base/component.go" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="1060" y="150" width="80" height="40" as="geometry" />
        </mxCell>
        
        <!-- 工厂模式 -->
        <mxCell id="factories-bg" value="工厂模式" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="870" y="200" width="260" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="msg-factory" value="消息工厂<br/>message_factory.go" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="880" y="230" width="100" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="user-factory" value="用户工厂<br/>user_factory.go" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="1000" y="230" width="100" height="40" as="geometry" />
        </mxCell>
        
        <!-- 接口层 -->
        <mxCell id="interfaces-bg" value="接口层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="870" y="280" width="260" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="msg-interface" value="消息接口<br/>interfaces/message.go" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="880" y="310" width="100" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="user-interface" value="用户接口<br/>interfaces/user.go" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="1000" y="310" width="100" height="40" as="geometry" />
        </mxCell>
        
        <!-- LLM集成层 -->
        <mxCell id="llm-integration-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;strokeWidth=2;opacity=20;" vertex="1" parent="1">
          <mxGeometry x="450" y="320" width="350" height="240" as="geometry" />
        </mxCell>
        
        <mxCell id="llm-integration-title" value="LLM集成层" style="text;strokeColor=none;fillColor=none;html=1;fontSize=16;fontStyle=1;verticalAlign=middle;align=center;" vertex="1" parent="1">
          <mxGeometry x="450" y="325" width="120" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="llm-gateway" value="LLM网关<br/>llm/gateway.go" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="470" y="370" width="100" height="50" as="geometry" />
        </mxCell>
        
        <!-- 函数调用 -->
        <mxCell id="function-calling-bg" value="函数调用" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=11;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="590" y="355" width="180" height="15" as="geometry" />
        </mxCell>
        
        <mxCell id="fc-executor" value="执行器<br/>executor.go" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="600" y="375" width="50" height="35" as="geometry" />
        </mxCell>
        
        <mxCell id="fc-handler" value="处理器<br/>handler.go" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="660" y="375" width="50" height="35" as="geometry" />
        </mxCell>
        
        <mxCell id="fc-validator" value="验证器<br/>validator.go" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="720" y="375" width="50" height="35" as="geometry" />
        </mxCell>
        
        <!-- 网络搜索 -->
        <mxCell id="web-search-bg" value="网络搜索" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=11;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="590" y="425" width="180" height="15" as="geometry" />
        </mxCell>
        
        <mxCell id="ws-detector" value="检测器<br/>detector.go" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="600" y="445" width="50" height="35" as="geometry" />
        </mxCell>
        
        <mxCell id="ws-service" value="搜索服务<br/>service.go" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="660" y="445" width="50" height="35" as="geometry" />
        </mxCell>
        
        <!-- 检索服务 -->
        <mxCell id="retrieval-bg" value="检索服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=11;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="590" y="495" width="180" height="15" as="geometry" />
        </mxCell>
        
        <mxCell id="query-gen" value="查询生成<br/>query_generator.go" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="600" y="515" width="60" height="35" as="geometry" />
        </mxCell>
        
        <mxCell id="search-mgr" value="搜索管理<br/>search_manager.go" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="670" y="515" width="60" height="35" as="geometry" />
        </mxCell>
        
        <!-- 其他组件 -->
        <mxCell id="content-filter" value="内容过滤<br/>content_filter.go" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="470" y="440" width="80" height="35" as="geometry" />
        </mxCell>
        
        <mxCell id="msg-utils" value="消息工具<br/>message_utils.go" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="470" y="485" width="80" height="35" as="geometry" />
        </mxCell>
        
        <!-- 数据访问层 -->
        <mxCell id="repository-layer-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f0f0;strokeColor=#666666;strokeWidth=2;opacity=20;" vertex="1" parent="1">
          <mxGeometry x="50" y="580" width="750" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="repository-layer-title" value="数据访问层 (Repository)" style="text;strokeColor=none;fillColor=none;html=1;fontSize=16;fontStyle=1;verticalAlign=middle;align=center;" vertex="1" parent="1">
          <mxGeometry x="50" y="585" width="200" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="base-repo" value="基础仓库<br/>base_repository.go" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f0f0;strokeColor=#666666;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="70" y="625" width="80" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="chat-repo" value="聊天仓库<br/>chat_repository.go" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="160" y="625" width="80" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="user-repo" value="用户仓库<br/>user_repository.go" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="250" y="625" width="80" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="model-repo" value="模型仓库<br/>model_repository.go" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="340" y="625" width="80" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="config-repo" value="配置仓库<br/>config_repository.go" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="430" y="625" width="80" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="memory-repo" value="记忆仓库<br/>memory_repository.go" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="520" y="625" width="80" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="msg-repo" value="消息仓库<br/>message_repository.go" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="610" y="625" width="80" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="tool-repo" value="工具仓库<br/>tool_repository.go" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="700" y="625" width="80" height="40" as="geometry" />
        </mxCell>
        
        <!-- 客户端层 -->
        <mxCell id="client-layer-bg2" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;strokeWidth=2;opacity=20;" vertex="1" parent="1">
          <mxGeometry x="850" y="580" width="300" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="client-layer-title2" value="客户端层" style="text;strokeColor=none;fillColor=none;html=1;fontSize=16;fontStyle=1;verticalAlign=middle;align=center;" vertex="1" parent="1">
          <mxGeometry x="850" y="585" width="100" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="db-client" value="数据库客户端<br/>database/db.go" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="870" y="625" width="80" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="openai-client" value="OpenAI客户端<br/>openai_client.go" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="960" y="625" width="80" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="tavily-client" value="Tavily客户端<br/>tavily_client.go" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="1050" y="625" width="80" height="40" as="geometry" />
        </mxCell>
        
        <!-- 数据存储层 -->
        <mxCell id="storage-layer-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;strokeWidth=2;opacity=20;" vertex="1" parent="1">
          <mxGeometry x="50" y="750" width="300" height="100" as="geometry" />
        </mxCell>
        
        <mxCell id="storage-layer-title" value="数据存储层" style="text;strokeColor=none;fillColor=none;html=1;fontSize=16;fontStyle=1;verticalAlign=middle;align=center;" vertex="1" parent="1">
          <mxGeometry x="50" y="755" width="120" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="postgresql" value="PostgreSQL<br/>主数据库" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="80" y="795" width="100" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="redis" value="Redis<br/>缓存数据库" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="200" y="795" width="100" height="40" as="geometry" />
        </mxCell>
        
        <!-- 外部服务层 -->
        <mxCell id="external-services-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;strokeWidth=2;opacity=20;" vertex="1" parent="1">
          <mxGeometry x="400" y="750" width="400" height="100" as="geometry" />
        </mxCell>
        
        <mxCell id="external-services-title" value="外部服务层" style="text;strokeColor=none;fillColor=none;html=1;fontSize=16;fontStyle=1;verticalAlign=middle;align=center;" vertex="1" parent="1">
          <mxGeometry x="400" y="755" width="120" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="openai-api" value="OpenAI API" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="430" y="795" width="80" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="tavily-api" value="Tavily 搜索API" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="530" y="795" width="80" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="local-llm" value="本地LLM模型" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="630" y="795" width="80" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="pipeline-api" value="Pipeline API" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="730" y="795" width="80" height="40" as="geometry" />
        </mxCell>
        
        <!-- 配置层 -->
        <mxCell id="config-layer-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f0f0;strokeColor=#666666;strokeWidth=2;opacity=20;" vertex="1" parent="1">
          <mxGeometry x="850" y="750" width="300" height="100" as="geometry" />
        </mxCell>
        
        <mxCell id="config-layer-title" value="配置层" style="text;strokeColor=none;fillColor=none;html=1;fontSize=16;fontStyle=1;verticalAlign=middle;align=center;" vertex="1" parent="1">
          <mxGeometry x="850" y="755" width="100" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="config-manager" value="配置管理器<br/>config/manager.go" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f0f0;strokeColor=#666666;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="870" y="795" width="80" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="app-config" value="应用配置<br/>app_config.go" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f0f0;strokeColor=#666666;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="960" y="795" width="80" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="db-config" value="数据库配置<br/>db_config.go" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f0f0;strokeColor=#666666;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="1050" y="795" width="80" height="40" as="geometry" />
        </mxCell>
        
        <!-- 主要数据流连接线 -->
        <!-- 客户端到API网关 -->
        <mxCell id="edge1" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#2E7D32;" edge="1" parent="1" source="web-client" target="gin-router">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="400" as="sourcePoint" />
            <mxPoint x="450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge2" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#2E7D32;" edge="1" parent="1" source="mobile-client" target="gin-router">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="400" as="sourcePoint" />
            <mxPoint x="450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge3" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#2E7D32;" edge="1" parent="1" source="api-client" target="gin-router">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="400" as="sourcePoint" />
            <mxPoint x="450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 路由到中间件 -->
        <mxCell id="edge4" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#1976D2;" edge="1" parent="1" source="gin-router" target="auth-middleware">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="400" as="sourcePoint" />
            <mxPoint x="450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 中间件链 -->
        <mxCell id="edge5" style="endArrow=classic;html=1;rounded=0;strokeWidth=1;strokeColor=#666666;" edge="1" parent="1" source="auth-middleware" target="cors-middleware">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="400" as="sourcePoint" />
            <mxPoint x="450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge6" style="endArrow=classic;html=1;rounded=0;strokeWidth=1;strokeColor=#666666;" edge="1" parent="1" source="cors-middleware" target="log-middleware">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="400" as="sourcePoint" />
            <mxPoint x="450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 中间件到路由 -->
        <mxCell id="edge7" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#1976D2;" edge="1" parent="1" source="log-middleware" target="chat-routes">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="400" as="sourcePoint" />
            <mxPoint x="450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 路由到服务 -->
        <mxCell id="edge8" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#1976D2;" edge="1" parent="1" source="chat-routes" target="chat-service">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="400" as="sourcePoint" />
            <mxPoint x="450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge9" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#1976D2;" edge="1" parent="1" source="prompt-routes" target="prompt-service">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="400" as="sourcePoint" />
            <mxPoint x="450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 服务到LLM网关 -->
        <mxCell id="edge10" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#F57C00;" edge="1" parent="1" source="chat-service" target="llm-gateway">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="400" as="sourcePoint" />
            <mxPoint x="450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- LLM网关到各功能模块 -->
        <mxCell id="edge11" style="endArrow=classic;html=1;rounded=0;strokeWidth=1;strokeColor=#F57C00;" edge="1" parent="1" source="llm-gateway" target="fc-executor">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="400" as="sourcePoint" />
            <mxPoint x="450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge12" style="endArrow=classic;html=1;rounded=0;strokeWidth=1;strokeColor=#F57C00;" edge="1" parent="1" source="llm-gateway" target="ws-detector">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="400" as="sourcePoint" />
            <mxPoint x="450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge13" style="endArrow=classic;html=1;rounded=0;strokeWidth=1;strokeColor=#F57C00;" edge="1" parent="1" source="llm-gateway" target="query-gen">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="400" as="sourcePoint" />
            <mxPoint x="450" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 服务到仓库 -->
        <mxCell id="edge14" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#7B1FA2;" edge="1" parent="1" source="chat-service" target="chat-repo">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="500" as="sourcePoint" />
            <mxPoint x="450" y="450" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge15" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#7B1FA2;" edge="1" parent="1" source="chat-service" target="user-repo">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="500" as="sourcePoint" />
            <mxPoint x="450" y="450" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 仓库到数据库客户端 -->
        <mxCell id="edge16" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#7B1FA2;" edge="1" parent="1" source="chat-repo" target="db-client">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="700" as="sourcePoint" />
            <mxPoint x="450" y="650" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge17" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#7B1FA2;" edge="1" parent="1" source="user-repo" target="db-client">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="700" as="sourcePoint" />
            <mxPoint x="450" y="650" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 数据库客户端到数据库 -->
        <mxCell id="edge18" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#388E3C;" edge="1" parent="1" source="db-client" target="postgresql">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="700" as="sourcePoint" />
            <mxPoint x="450" y="650" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge19" style="endArrow=classic;html=1;rounded=0;strokeWidth=1;strokeColor=#388E3C;" edge="1" parent="1" source="db-client" target="redis">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="700" as="sourcePoint" />
            <mxPoint x="450" y="650" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- LLM客户端到外部服务 -->
        <mxCell id="edge20" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#FF5722;" edge="1" parent="1" source="openai-client" target="openai-api">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="600" y="700" as="sourcePoint" />
            <mxPoint x="650" y="650" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge21" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#FF5722;" edge="1" parent="1" source="tavily-client" target="tavily-api">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="600" y="700" as="sourcePoint" />
            <mxPoint x="650" y="650" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 依赖注入关系 (虚线) -->
        <mxCell id="edge22" style="endArrow=classic;html=1;rounded=0;dashed=1;dashPattern=5 5;strokeColor=#9E9E9E;" edge="1" parent="1" source="di-core" target="chat-service">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="600" y="450" as="sourcePoint" />
            <mxPoint x="650" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="edge23" style="endArrow=classic;html=1;rounded=0;dashed=1;dashPattern=5 5;strokeColor=#9E9E9E;" edge="1" parent="1" source="di-core" target="llm-gateway">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="600" y="450" as="sourcePoint" />
            <mxPoint x="650" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 图例 -->
        <mxCell id="legend-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="1200" y="100" width="180" height="200" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-title" value="图例说明" style="text;strokeColor=none;fillColor=none;html=1;fontSize=14;fontStyle=1;verticalAlign=middle;align=center;" vertex="1" parent="1">
          <mxGeometry x="1200" y="105" width="180" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="legend1" value="绿色箭头: 客户端请求" style="text;strokeColor=none;fillColor=none;html=1;fontSize=10;verticalAlign=middle;align=left;strokeColor=#2E7D32;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1210" y="135" width="160" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="legend2" value="蓝色箭头: 内部数据流" style="text;strokeColor=none;fillColor=none;html=1;fontSize=10;verticalAlign=middle;align=left;" vertex="1" parent="1">
          <mxGeometry x="1210" y="155" width="160" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="legend3" value="橙色箭头: LLM处理流程" style="text;strokeColor=none;fillColor=none;html=1;fontSize=10;verticalAlign=middle;align=left;" vertex="1" parent="1">
          <mxGeometry x="1210" y="175" width="160" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="legend4" value="紫色箭头: 数据存储流程" style="text;strokeColor=none;fillColor=none;html=1;fontSize=10;verticalAlign=middle;align=left;" vertex="1" parent="1">
          <mxGeometry x="1210" y="195" width="160" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="legend5" value="红色箭头: 外部服务调用" style="text;strokeColor=none;fillColor=none;html=1;fontSize=10;verticalAlign=middle;align=left;" vertex="1" parent="1">
          <mxGeometry x="1210" y="215" width="160" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="legend6" value="灰色虚线: 依赖注入" style="text;strokeColor=none;fillColor=none;html=1;fontSize=10;verticalAlign=middle;align=left;" vertex="1" parent="1">
          <mxGeometry x="1210" y="235" width="160" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="legend7" value="框颜色代表不同架构层" style="text;strokeColor=none;fillColor=none;html=1;fontSize=10;verticalAlign=middle;align=left;" vertex="1" parent="1">
          <mxGeometry x="1210" y="255" width="160" height="20" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>