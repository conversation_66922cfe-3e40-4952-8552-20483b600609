# HKChat API 系统架构文档

## 概述

HKChat API 是一个基于 Go 语言开发的智能聊天后端服务，采用现代化的分层架构设计，支持多种 LLM 模型集成、实时聊天、用户管理、文档检索等功能。

## 架构设计原则

- **分层架构**: 清晰的分层设计，职责分离
- **依赖注入**: 使用自定义 DI 容器管理依赖关系
- **接口导向**: 通过接口实现解耦
- **配置驱动**: 灵活的配置管理系统
- **模块化**: 高内聚、低耦合的模块设计

## 目录结构

```
hkchat_api/
├── api/                    # API 路由层
│   ├── chat_api_routes.go
│   ├── middleware/
│   └── registry.go
├── cmd/                    # 应用入口
│   └── server/
├── config/                 # 配置文件
├── deploy/                 # 部署配置
├── docs/                   # 文档和 API 规范
├── internal/              # 内部模块
│   ├── config/            # 配置管理
│   ├── core/              # 核心组件
│   │   ├── di/            # 依赖注入
│   │   ├── factories/     # 工厂模式
│   │   ├── interfaces/    # 业务接口
│   │   └── llm/           # LLM 集成
│   ├── models/            # 数据模型
│   │   ├── entities/      # 数据库实体
│   │   ├── dto/           # 数据传输对象
│   │   └── domain/        # 领域模型
│   ├── repository/        # 数据访问层
│   └── services/          # 业务服务层
├── pkg/                   # 公共包
│   ├── auth/              # 认证相关
│   ├── clients/           # 外部客户端
│   ├── prompts/           # 提示词模板
│   └── utils/             # 工具函数
└── scripts/               # 脚本工具
```

## 核心架构组件

### 1. API 层 (`api/`)
- **职责**: HTTP 接口处理、路由注册、中间件管理
- **主要组件**:
  - `ChatApiRoutes`: 聊天相关 API 路由
  - `PromptConfigRoutes`: 提示词配置路由
  - `ApiRegistry`: 统一路由注册器

### 2. 业务服务层 (`internal/services/`)
- **职责**: 核心业务逻辑处理
- **主要服务**:
  - `ChatService`: 聊天服务，处理消息流、LLM 调用

### 3. 数据访问层 (`internal/repository/`)
- **职责**: 数据库操作抽象
- **主要组件**:
  - `ChatRepository`: 聊天数据管理
  - `UserRepository`: 用户数据管理
  - `ModelRepository`: 模型配置管理

### 4. 核心框架 (`internal/core/`)
- **依赖注入** (`di/`): 自定义 DI 容器
- **LLM 集成** (`llm/`): 多 LLM 模型支持
- **工厂模式** (`factories/`): 对象创建管理
- **接口定义** (`interfaces/`): 业务接口规范

### 5. 配置管理 (`internal/config/`)
- **职责**: 配置文件解析、环境变量管理
- **特性**: 支持多环境配置、动态配置更新

## 关键设计模式

### 依赖注入容器
```go
type Container struct {
    coreComponents map[string]*base.BaseCoreComponent
    coreServices   map[string]*base.BaseCoreService
    repositories   map[string]interface{}
    clients        map[string]interface{}
}
```

### LLM 网关模式
- 统一的 LLM 接口抽象
- 支持多种模型提供商（OpenAI、本地模型等）
- 智能路由和负载均衡

### 仓储模式
- 统一的数据访问接口
- 数据库操作抽象
- 支持事务管理

## 数据流架构

详细的数据流程图请参考: [系统架构流程图](./system-architecture-flow.drawio.xml)

## 技术栈

- **Web 框架**: Gin
- **ORM**: GORM
- **数据库**: PostgreSQL
- **缓存**: Redis (可选)
- **文档**: Swagger
- **容器化**: Docker
- **部署**: Kubernetes

## 扩展性设计

### 水平扩展
- 无状态服务设计
- 数据库连接池
- 缓存分层

### 功能扩展
- 插件化架构
- 接口驱动开发
- 配置化功能开关

## 监控和运维

- 健康检查端点
- 结构化日志
- 性能指标收集
- 错误追踪

## 安全设计

- JWT 认证
- API 访问控制
- 输入验证
- 安全头设置

---

更多详细信息请查看各个子模块的具体文档。