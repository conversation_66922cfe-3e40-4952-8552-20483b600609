# 依赖注入架构文档

## 概述

HKChat API 采用自定义的依赖注入(DI)容器来管理组件之间的依赖关系，实现松耦合的架构设计。

## DI 容器架构

### 核心组件

```go
type Container struct {
    coreComponents map[string]*base.BaseCoreComponent  // 核心组件注册表
    coreServices   map[string]*base.BaseCoreService    // 业务服务注册表  
    repositories   map[string]interface{}             // Repository注册表
    clients        map[string]interface{}             // 客户端注册表
    di             *DI                                // DI实例
}
```

### 组件分层

1. **核心组件 (CoreComponents)**
   - 基础设施组件
   - 配置管理器
   - 数据库连接管理

2. **核心服务 (CoreServices)**
   - 业务逻辑服务
   - 聊天服务
   - 配置服务

3. **仓储层 (Repositories)**
   - 数据访问对象
   - 数据库操作抽象

4. **客户端 (Clients)**
   - 外部服务客户端
   - LLM 客户端
   - 搜索客户端

## 初始化流程

### 1. 容器创建
```go
container := di.NewContainer()
// 或者
container := di.NewContainerWithDB(db)
```

### 2. 组件注册
```go
// 注册核心组件
container.RegisterCoreComponent("ConfigManager", configComponent)

// 注册仓储
container.RegisterRepository("ChatRepository", chatRepo)

// 注册服务
container.RegisterCoreService("ChatService", chatService)

// 注册客户端
container.RegisterClient("LLMClient", llmClient)
```

### 3. 容器初始化
```go
err := container.Initialize()
```

### 4. 依赖解析
```go
// 获取组件
component, err := container.GetCoreComponent("ConfigManager")

// 获取服务
service, err := container.GetCoreService("ChatService")

// 获取仓储
repo, err := container.GetRepository("ChatRepository")
```

## 依赖注入模式

### 构造函数注入
```go
// ChatService 通过构造函数接收依赖
func NewChatService(
    llmGateway llm.Gateway,
    chatRepo *repository.ChatRepository,
    modelRepo *repository.ModelRepository,
    configManager *config.Manager,
) *ChatService {
    return &ChatService{
        llmGateway:    llmGateway,
        chatRepo:      chatRepo,
        modelRepo:     modelRepo,
        configManager: configManager,
    }
}
```

### 接口注入
```go
type ChatService struct {
    llmGateway    llm.Gateway              // 接口类型
    chatRepo      interfaces.ChatRepository // 接口类型
    modelRepo     interfaces.ModelRepository
    configManager *config.Manager
}
```

## 生命周期管理

### 组件生命周期

1. **创建阶段**
   - 组件实例化
   - 依赖注入

2. **初始化阶段**
   - `Initialize()` 方法调用
   - 资源准备
   - 连接建立

3. **运行阶段**
   - 正常服务提供
   - 健康检查

4. **关闭阶段**
   - `Shutdown()` 方法调用
   - 资源清理
   - 连接关闭

### 健康检查
```go
// 检查所有组件健康状态
err := container.HealthCheck(ctx)

// 检查单个组件
err := component.HealthCheck(ctx)
```

### 优雅关闭
```go
// 关闭所有组件
err := container.Shutdown(ctx)
```

## 配置管理集成

### ConfigManager 组件
```go
type ConfigManagerComponent struct {
    configManager *config.Manager
    initialized   bool
}

func (c *ConfigManagerComponent) Initialize() error {
    // 初始化配置管理器
    return c.configManager.Initialize()
}
```

### 配置注入
```go
// 服务中使用配置
func (s *ChatService) GetModelConfig(modelName string) (*ModelConfig, error) {
    return s.configManager.GetModelConfig(modelName)
}
```

## LLM 网关集成

### 全局 LLM 网关
```go
// 获取全局 LLM 网关
llmGateway := di.GetGlobalLLMGateway()

// 在服务中使用
chatService := services.NewChatService(llmGateway, chatRepo, modelRepo, configManager)
```

### 多模型支持
```go
type LLMGateway interface {
    Chat(ctx context.Context, request *ChatRequest) (*ChatResponse, error)
    GetAvailableModels() []string
    SelectModel(modelName string) error
}
```

## 错误处理

### 依赖缺失处理
```go
if chatRepo == nil {
    utils.Warn("ChatRepository 缺失，聊天功能将不可用")
    return nil, errors.New("required dependency missing")
}
```

### 初始化失败处理
```go
if err := component.Initialize(); err != nil {
    utils.Errorf("组件 %s 初始化失败: %v", name, err)
    return err
}
```

## 调试和监控

### 容器状态查询
```go
status := container.GetContainerStatus()
// 返回: {
//   "initialized": true,
//   "core_components": 3,
//   "core_services": 2,
//   "repositories": 5,
//   "clients": 2
// }
```

### 组件列表
```go
components := container.ListComponents()
// 返回所有已注册组件的名称列表
```

### 调试日志
```go
utils.Debugf("🔧 注册核心组件: %s", name)
utils.Debugf("🎯 注册核心服务: %s", name)
utils.Debugf("🗄️ 注册Repository: %s", name)
utils.Debugf("🔌 注册客户端: %s", name)
```

## 最佳实践

### 1. 接口优先
- 定义清晰的接口
- 依赖接口而非具体实现
- 便于测试和替换

### 2. 单一职责
- 每个组件职责明确
- 避免循环依赖
- 保持组件的轻量性

### 3. 配置外部化
- 通过配置管理器注入配置
- 支持多环境配置
- 运行时配置更新

### 4. 错误处理
- 优雅降级
- 详细的错误日志
- 健康检查机制

### 5. 测试友好
- 支持模拟对象注入
- 独立的测试环境
- 组件隔离测试

## 扩展性

### 添加新组件
1. 实现相应接口
2. 注册到容器
3. 在需要的地方注入

### 添加新服务
1. 定义服务接口
2. 实现具体服务
3. 通过 DI 容器管理依赖

### 插件化支持
- 动态组件加载
- 插件生命周期管理
- 配置驱动的插件启用