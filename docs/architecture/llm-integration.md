# LLM 集成架构文档

## 概述

HKChat API 通过 LLM 网关模式实现多种大语言模型的统一集成，支持 OpenAI、本地模型等多种 LLM 提供商。

## LLM 网关架构

### 核心接口设计

```go
type Gateway interface {
    // 聊天接口
    Chat(ctx context.Context, request *ChatRequest) (*ChatResponse, error)
    
    // 流式聊天接口  
    ChatStream(ctx context.Context, request *ChatRequest) (<-chan *ChatResponse, error)
    
    // 获取可用模型
    GetAvailableModels() []string
    
    // 选择模型
    SelectModel(modelName string) error
    
    // 健康检查
    HealthCheck(ctx context.Context) error
}
```

### 请求响应结构

```go
type ChatRequest struct {
    Messages    []Message          `json:"messages"`
    Model       string            `json:"model"`
    Temperature float32           `json:"temperature,omitempty"`
    MaxTokens   int              `json:"max_tokens,omitempty"`
    Stream      bool             `json:"stream,omitempty"`
    Functions   []Function       `json:"functions,omitempty"`
    SystemPrompt string          `json:"system_prompt,omitempty"`
}

type ChatResponse struct {
    ID      string    `json:"id"`
    Object  string    `json:"object"`
    Created int64     `json:"created"`
    Model   string    `json:"model"`
    Choices []Choice  `json:"choices"`
    Usage   Usage     `json:"usage"`
    Error   *Error    `json:"error,omitempty"`
}
```

## 模型提供商支持

### OpenAI 集成

```go
type OpenAIClient struct {
    apiKey     string
    baseURL    string
    httpClient *http.Client
    models     []string
}

func (c *OpenAIClient) Chat(ctx context.Context, request *ChatRequest) (*ChatResponse, error) {
    // 构建 OpenAI API 请求
    openaiReq := c.buildOpenAIRequest(request)
    
    // 发送请求
    resp, err := c.sendRequest(ctx, openaiReq)
    if err != nil {
        return nil, err
    }
    
    // 转换响应格式
    return c.parseOpenAIResponse(resp)
}
```

### 本地模型集成

```go
type LocalLLMClient struct {
    endpoint   string
    modelName  string
    httpClient *http.Client
}

func (c *LocalLLMClient) Chat(ctx context.Context, request *ChatRequest) (*ChatResponse, error) {
    // 构建本地模型请求格式
    localReq := c.buildLocalRequest(request)
    
    // 发送到本地模型服务
    resp, err := c.sendLocalRequest(ctx, localReq)
    if err != nil {
        return nil, err
    }
    
    // 统一响应格式
    return c.parseLocalResponse(resp)
}
```

## 函数调用支持

### Function Calling 架构

```go
type FunctionCaller struct {
    executor  FunctionExecutor
    validator FunctionValidator
    registry  map[string]Function
}

type Function struct {
    Name        string      `json:"name"`
    Description string      `json:"description"`
    Parameters  interface{} `json:"parameters"`
    Handler     FunctionHandler
}

type FunctionHandler interface {
    Execute(ctx context.Context, args map[string]interface{}) (interface{}, error)
    Validate(args map[string]interface{}) error
}
```

### Web 搜索功能

```go
type WebSearchFunction struct {
    searchClient WebSearchClient
    queryGenerator QueryGenerator
}

func (f *WebSearchFunction) Execute(ctx context.Context, args map[string]interface{}) (interface{}, error) {
    query, ok := args["query"].(string)
    if !ok {
        return nil, errors.New("query parameter required")
    }
    
    // 执行网络搜索
    results, err := f.searchClient.Search(ctx, query)
    if err != nil {
        return nil, err
    }
    
    return f.formatSearchResults(results), nil
}
```

### 搜索检测机制

```go
type WebSearchDetector struct {
    rules []DetectionRule
}

type DetectionRule struct {
    Pattern    string
    Confidence float64
    Keywords   []string
}

func (d *WebSearchDetector) ShouldSearch(message string) bool {
    // 关键词检测
    if d.containsSearchKeywords(message) {
        return true
    }
    
    // 模式匹配
    if d.matchesSearchPattern(message) {
        return true
    }
    
    // 时间敏感性检测
    if d.containsTimeReference(message) {
        return true
    }
    
    return false
}
```

## 内容过滤

### 内容安全检查

```go
type ContentFilter struct {
    filters []FilterRule
}

type FilterRule struct {
    Type        FilterType
    Pattern     string
    Action      FilterAction
    Severity    int
}

func (f *ContentFilter) FilterRequest(request *ChatRequest) error {
    for _, message := range request.Messages {
        if err := f.checkMessage(message.Content); err != nil {
            return err
        }
    }
    return nil
}

func (f *ContentFilter) FilterResponse(response *ChatResponse) error {
    for _, choice := range response.Choices {
        if err := f.checkMessage(choice.Message.Content); err != nil {
            return err
        }
    }
    return nil
}
```

## 消息工具函数

### 消息处理

```go
type MessageUtils struct{}

func (u *MessageUtils) BuildSystemMessage(prompt string) Message {
    return Message{
        Role:    "system",
        Content: prompt,
    }
}

func (u *MessageUtils) BuildUserMessage(content string) Message {
    return Message{
        Role:    "user", 
        Content: content,
    }
}

func (u *MessageUtils) BuildAssistantMessage(content string) Message {
    return Message{
        Role:    "assistant",
        Content: content,
    }
}

func (u *MessageUtils) ExtractFunctionCalls(message Message) []FunctionCall {
    // 解析消息中的函数调用
    var calls []FunctionCall
    // ... 解析逻辑
    return calls
}
```

## 检索增强生成 (RAG)

### 查询生成器

```go
type QueryGenerator struct {
    llmClient LLMClient
    templates map[string]string
}

func (g *QueryGenerator) GenerateSearchQuery(context string, question string) (string, error) {
    prompt := g.buildQueryPrompt(context, question)
    
    request := &ChatRequest{
        Messages: []Message{
            {Role: "system", Content: g.templates["query_generation"]},
            {Role: "user", Content: prompt},
        },
        Model:       "gpt-3.5-turbo",
        Temperature: 0.1,
        MaxTokens:   100,
    }
    
    response, err := g.llmClient.Chat(context.Background(), request)
    if err != nil {
        return "", err
    }
    
    return response.Choices[0].Message.Content, nil
}
```

### 搜索管理器

```go
type SearchManager struct {
    webClient    WebSearchClient
    docClient    DocumentSearchClient
    queryGen     QueryGenerator
    resultMerger ResultMerger
}

func (m *SearchManager) Search(ctx context.Context, query string, sources []string) (*SearchResult, error) {
    var results []*SearchResult
    
    // 并行搜索多个源
    for _, source := range sources {
        switch source {
        case "web":
            result, err := m.webClient.Search(ctx, query)
            if err == nil {
                results = append(results, result)
            }
        case "documents":
            result, err := m.docClient.Search(ctx, query)
            if err == nil {
                results = append(results, result)
            }
        }
    }
    
    // 合并和排序结果
    return m.resultMerger.Merge(results), nil
}
```

## 错误处理和重试

### 错误分类

```go
type LLMError struct {
    Type    ErrorType
    Message string
    Code    int
    Retry   bool
}

const (
    ErrorTypeRateLimit ErrorType = "rate_limit"
    ErrorTypeTimeout   ErrorType = "timeout"
    ErrorTypeAuth      ErrorType = "auth"
    ErrorTypeModel     ErrorType = "model"
    ErrorTypeNetwork   ErrorType = "network"
)
```

### 重试机制

```go
type RetryConfig struct {
    MaxRetries    int
    InitialDelay  time.Duration
    MaxDelay      time.Duration
    BackoffFactor float64
}

func (c *OpenAIClient) ChatWithRetry(ctx context.Context, request *ChatRequest) (*ChatResponse, error) {
    var lastErr error
    
    for attempt := 0; attempt <= c.retryConfig.MaxRetries; attempt++ {
        resp, err := c.Chat(ctx, request)
        if err == nil {
            return resp, nil
        }
        
        lastErr = err
        
        // 检查是否可重试
        if !c.shouldRetry(err) {
            break
        }
        
        // 计算退避延迟
        delay := c.calculateDelay(attempt)
        time.Sleep(delay)
    }
    
    return nil, lastErr
}
```

## 监控和日志

### 性能指标

```go
type LLMMetrics struct {
    RequestCount    int64
    ErrorCount      int64
    AvgLatency      time.Duration
    TokenUsage      int64
    CostEstimate    float64
}

func (g *Gateway) RecordMetrics(request *ChatRequest, response *ChatResponse, duration time.Duration) {
    g.metrics.RequestCount++
    g.metrics.AvgLatency = (g.metrics.AvgLatency + duration) / 2
    
    if response.Usage.TotalTokens > 0 {
        g.metrics.TokenUsage += int64(response.Usage.TotalTokens)
    }
    
    g.metrics.CostEstimate += g.calculateCost(request.Model, response.Usage)
}
```

### 请求日志

```go
func (g *Gateway) logRequest(request *ChatRequest) {
    utils.Infof("LLM请求: 模型=%s, 消息数=%d, 最大令牌=%d", 
        request.Model, len(request.Messages), request.MaxTokens)
}

func (g *Gateway) logResponse(response *ChatResponse, duration time.Duration) {
    utils.Infof("LLM响应: 用时=%v, 令牌使用=%d", 
        duration, response.Usage.TotalTokens)
}
```

## 配置管理

### 模型配置

```yaml
llm:
  providers:
    openai:
      api_key: "${OPENAI_API_KEY}"
      base_url: "https://api.openai.com/v1"
      models:
        - gpt-4
        - gpt-3.5-turbo
      rate_limit: 60
      timeout: 30s
    
    local:
      endpoint: "http://localhost:8080"
      models:
        - local-llama
      timeout: 60s
  
  default_model: "gpt-3.5-turbo"
  max_tokens: 4096
  temperature: 0.7
```

### 功能配置

```yaml
functions:
  web_search:
    enabled: true
    provider: "tavily"
    max_results: 10
    timeout: 10s
  
  detection:
    web_search:
      keywords:
        - "最新"
        - "今天"
        - "现在"
      patterns:
        - "\\d{4}年.*?发生了什么"
        - "最近.*?怎么样"
```

## 扩展性设计

### 新增 LLM 提供商

1. 实现 `LLMClient` 接口
2. 注册到 LLM 网关
3. 添加相应配置

### 新增函数功能

1. 实现 `FunctionHandler` 接口
2. 注册到函数调用器
3. 配置函数元数据

### 自定义过滤器

1. 实现 `ContentFilter` 接口
2. 注册到过滤器链
3. 配置过滤规则