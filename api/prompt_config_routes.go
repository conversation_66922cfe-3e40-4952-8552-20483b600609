package api

import (
	"net/http"

	"hkchat_api/internal/config"
	"hkchat_api/internal/models/dto/request"
	"hkchat_api/internal/models/dto/response"
	"hkchat_api/pkg/utils"

	"github.com/gin-gonic/gin"
)

// PromptConfigRoutes 提示词配置路由
type PromptConfigRoutes struct {
	configService *config.PromptService
}

// NewPromptConfigRoutes 创建提示词配置路由
func NewPromptConfigRoutes(configService *config.PromptService) *PromptConfigRoutes {
	return &PromptConfigRoutes{
		configService: configService,
	}
}

// GetPromptConfig 获取提示词配置
func (r *PromptConfigRoutes) GetPromptConfig(c *gin.Context) {
	utils.Info("🔧 获取提示词配置")

	// 获取当前配置
	config := r.configService.GetConfig()

	// 转换为响应格式
	resp := response.NewPromptConfigResponse(config)

	utils.Info("✅ 提示词配置获取成功")
	c.JSON(http.StatusOK, resp)
}

// UpdatePromptConfig 更新提示词配置
func (r *PromptConfigRoutes) UpdatePromptConfig(c *gin.Context) {
	userID := r.getUserID(c)
	utils.Infof("🔧 用户 %s 请求更新提示词配置", userID)

	// 检查管理员权限
	if !r.isAdmin(c) {
		utils.Warnf("用户 %s 没有管理员权限，拒绝更新配置", userID)
		r.sendErrorResponse(c, http.StatusForbidden, "permission_denied", "需要管理员权限", "")
		return
	}

	var req request.PromptConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Errorf("提示词配置请求参数解析失败: %v", err)
		r.sendErrorResponse(c, http.StatusBadRequest, "invalid_request", "请求参数格式错误", err.Error())
		return
	}

	// 转换为更新映射
	updates := r.convertRequestToMap(&req)

	// 更新配置
	if err := r.configService.UpdateConfig(updates); err != nil {
		utils.Errorf("更新提示词配置失败: %v", err)
		r.sendErrorResponse(c, http.StatusInternalServerError, "update_failed", "配置更新失败", err.Error())
		return
	}

	// 获取更新后的配置
	updatedConfig := r.configService.GetConfig()
	resp := response.NewPromptConfigResponse(updatedConfig)

	utils.Infof("✅ 用户 %s 提示词配置更新成功", userID)
	c.JSON(http.StatusOK, resp)
}

// ReloadPromptConfig 重新加载提示词配置
func (r *PromptConfigRoutes) ReloadPromptConfig(c *gin.Context) {
	userID := r.getUserID(c)
	utils.Infof("🔧 用户 %s 请求重新加载提示词配置", userID)

	// 检查管理员权限
	if !r.isAdmin(c) {
		utils.Warnf("用户 %s 没有管理员权限，拒绝重新加载配置", userID)
		r.sendErrorResponse(c, http.StatusForbidden, "permission_denied", "需要管理员权限", "")
		return
	}

	// 重新加载配置
	if err := r.configService.ReloadConfig(); err != nil {
		utils.Errorf("重新加载提示词配置失败: %v", err)
		r.sendErrorResponse(c, http.StatusInternalServerError, "reload_failed", "配置重新加载失败", err.Error())
		return
	}

	// 获取重新加载后的配置
	config := r.configService.GetConfig()
	resp := response.NewPromptConfigResponse(config)

	utils.Infof("✅ 用户 %s 提示词配置重新加载成功", userID)
	c.JSON(http.StatusOK, resp)
}

// ResetPromptConfig 重置提示词配置为默认值
func (r *PromptConfigRoutes) ResetPromptConfig(c *gin.Context) {
	userID := r.getUserID(c)
	utils.Infof("🔧 用户 %s 请求重置提示词配置", userID)

	// 检查管理员权限
	if !r.isAdmin(c) {
		utils.Warnf("用户 %s 没有管理员权限，拒绝重置配置", userID)
		r.sendErrorResponse(c, http.StatusForbidden, "permission_denied", "需要管理员权限", "")
		return
	}

	// 重置配置
	if err := r.configService.ResetToDefault(); err != nil {
		utils.Errorf("重置提示词配置失败: %v", err)
		r.sendErrorResponse(c, http.StatusInternalServerError, "reset_failed", "配置重置失败", err.Error())
		return
	}

	// 获取重置后的配置
	config := r.configService.GetConfig()
	resp := response.NewPromptConfigResponse(config)

	utils.Infof("✅ 用户 %s 提示词配置重置成功", userID)
	c.JSON(http.StatusOK, resp)
}

// convertRequestToMap 将请求转换为更新映射
func (r *PromptConfigRoutes) convertRequestToMap(req *request.PromptConfigRequest) map[string]interface{} {
	updates := make(map[string]interface{})

	if req.TaskModel != nil {
		updates["TASK_MODEL"] = *req.TaskModel
	}
	if req.TaskModelExternal != nil {
		updates["TASK_MODEL_EXTERNAL"] = *req.TaskModelExternal
	}
	if req.EnableTitleGeneration != nil {
		updates["ENABLE_TITLE_GENERATION"] = *req.EnableTitleGeneration
	}
	if req.TitleGenerationPromptTemplate != nil {
		updates["TITLE_GENERATION_PROMPT_TEMPLATE"] = *req.TitleGenerationPromptTemplate
	}
	if req.ImagePromptGenerationPromptTemplate != nil {
		updates["IMAGE_PROMPT_GENERATION_PROMPT_TEMPLATE"] = *req.ImagePromptGenerationPromptTemplate
	}
	if req.EnableAutocompleteGeneration != nil {
		updates["ENABLE_AUTOCOMPLETE_GENERATION"] = *req.EnableAutocompleteGeneration
	}
	if req.AutocompleteGenerationInputMaxLength != nil {
		updates["AUTOCOMPLETE_GENERATION_INPUT_MAX_LENGTH"] = *req.AutocompleteGenerationInputMaxLength
	}
	if req.TagsGenerationPromptTemplate != nil {
		updates["TAGS_GENERATION_PROMPT_TEMPLATE"] = *req.TagsGenerationPromptTemplate
	}
	if req.FollowUpGenerationPromptTemplate != nil {
		updates["FOLLOW_UP_GENERATION_PROMPT_TEMPLATE"] = *req.FollowUpGenerationPromptTemplate
	}
	if req.EnableFollowUpGeneration != nil {
		updates["ENABLE_FOLLOW_UP_GENERATION"] = *req.EnableFollowUpGeneration
	}
	if req.EnableTagsGeneration != nil {
		updates["ENABLE_TAGS_GENERATION"] = *req.EnableTagsGeneration
	}
	if req.EnableSearchQueryGeneration != nil {
		updates["ENABLE_SEARCH_QUERY_GENERATION"] = *req.EnableSearchQueryGeneration
	}
	if req.EnableRetrievalQueryGeneration != nil {
		updates["ENABLE_RETRIEVAL_QUERY_GENERATION"] = *req.EnableRetrievalQueryGeneration
	}
	if req.QueryGenerationPromptTemplate != nil {
		updates["QUERY_GENERATION_PROMPT_TEMPLATE"] = *req.QueryGenerationPromptTemplate
	}
	if req.ToolsFunctionCallingPromptTemplate != nil {
		updates["TOOLS_FUNCTION_CALLING_PROMPT_TEMPLATE"] = *req.ToolsFunctionCallingPromptTemplate
	}

	return updates
}

// 辅助方法
func (r *PromptConfigRoutes) getUserID(c *gin.Context) string {
	if userID, exists := c.Get("user_id"); exists {
		if uid, ok := userID.(string); ok {
			return uid
		}
	}
	return "anonymous"
}

func (r *PromptConfigRoutes) isAdmin(c *gin.Context) bool {
	// 这里应该实现真实的管理员检查逻辑
	// 现在先简单实现，检查是否有admin角色
	if role, exists := c.Get("user_role"); exists {
		if userRole, ok := role.(string); ok {
			return userRole == "admin"
		}
	}

	// 临时实现：检查Authorization header中是否包含admin
	authHeader := c.GetHeader("Authorization")
	return authHeader != "" && (authHeader == "Bearer admin" || authHeader == "admin")
}

func (r *PromptConfigRoutes) sendErrorResponse(c *gin.Context, statusCode int, errorType, message, details string) {
	errorResp := response.ErrorResponse{
		Success: false,
		Error:   errorType,
		Message: message,
		Code:    string(rune(statusCode)),
		Details: details,
	}

	if details != "" {
		utils.Debugf("错误详情: %s", details)
	}

	c.JSON(statusCode, errorResp)
}

// RegisterRoutes 注册提示词配置路由
func (r *PromptConfigRoutes) RegisterRoutes(router *gin.Engine) {
	api := router.Group("/api")
	{
		prompts := api.Group("/prompts")
		{
			// 获取配置（需要登录）
			prompts.GET("/config", r.GetPromptConfig)

			// 更新配置（需要管理员权限）
			prompts.POST("/config", r.UpdatePromptConfig)

			// 重新加载配置（需要管理员权限）
			prompts.POST("/config/reload", r.ReloadPromptConfig)

			// 重置配置（需要管理员权限）
			prompts.POST("/config/reset", r.ResetPromptConfig)
		}
	}

	utils.Infof("✅ 提示词配置路由注册完成")
}
