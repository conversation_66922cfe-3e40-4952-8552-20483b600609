package api

import (
	"fmt"
	"net/http"
	"time"

	_ "hkchat_api/docs"
	"hkchat_api/api/v2"
	"hkchat_api/internal/config"
	"hkchat_api/internal/core/di"
	"hkchat_api/internal/repository"
	"hkchat_api/internal/services"
	"hkchat_api/pkg/utils"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

// ApiRegistry API路由注册器
type ApiRegistry struct {
	chatRoutes         *ChatApiRoutes
	chatV2Routes       *v2.ChatApiRoutes
	promptConfigRoutes *PromptConfigRoutes
	container          *di.Container
}

// NewApiRegistry 创建API路由注册器
func NewApiRegistry() *ApiRegistry {
	return &ApiRegistry{
		chatRoutes:         NewChatApiRoutes(nil, nil, nil), // 暂时不依赖服务
		chatV2Routes:       nil,                             // 稍后初始化
		promptConfigRoutes: nil,                             // 稍后初始化
	}
}

// NewApiRegistryWithContainer 创建带DI容器的API路由注册器
func NewApiRegistryWithContainer(container *di.Container) *ApiRegistry {
	var chatService *services.ChatService
	var userRepo *repository.UserRepository
	var modelRepo *repository.ModelRepository
	var chatRepo *repository.ChatRepository
	var configManager *config.Manager
	var promptConfigService *config.PromptService

	// 尝试从DI容器获取服务
	if container != nil {
		utils.Debug("🔍 开始从DI容器获取依赖...")
		diInstance := container.GetDI()

		// 获取 ChatRepository
		utils.Debug("🔍 尝试获取ChatRepository...")
		if chatRepoInstance := diInstance.GetByName("ChatRepository"); chatRepoInstance != nil {
			if cr, ok := chatRepoInstance.(*repository.ChatRepository); ok {
				chatRepo = cr
				utils.Debug("   ✅ 从DI容器获取ChatRepository成功")
			} else {
				utils.Warn("   ❌ ChatRepository类型转换失败")
			}
		} else {
			utils.Warn("   ❌ 从DI容器获取ChatRepository失败")
		}

		// 获取 ConfigManager
		utils.Debug("🔍 尝试获取ConfigManager...")
		configManagerComponent := di.GetCoreComponent("ConfigManager")
		if configManagerComponent != nil {
			utils.Debug("   ✅ ConfigManager组件获取成功")
			if cmc, ok := configManagerComponent.(*di.ConfigManagerComponent); ok {
				configManager = cmc.GetConfigManager()
				if configManager != nil {
					utils.Debug("   ✅ 从DI容器获取ConfigManager成功")
				} else {
					utils.Warn("   ❌ ConfigManager实例为空")
				}
			} else {
				utils.Warnf("   ❌ ConfigManager组件类型转换失败，实际类型: %T", configManagerComponent)
			}
		} else {
			utils.Warn("   ❌ 从DI容器获取ConfigManager失败")
		}

		// 获取UserRepository
		if userRepoInstance := diInstance.GetByName("UserRepository"); userRepoInstance != nil {
			if ur, ok := userRepoInstance.(*repository.UserRepository); ok {
				userRepo = ur
				utils.Debug("   ✅ 从DI容器获取UserRepository成功")
			}
		}

		// 获取ModelRepository
		if modelRepoInstance := diInstance.GetByName("ModelRepository"); modelRepoInstance != nil {
			if mr, ok := modelRepoInstance.(*repository.ModelRepository); ok {
				modelRepo = mr
				utils.Debug("   ✅ 从DI容器获取ModelRepository成功")
			}
		}

		// 获取 LLM 网关并创建 ChatService
		utils.Debug("🔍 尝试获取LLM网关...")
		if llmGateway := di.GetGlobalLLMGateway(); llmGateway != nil {
			utils.Debug("   ✅ 获取LLM网关成功")
			utils.Debug("🔍 检查创建ChatService的依赖条件...")
			utils.Debugf("   - chatRepo: %v", chatRepo != nil)
			utils.Debugf("   - modelRepo: %v", modelRepo != nil)
			utils.Debugf("   - configManager: %v", configManager != nil)

			if chatRepo != nil && modelRepo != nil && configManager != nil {
				utils.Debug("🔍 正在创建ChatService...")
				chatService = services.NewChatService(llmGateway, chatRepo, modelRepo, configManager)
				utils.Debug("   ✅ 使用LLM网关创建ChatService成功")
			} else {
				utils.Warn("   ⚠️ ChatRepository、ModelRepository 或 ConfigManager 缺失，无法创建ChatService")
				if chatRepo == nil {
					utils.Warn("   ⚠️ ChatRepository 为空")
				}
				if modelRepo == nil {
					utils.Warn("   ⚠️ ModelRepository 为空")
				}
				if configManager == nil {
					utils.Warn("   ⚠️ ConfigManager 为空")
				}
				utils.Warn("   ⚠️ 聊天功能将不可用，请检查依赖注入配置")
			}
		} else {
			utils.Warn("   ⚠️ 无法获取LLM网关，ChatService将为空")
			utils.Warn("   ⚠️ 聊天功能将不可用，请检查LLM网关配置")
		}

		// 获取PromptConfigService（通过DI容器）
		if promptConfigServiceInstance := diInstance.GetByName("PromptConfigService"); promptConfigServiceInstance != nil {
			if pcs, ok := promptConfigServiceInstance.(*config.PromptService); ok {
				promptConfigService = pcs
				utils.Debug("   ✅ 从DI容器获取PromptConfigService成功")
			}
		} else if db := diInstance.GetDB(); db != nil {
			// 如果DI容器中没有PromptConfigService，但有数据库连接，则创建一个
			promptConfigService = config.NewPromptService(db)
			utils.Debug("   ✅ 提示词配置服务初始化成功")
		}
	} else {
		utils.Warn("🔍 DI容器为空，无法获取依赖")
	}

	registry := &ApiRegistry{
		chatRoutes:   NewChatApiRoutes(chatService, userRepo, modelRepo),
		chatV2Routes: v2.NewChatApiRoutes(chatService, userRepo, modelRepo),
		container:    container,
	}

	// 初始化提示词配置路由
	if promptConfigService != nil {
		registry.promptConfigRoutes = NewPromptConfigRoutes(promptConfigService)
	}

	// 检查依赖注入状态（用于调试）
	registry.CheckDependencyInjectionStatus()

	return registry
}

// NewApiRegistryWithServices 创建带服务依赖的API路由注册器
func NewApiRegistryWithServices(chatService *services.ChatService, userRepo *repository.UserRepository, modelRepo *repository.ModelRepository) *ApiRegistry {
	return &ApiRegistry{
		chatRoutes:         NewChatApiRoutes(chatService, userRepo, modelRepo),
		chatV2Routes:       v2.NewChatApiRoutes(chatService, userRepo, modelRepo),
		promptConfigRoutes: nil, // 需要数据库连接才能初始化
	}
}

// RegisterAllRoutes 注册所有API路由
func (r *ApiRegistry) RegisterAllRoutes(router *gin.Engine) {
	utils.Info("🌐 开始注册API路由...")

	// 配置跨域设置
	r.setupCORS(router)

	// 注册全局中间件
	r.setupGlobalMiddleware(router)

	// 注册Swagger文档
	router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
	utils.Debug("   ✅ Swagger文档路由注册成功")

	// 注册健康检查和基础路由
	r.setupBasicRoutes(router)
	//router 加上 /api 前缀
	api := router.Group("/api").Group("v1")
	// 注册聊天路由
	r.chatRoutes.RegisterRoutes(api)
	utils.Debug("   ✅ 聊天路由注册成功")

	// 注册V2聊天路由
	apiV2 := router.Group("/api").Group("v2")
	if r.chatV2Routes != nil {
		r.chatV2Routes.RegisterRoutes(apiV2)
		utils.Debug("   ✅ V2聊天路由注册成功")
	} else {
		utils.Warn("   ⚠️ V2聊天路由未初始化，跳过注册")
	}

	// 注册提示词配置路由
	if r.promptConfigRoutes != nil {
		r.promptConfigRoutes.RegisterRoutes(router)
		utils.Debug("   ✅ 提示词配置路由注册成功")
	} else {
		utils.Warn("   ⚠️ 提示词配置路由未初始化，跳过注册")
	}

	utils.Info("🌐 API路由注册完成")
}

// setupCORS 配置跨域设置
func (r *ApiRegistry) setupCORS(router *gin.Engine) {
	config := cors.Config{
		AllowOrigins:     []string{"*"}, // 生产环境应该设置具体的域名
		AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"Origin", "Content-Type", "Content-Length", "Accept-Encoding", "X-CSRF-Token", "Authorization"},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	}

	router.Use(cors.New(config))
	utils.Debug("   ✅ 跨域设置完成")
}

// setupGlobalMiddleware 设置全局中间件
func (r *ApiRegistry) setupGlobalMiddleware(router *gin.Engine) {
	// 请求日志中间件
	router.Use(r.loggerMiddleware())

	// 错误处理中间件
	router.Use(r.errorHandlerMiddleware())

	// 请求限流中间件（可选）
	// router.Use(r.rateLimitMiddleware())

	utils.Debug("   ✅ 全局中间件设置完成")
}

// loggerMiddleware 日志中间件
func (r *ApiRegistry) loggerMiddleware() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		return fmt.Sprintf("[%s] %s %s %d %s\n",
			param.TimeStamp.Format("2006-01-02 15:04:05"),
			param.Method,
			param.Path,
			param.StatusCode,
			param.Latency,
		)
	})
}

// errorHandlerMiddleware 错误处理中间件
func (r *ApiRegistry) errorHandlerMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				utils.Errorf("API处理异常: %v", err)
				c.JSON(http.StatusInternalServerError, gin.H{
					"error":   "Internal Server Error",
					"message": "服务器内部错误",
				})
				c.Abort()
			}
		}()
		c.Next()
	}
}

// authMiddleware 认证中间件（示例）
func (r *ApiRegistry) authMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		token := c.GetHeader("Authorization")
		if token == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "Unauthorized",
				"message": "请提供认证token",
			})
			c.Abort()
			return
		}

		// 这里可以验证token的有效性
		// if !isValidToken(token) {
		//     c.JSON(http.StatusUnauthorized, gin.H{
		//         "error":   "Unauthorized",
		//         "message": "无效的token",
		//     })
		//     c.Abort()
		//     return
		// }

		c.Next()
	}
}

// setupBasicRoutes 设置基础路由
func (r *ApiRegistry) setupBasicRoutes(router *gin.Engine) {
	// 健康检查
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":  "healthy",
			"message": "HKChat API is running",
		})
	})

	// API信息
	router.GET("/api/info", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"name":        "HKChat API",
			"version":     "1.0.0",
			"description": "HKChat 聊天应用API",
		})
	})

	// 404处理
	router.NoRoute(func(c *gin.Context) {
		c.JSON(http.StatusNotFound, gin.H{
			"detail": "Not Found",
		})
	})

	utils.Debug("   ✅ 基础路由注册成功")
}

// SetupRoutes 设置所有路由（兼容旧接口）
func SetupRoutes(router *gin.Engine) {
	registry := NewApiRegistry()
	registry.RegisterAllRoutes(router)
}

// SetupRoutesWithContainer 使用DI容器设置所有路由
func SetupRoutesWithContainer(router *gin.Engine, container *di.Container) {
	// 测试ChatService创建过程
	utils.Info("🧪 测试ChatService创建...")
	TestChatServiceCreation(container)

	registry := NewApiRegistryWithContainer(container)

	// 启动时的额外检查
	utils.Info("🚀 启动时检查依赖注入状态...")
	registry.CheckDependencyInjectionStatus()

	registry.RegisterAllRoutes(router)
}

// CheckDependencyInjectionStatus 检查依赖注入状态
func (r *ApiRegistry) CheckDependencyInjectionStatus() {
	utils.Info("🔍 检查依赖注入状态...")

	// 检查 ChatService 状态
	if r.chatRoutes != nil && r.chatRoutes.chatService != nil {
		utils.Info("   ✅ ChatService 已初始化")
	} else {
		utils.Warn("   ❌ ChatService 未初始化")
	}

	// 检查 PromptConfigService 状态
	if r.promptConfigRoutes != nil {
		utils.Info("   ✅ PromptConfigService 已初始化")
	} else {
		utils.Warn("   ❌ PromptConfigService 未初始化")
	}

	// 检查 Container 状态
	if r.container != nil {
		utils.Info("   ✅ DI Container 已初始化")
		if diInstance := r.container.GetDI(); diInstance != nil {
			stats := diInstance.GetStats()
			utils.Infof("   📊 DI统计: 仓储=%d, 服务=%d, 处理器=%d, 总计=%d",
				stats["repositories"], stats["services"], stats["handlers"], stats["total"])
		}
	} else {
		utils.Warn("   ❌ DI Container 未初始化")
	}

	utils.Info("🔍 依赖注入状态检查完成")
}

// TestChatServiceCreation 测试ChatService创建过程（调试用）
func TestChatServiceCreation(container *di.Container) {
	utils.Info("🧪 === 测试ChatService创建过程 ===")

	if container == nil {
		utils.Errorf("   ❌ 容器为空")
		return
	}

	diInstance := container.GetDI()
	if diInstance == nil {
		utils.Errorf("   ❌ DI实例为空")
		return
	}

	// 步骤1: 检查ChatRepository
	utils.Info("🔍 步骤1: 检查ChatRepository...")
	chatRepoInstance := diInstance.GetByName("ChatRepository")
	if chatRepoInstance == nil {
		utils.Errorf("   ❌ ChatRepository获取失败")
		return
	}

	chatRepo, ok := chatRepoInstance.(*repository.ChatRepository)
	if !ok {
		utils.Errorf("   ❌ ChatRepository类型转换失败")
		return
	}
	utils.Info("   ✅ ChatRepository获取成功")

	// 步骤2: 检查ConfigManager
	utils.Info("🔍 步骤2: 检查ConfigManager...")
	configManagerComponent := di.GetCoreComponent("ConfigManager")
	if configManagerComponent == nil {
		utils.Errorf("   ❌ ConfigManager组件获取失败")
		return
	}
	utils.Info("   ✅ ConfigManager组件获取成功")

	cmc, ok := configManagerComponent.(*di.ConfigManagerComponent)
	if !ok {
		utils.Errorf("   ❌ ConfigManager组件类型转换失败，实际类型: %T", configManagerComponent)
		return
	}
	utils.Info("   ✅ ConfigManager组件类型转换成功")

	configManager := cmc.GetConfigManager()
	if configManager == nil {
		utils.Errorf("   ❌ ConfigManager实例为空")
		return
	}
	utils.Info("   ✅ ConfigManager获取成功")

	// 步骤3: 检查LLM网关
	utils.Info("🔍 步骤3: 检查LLM网关...")
	llmGateway := di.GetGlobalLLMGateway()
	if llmGateway == nil {
		utils.Errorf("   ❌ LLM网关为空")
		return
	}
	utils.Info("   ✅ LLM网关获取成功")

	// 步骤3.5: 检查ModelRepository
	utils.Info("🔍 步骤3.5: 检查ModelRepository...")
	modelRepoInstance := diInstance.GetByName("ModelRepository")
	if modelRepoInstance == nil {
		utils.Errorf("   ❌ ModelRepository获取失败")
		return
	}

	modelRepo, ok := modelRepoInstance.(*repository.ModelRepository)
	if !ok {
		utils.Errorf("   ❌ ModelRepository类型转换失败")
		return
	}
	utils.Info("   ✅ ModelRepository获取成功")

	// 步骤4: 尝试创建ChatService
	utils.Info("🔍 步骤4: 尝试创建ChatService...")
	chatService := services.NewChatService(llmGateway, chatRepo, modelRepo, configManager)
	if chatService == nil {
		utils.Errorf("   ❌ ChatService创建失败")
		return
	}
	utils.Info("   ✅ ChatService创建成功")

	utils.Info("🧪 === ChatService创建测试完成 ===")
}
