package middleware

import (
	"net/http"
	"strings"

	"hkchat_api/internal/config"
	"hkchat_api/internal/models/entities"
	"hkchat_api/internal/repository"
	"hkchat_api/pkg/auth"
	"hkchat_api/pkg/utils"

	"github.com/gin-gonic/gin"
)

// AuthMiddleware JWT认证中间件
type AuthMiddleware struct {
	jwtService *auth.JWTService
	userRepo   *repository.UserRepository
}

// NewAuthMiddleware 创建认证中间件
func NewAuthMiddleware(userRepo *repository.UserRepository) *AuthMiddleware {
	jwtService := auth.NewJWTService(config.GlobalConfig.JWT.SecretKey)
	return &AuthMiddleware{
		jwtService: jwtService,
		userRepo:   userRepo,
	}
}

// RequireUserAuth 需要用户认证的中间件（模仿Python项目的get_verified_user）
func (m *AuthMiddleware) RequireUserAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		token := m.extractToken(c)
		token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjE5OTlhN2M5LTkxYzUtNDBiNS04MTQ2LWFhNzg5MmM0NjVmNSIsImV4cCI6NDkwNTY2NzQ1NX0.ESLCCsXmxhlkG_JamWg36LEDWOLdr7fVaRKeJ-mt2Vw"
		if token == "" {
			utils.AuthWarn("请求缺少Authorization token: %s %s", c.Request.Method, c.Request.URL.Path)
			c.JSON(http.StatusUnauthorized, gin.H{"detail": "Not authenticated"})
			c.Abort()
			return
		}

		// 解析token
		claims, err := m.jwtService.ParseToken(token)
		if err != nil {
			utils.AuthError("JWT token验证失败: %v", err)
			c.JSON(http.StatusUnauthorized, gin.H{"detail": "Invalid token"})
			c.Abort()
			return
		}

		// 通过用户ID获取完整用户信息
		var user entities.User
		err = m.userRepo.GetByID(&user, claims.ID)
		if err != nil {
			utils.AuthError("获取用户信息失败: %v", err)
			c.JSON(http.StatusUnauthorized, gin.H{"detail": "User not found"})
			c.Abort()
			return
		}

		// 将用户信息存储到上下文中
		c.Set("user", &user)
		c.Set("jwt_claims", claims)

		utils.AuthDebug("用户认证成功: ID=%s, 用户名=%s", user.Id, user.Name)
		c.Next()
	}
}

// RequireAdminAuth 需要管理员认证的中间件（模仿Python项目的get_admin_user）
func (m *AuthMiddleware) RequireAdminAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		token := m.extractToken(c)
		if token == "" {
			utils.AuthWarn("管理员请求缺少Authorization token: %s %s", c.Request.Method, c.Request.URL.Path)
			c.JSON(http.StatusUnauthorized, gin.H{"detail": "Not authenticated"})
			c.Abort()
			return
		}

		// 解析token
		claims, err := m.jwtService.ParseToken(token)
		if err != nil {
			utils.AuthError("管理员JWT token验证失败: %v", err)
			c.JSON(http.StatusUnauthorized, gin.H{"detail": "Invalid token"})
			c.Abort()
			return
		}

		// 通过用户ID获取完整用户信息
		var user entities.User
		err = m.userRepo.GetByID(&user, claims.ID)
		if err != nil {
			utils.AuthError("获取用户信息失败: %v", err)
			c.JSON(http.StatusUnauthorized, gin.H{"detail": "User not found"})
			c.Abort()
			return
		}

		// 检查管理员权限
		if user.Role != "admin" {
			utils.AuthWarn("非管理员用户尝试访问管理员接口: 用户=%s, 角色=%s", user.Id, user.Role)
			c.JSON(http.StatusForbidden, gin.H{"detail": "Admin access required"})
			c.Abort()
			return
		}

		// 将用户信息存储到上下文中
		c.Set("user_id", user.Id)
		c.Set("username", user.Name)
		c.Set("user_email", user.Email)
		c.Set("user_role", user.Role)
		c.Set("user", &user)
		c.Set("jwt_claims", claims)

		utils.AuthDebug("管理员认证成功: ID=%s, 用户名=%s", user.Id, user.Name)
		c.Next()
	}
}

// OptionalAuth 可选认证的中间件（模仿Python项目的/api/config处理方式）
func (m *AuthMiddleware) OptionalAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		token := m.extractToken(c)
		if token == "" {
			// 没有token，继续执行但不设置用户信息
			utils.AuthDebug("可选认证: 未提供token")
			c.Next()
			return
		}

		// 尝试解析token
		claims, err := m.jwtService.ParseToken(token)
		if err != nil {
			utils.AuthWarn("可选认证: token解析失败: %v", err)
			// token无效，继续执行但不设置用户信息
			c.Next()
			return
		}

		// 尝试通过用户ID获取完整用户信息
		var user entities.User
		err = m.userRepo.GetByID(&user, claims.ID)
		if err != nil {
			utils.AuthWarn("可选认证: 获取用户信息失败: %v", err)
			// 获取用户信息失败，继续执行但不设置用户信息
			c.Next()
			return
		}

		// 将用户信息存储到上下文中
		c.Set("user_id", user.Id)
		c.Set("username", user.Name)
		c.Set("user_email", user.Email)
		c.Set("user_role", user.Role)
		c.Set("user", &user)
		c.Set("jwt_claims", claims)

		utils.AuthDebug("可选认证成功: ID=%s, 用户名=%s", user.Id, user.Name)
		c.Next()
	}
}

// extractToken 从请求中提取token
func (m *AuthMiddleware) extractToken(c *gin.Context) string {
	// 从Authorization header中提取
	authHeader := c.GetHeader("Authorization")
	if authHeader != "" {
		if strings.HasPrefix(authHeader, "Bearer ") {
			return authHeader
		}
		// 如果只是token没有Bearer前缀，添加前缀
		return "Bearer " + authHeader
	}

	// 从query参数中提取（用于某些特殊场景）
	if token := c.Query("token"); token != "" {
		return "Bearer " + token
	}

	return ""
}

// GetUserID 从上下文中获取用户ID
func GetUserID(c *gin.Context) string {
	if userID, exists := c.Get("user_id"); exists {
		if uid, ok := userID.(string); ok {
			return uid
		}
	}
	return ""
}

// GetUsername 从上下文中获取用户名
func GetUsername(c *gin.Context) string {
	if username, exists := c.Get("username"); exists {
		if uname, ok := username.(string); ok {
			return uname
		}
	}
	return ""
}

// GetUserRole 从上下文中获取用户角色
func GetUserRole(c *gin.Context) string {
	if role, exists := c.Get("user_role"); exists {
		if r, ok := role.(string); ok {
			return r
		}
	}
	return ""
}

// GetJWTClaims 从上下文中获取JWT Claims
func GetJWTClaims(c *gin.Context) *auth.JWTClaims {
	if claims, exists := c.Get("jwt_claims"); exists {
		if jwtClaims, ok := claims.(*auth.JWTClaims); ok {
			return jwtClaims
		}
	}
	return nil
}

// IsAuthenticated 检查用户是否已认证
func IsAuthenticated(c *gin.Context) bool {
	return GetUserID(c) != ""
}

// IsAdmin 检查用户是否为管理员
func IsAdmin(c *gin.Context) bool {
	return GetUserRole(c) == "admin"
}

// GetUser 从上下文中获取完整用户信息
func GetUser(c *gin.Context) *entities.User {
	if user, exists := c.Get("user"); exists {
		if u, ok := user.(*entities.User); ok {
			return u
		}
	}
	return nil
}
