package api

import (
	"net/http"
	"time"

	"hkchat_api/api/middleware"
	"hkchat_api/internal/core/llm"
	"hkchat_api/internal/models/domain"
	"hkchat_api/internal/models/dto/request"
	"hkchat_api/internal/models/dto/response"
	"hkchat_api/internal/models/entities"
	"hkchat_api/internal/repository"
	"hkchat_api/internal/services"
	"hkchat_api/pkg/utils"

	"github.com/gin-gonic/gin"
)

// ChatApiRoutes 聊天API路由
type ChatApiRoutes struct {
	chatService    *services.ChatService
	authMiddleware *middleware.AuthMiddleware
	modelRepo      *repository.ModelRepository
	contentFilter  *llm.ContentFilter
}

// NewChatApiRoutes 创建聊天API路由
func NewChatApiRoutes(chatService *services.ChatService, userRepo *repository.UserRepository, modelRepo *repository.ModelRepository) *ChatApiRoutes {
	return &ChatApiRoutes{
		chatService:    chatService,
		authMiddleware: middleware.NewAuthMiddleware(userRepo),
		modelRepo:      modelRepo,
		contentFilter:  llm.NewContentFilter(nil),
	}
}

// ChatCompletion 聊天完成接口
func (r *ChatApiRoutes) ChatCompletion(c *gin.Context) {
	user := middleware.GetUser(c)

	// 解析请求参数
	var req request.ChatCompletionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		r.sendErrorResponse(c, http.StatusBadRequest, "invalid_request", "请求参数格式错误", err.Error())
		return
	}

	// 检查服务是否可用
	if r.chatService == nil {
		r.sendErrorResponse(c, http.StatusInternalServerError, "service_unavailable", "聊天服务未初始化", "")
		return
	}

	// 处理聊天请求
	resp, err := r.processChatRequest(c, &req, user)
	if err != nil {
		r.sendErrorResponse(c, http.StatusInternalServerError, "service_error", "聊天服务处理失败", err.Error())
		return
	}

	utils.ChatInfo("用户 %s 聊天请求处理成功，响应ID: %s", user.Id, resp.ID)
	c.JSON(http.StatusOK, resp)
}


// processChatRequest 处理聊天请求
func (r *ChatApiRoutes) processChatRequest(c *gin.Context, req *request.ChatCompletionRequest, user *entities.User) (*response.ChatCompletionResponse, error) {
	// 初始化metadata
	metadata := map[string]interface{}{
		"user_id":   user.Id,
		"chat_id":   req.ChatID,
		"timestamp": time.Now().Unix(),
	}

	// 转换DTO为domain model
	domainReq := r.convertDTOToDomain(req)

	// 处理聊天载荷
	processedDomainReq, processedMetadata, _, err := r.chatService.ProcessChatPayload(c.Request.Context(), domainReq, user, metadata)
	if err != nil {
		return nil, utils.NewError("聊天载荷处理失败: %v", err)
	}

	// 内容过滤
	filteredDomainReq, err := r.contentFilter.FilterChatRequest(c.Request.Context(), processedDomainReq, user, processedMetadata)
	if err != nil {
		return nil, utils.NewError("消息审核失败: %v", err)
	}

	// 调用聊天服务（传入domain model）
	if finalDomainReq, ok := filteredDomainReq.(*domain.ChatRequest); ok {
		return r.chatService.ProcessChatCompletion(c.Request.Context(), finalDomainReq, user)
	}
	
	return nil, utils.NewError("内容过滤返回类型错误")
}





// sendErrorResponse 发送错误响应
func (r *ChatApiRoutes) sendErrorResponse(c *gin.Context, statusCode int, errorType, message, details string) {
	errorResp := response.ErrorResponse{
		Success: false,
		Error:   errorType,
		Message: message,
		Code:    string(rune(statusCode)),
		Details: details,
	}

	if details != "" {
		utils.ApiDebug("错误详情: %s", details)
	}

	c.JSON(statusCode, errorResp)
}

// RegisterRoutes 注册聊天路由
func (r *ChatApiRoutes) RegisterRoutes(router *gin.RouterGroup) {
	router.Use(r.authMiddleware.RequireUserAuth())
	router.POST("/chat/completions", r.ChatCompletion)
	utils.ApiInfo("聊天API路由注册完成: /chat/completions")
}

// convertDTOToDomain 将简化的DTO转换为domain model
func (r *ChatApiRoutes) convertDTOToDomain(req *request.ChatCompletionRequest) *domain.ChatRequest {
	// 将query转换为用户消息
	messages := []domain.ChatMessage{
		{
			Role:    "user",
			Content: req.Query,
		},
	}

	// 转换Features
	var features *domain.ChatFeatures
	if req.Features != nil {
		features = &domain.ChatFeatures{
			CodeInterpreter: req.Features.CodeInterpreter,
			ImageGeneration: req.Features.ImageGeneration,
			Memory:          req.Features.Memory,
			WebSearch:       req.Features.WebSearch,
		}
	}

	return &domain.ChatRequest{
		Model:    req.Model,
		Messages: messages,
		Query:    req.Query,
		ChatID:   req.ChatID,
		Features: features,
		// 设置默认值
		Stream:    false,
		SessionID: "", // 如果需要，可从上下文生成
	}
}

