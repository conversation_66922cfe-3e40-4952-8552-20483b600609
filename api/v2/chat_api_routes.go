package v2

import (
	"net/http"
	"time"

	"hkchat_api/api/middleware"
	"hkchat_api/internal/core/llm"
	"hkchat_api/internal/models/domain"
	"hkchat_api/internal/models/dto/request"
	"hkchat_api/internal/models/dto/response"
	"hkchat_api/internal/models/entities"
	"hkchat_api/internal/repository"
	"hkchat_api/internal/services"
	"hkchat_api/pkg/utils"

	"github.com/gin-gonic/gin"
)

// ChatApiRoutes V2版本的聊天API路由
type ChatApiRoutes struct {
	chatService    *services.ChatService
	authMiddleware *middleware.AuthMiddleware
	modelRepo      *repository.ModelRepository
	contentFilter  *llm.ContentFilter
}

// NewChatApiRoutes 创建V2聊天API路由
func NewChatApiRoutes(chatService *services.ChatService, userRepo *repository.UserRepository, modelRepo *repository.ModelRepository) *ChatApiRoutes {
	return &ChatApiRoutes{
		chatService:    chatService,
		authMiddleware: middleware.NewAuthMiddleware(userRepo),
		modelRepo:      modelRepo,
		contentFilter:  llm.NewContentFilter(nil),
	}
}

// ChatCompletionV2 V2版本的聊天完成接口（SSE格式）
func (r *ChatApiRoutes) ChatCompletionV2(c *gin.Context) {
	user := middleware.GetUser(c)

	// 解析V2格式的请求参数
	var v2Req request.ChatCompletionV2Request
	if err := c.ShouldBindJSON(&v2Req); err != nil {
		r.sendErrorResponse(c, http.StatusBadRequest, "invalid_request", "请求参数格式错误", err.Error())
		return
	}

	utils.ChatInfo("收到V2聊天请求: query=%s, model_id=%s", v2Req.Query, v2Req.ModelID)

	// 转换为标准请求格式
	standardReq := r.convertV2ToStandardRequest(&v2Req)

	// 设置SSE响应头
	r.setSSEHeaders(c)

	// 处理流式响应
	if err := r.processStreamingResponse(c, standardReq, user, v2Req.ConversationID); err != nil {
		utils.ChatError("流式聊天完成处理失败: %v", err)
		errorEvent := response.NewErrorEvent("抱歉我现在还不能回答你的问题，换个话题再试试吧。")
		c.String(http.StatusOK, errorEvent.ToSSEString())
		return
	}

	utils.ChatInfo("用户 %s V2聊天请求处理成功", user.Id)
}

// convertV2ToStandardRequest 将V2请求转换为简化的标准请求格式
func (r *ChatApiRoutes) convertV2ToStandardRequest(v2Req *request.ChatCompletionV2Request) *request.ChatCompletionRequest {
	return &request.ChatCompletionRequest{
		Model:  v2Req.ModelID,
		Query:  v2Req.Query,
		ChatID: v2Req.ConversationID,
		// Features 留空，由需要时设置
	}
}

// processStreamingResponse 处理流式响应
func (r *ChatApiRoutes) processStreamingResponse(c *gin.Context, req *request.ChatCompletionRequest, user *entities.User, conversationID string) error {
	// 检查服务是否可用
	if r.chatService == nil {
		return utils.NewError("聊天服务未初始化")
	}

	// 转换DTO为domain model
	domainReq := r.convertDTOToDomain(req)

	// 调用聊天服务
	resp, err := r.chatService.ProcessChatCompletion(c.Request.Context(), domainReq, user)
	if err != nil {
		return utils.NewError("聊天服务处理失败: %v", err)
	}

	// 模拟流式输出
	return r.simulateStreamingOutput(c, resp, conversationID, user)
}

// simulateStreamingOutput 模拟流式输出
func (r *ChatApiRoutes) simulateStreamingOutput(c *gin.Context, resp *response.ChatCompletionResponse, conversationID string, user *entities.User) error {
	if len(resp.Choices) == 0 {
		return utils.NewError("响应内容为空")
	}

	content := resp.Choices[0].Message.Content
	chunkSize := 5
	runes := []rune(content)

	// 分块发送内容
	for i := 0; i < len(runes); i += chunkSize {
		end := i + chunkSize
		if end > len(runes) {
			end = len(runes)
		}

		chunk := string(runes[i:end])

		// 创建并发送APPEND事件
		appendEvent := &response.HKGAIChatAppendEvent{
			BaseMessageV2: response.BaseMessageV2{
				ID:             resp.ID,
				Content:        chunk,
				ModelName:      resp.Model,
				Role:           2,
				ConversationID: conversationID,
				Status:         5,
				Tools:          []response.Tool{},
				Inputs: map[string]interface{}{
					"temperature": "0.7",
					"language":    "zh-CN",
				},
				UsageMetadata: &response.UsageMetadata{
					OutputTokens: resp.Usage.CompletionTokens,
					TTFT:         0.8,
					TPOT:         0.05,
					Latency:      2.5,
					TPS:          20.0,
				},
			},
		}

		sseEvent := response.NewAppendEvent(appendEvent)
		c.String(http.StatusOK, sseEvent.ToSSEString())
		c.Writer.Flush()
		time.Sleep(50 * time.Millisecond)
	}

	// 发送FINISH事件
	finishEvent := &response.HKGAIChatFinishEvent{
		ID:             resp.ID,
		UserID:         user.Id,
		ConversationID: conversationID,
		Status:         1,
		Content:        content,
		Role:           "assistant",
		CreatedAt:      time.Now().Format(time.RFC3339),
		UpdatedAt:      time.Now().Format(time.RFC3339),
	}

	finishSSEEvent := response.NewFinishEvent(finishEvent)
	c.String(http.StatusOK, finishSSEEvent.ToSSEString())
	c.Writer.Flush()

	return nil
}

// setSSEHeaders 设置SSE响应头
func (r *ChatApiRoutes) setSSEHeaders(c *gin.Context) {
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Access-Control-Allow-Origin", "*")
	c.Header("Access-Control-Allow-Headers", "Cache-Control")
}

// sendErrorResponse 发送错误响应
func (r *ChatApiRoutes) sendErrorResponse(c *gin.Context, statusCode int, errorType, message, details string) {
	errorResp := response.ErrorResponse{
		Success: false,
		Error:   errorType,
		Message: message,
		Code:    string(rune(statusCode)),
		Details: details,
	}

	if details != "" {
		utils.ApiDebug("错误详情: %s", details)
	}

	c.JSON(statusCode, errorResp)
}

// RegisterRoutes 注册V2聊天路由
func (r *ChatApiRoutes) RegisterRoutes(router *gin.RouterGroup) {
	router.Use(r.authMiddleware.RequireUserAuth())
	router.POST("/chat_v2/completions", r.ChatCompletionV2)
	utils.ApiInfo("V2聊天API路由注册完成: /chat_v2/completions")
}

// convertDTOToDomain 将简化的DTO转换为domain model
func (r *ChatApiRoutes) convertDTOToDomain(req *request.ChatCompletionRequest) *domain.ChatRequest {
	// 将query转换为用户消息
	messages := []domain.ChatMessage{
		{
			Role:    "user",
			Content: req.Query,
		},
	}

	// 转换Features
	var features *domain.ChatFeatures
	if req.Features != nil {
		features = &domain.ChatFeatures{
			CodeInterpreter: req.Features.CodeInterpreter,
			ImageGeneration: req.Features.ImageGeneration,
			Memory:          req.Features.Memory,
			WebSearch:       req.Features.WebSearch,
		}
	}

	return &domain.ChatRequest{
		Model:    req.Model,
		Messages: messages,
		Query:    req.Query,
		ChatID:   req.ChatID,
		Features: features,
		// 设置默认值
		Stream:    false,
		SessionID: "", // 如果需要，可从上下文生成
	}
}