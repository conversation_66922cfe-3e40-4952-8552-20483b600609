package web_search

import (
	"bytes"
	"encoding/json"
	"fmt"
	"hkchat_api/internal/config"
	"hkchat_api/pkg/utils"
	"io"
	"net/http"
	"strings"
	"time"
)

// SearchResult 搜索结果结构
type SearchResult struct {
	Link    string `json:"link"`
	Title   string `json:"title"`
	Snippet string `json:"snippet"`
}

// SearchRequest Tavily搜索请求结构
type SearchRequest struct {
	Query         string   `json:"query"`
	MaxResults    int      `json:"max_results,omitempty"`
	FilterList    []string `json:"filter_list,omitempty"`
	IncludeImages bool     `json:"include_images,omitempty"`
	IncludeAnswer bool     `json:"include_answer,omitempty"`
}

// SearchResponse Tavily搜索响应结构
type SearchResponse struct {
	Query   string `json:"query"`
	Answer  string `json:"answer,omitempty"`
	Results []struct {
		URL     string  `json:"url"`
		Title   string  `json:"title"`
		Content string  `json:"content"`
		Score   float64 `json:"score,omitempty"`
	} `json:"results"`
	ResponseTime float64 `json:"response_time,omitempty"`
}

// Client Tavily 搜索客户端接口
type Client interface {
	// 执行搜索
	Search(req *SearchRequest) ([]*SearchResult, error)

	// 简单搜索（便捷方法）
	SimpleSearch(query string, maxResults int) ([]*SearchResult, error)

	// 带过滤的搜索
	SearchWithFilter(query string, maxResults int, filterList []string) ([]*SearchResult, error)

	// 健康检查
	HealthCheck() error
}

// client Tavily 搜索客户端实现
type client struct {
	config     *config.TavilyConfig
	httpClient *http.Client
}

// NewClient 创建 Tavily 搜索客户端实例
func NewClient() Client {
	cfg := config.GlobalConfig.Tavily

	// 设置默认值
	if cfg.BaseURL == "" {
		cfg.BaseURL = "https://api.tavily.com"
	}
	if cfg.MaxResults == 0 {
		cfg.MaxResults = 10
	}
	if cfg.Timeout == 0 {
		cfg.Timeout = 30
	}

	// 创建 HTTP 客户端
	httpClient := &http.Client{
		Timeout: time.Duration(cfg.Timeout) * time.Second,
	}

	utils.Info("🔍 Tavily 搜索客户端初始化完成")
	return &client{
		config:     &cfg,
		httpClient: httpClient,
	}
}

// Search 执行搜索
func (c *client) Search(req *SearchRequest) ([]*SearchResult, error) {
	utils.Infof("🔍 开始 Tavily 搜索: %s", req.Query)

	// 设置默认值
	if req.MaxResults == 0 {
		req.MaxResults = c.config.MaxResults
	}

	// 构建请求体
	reqBody, err := json.Marshal(req)
	if err != nil {
		utils.Errorf("❌ 序列化搜索请求失败: %v", err)
		return nil, fmt.Errorf("序列化搜索请求失败: %v", err)
	}

	// 创建 HTTP 请求
	url := fmt.Sprintf("%s/search", c.config.BaseURL)
	httpReq, err := http.NewRequest("POST", url, bytes.NewBuffer(reqBody))
	if err != nil {
		utils.Errorf("❌ 创建 HTTP 请求失败: %v", err)
		return nil, fmt.Errorf("创建 HTTP 请求失败: %v", err)
	}

	// 设置请求头
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", fmt.Sprintf("Bearer %s", c.config.APIKey))

	// 发送请求
	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		utils.Errorf("❌ 发送搜索请求失败: %v", err)
		return nil, fmt.Errorf("发送搜索请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		utils.Errorf("❌ 读取搜索响应失败: %v", err)
		return nil, fmt.Errorf("读取搜索响应失败: %v", err)
	}

	// 检查 HTTP 状态码
	if resp.StatusCode != http.StatusOK {
		utils.Errorf("❌ Tavily API 返回错误状态码: %d, 响应: %s", resp.StatusCode, string(respBody))
		return nil, fmt.Errorf("Tavily API 错误 (状态码: %d): %s", resp.StatusCode, string(respBody))
	}

	// 解析响应
	var searchResp SearchResponse
	if err := json.Unmarshal(respBody, &searchResp); err != nil {
		utils.Errorf("❌ 解析搜索响应失败: %v", err)
		return nil, fmt.Errorf("解析搜索响应失败: %v", err)
	}

	// 转换为搜索结果
	results := make([]*SearchResult, 0, len(searchResp.Results))
	for _, item := range searchResp.Results {
		result := &SearchResult{
			Link:    item.URL,
			Title:   item.Title,
			Snippet: item.Content,
		}
		results = append(results, result)
	}

	// 应用过滤器（如果有）
	if len(req.FilterList) > 0 {
		results = c.applyFilter(results, req.FilterList)
	}

	utils.Infof("✅ Tavily 搜索完成，返回 %d 条结果", len(results))
	return results, nil
}

// SimpleSearch 简单搜索（便捷方法）
func (c *client) SimpleSearch(query string, maxResults int) ([]*SearchResult, error) {
	req := &SearchRequest{
		Query:      query,
		MaxResults: maxResults,
	}
	return c.Search(req)
}

// SearchWithFilter 带过滤的搜索
func (c *client) SearchWithFilter(query string, maxResults int, filterList []string) ([]*SearchResult, error) {
	req := &SearchRequest{
		Query:      query,
		MaxResults: maxResults,
		FilterList: filterList,
	}
	return c.Search(req)
}

// HealthCheck 健康检查
func (c *client) HealthCheck() error {
	utils.Info("🔍 检查 Tavily 搜索服务连接状态...")

	// 简单的连接测试
	_, err := c.SimpleSearch("test", 1)
	if err != nil {
		utils.Errorf("❌ Tavily 搜索服务健康检查失败: %v", err)
		return err
	}

	utils.Info("✅ Tavily 搜索服务连接正常")
	return nil
}

// applyFilter 应用过滤器（参考 Python 版本的 get_filtered_results）
func (c *client) applyFilter(results []*SearchResult, filterList []string) []*SearchResult {
	if len(filterList) == 0 {
		return results
	}

	filtered := make([]*SearchResult, 0)

	for _, result := range results {
		shouldInclude := true

		// 检查是否包含过滤词（在标题或内容中）
		for _, filter := range filterList {
			filterLower := strings.ToLower(filter)
			titleLower := strings.ToLower(result.Title)
			snippetLower := strings.ToLower(result.Snippet)

			if strings.Contains(titleLower, filterLower) || strings.Contains(snippetLower, filterLower) {
				shouldInclude = false
				break
			}
		}

		if shouldInclude {
			filtered = append(filtered, result)
		}
	}

	utils.Infof("🔍 过滤器应用完成，从 %d 条结果过滤到 %d 条", len(results), len(filtered))
	return filtered
}
