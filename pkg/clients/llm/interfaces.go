package llm

import "context"

// Client LLM客户端通用接口（抽象）
type Client interface {
	// 聊天完成
	ChatCompletion(ctx context.Context, req *ChatRequest) (*ChatResponse, error)

	// 文本向量化
	CreateEmbedding(ctx context.Context, req *EmbeddingRequest) (*EmbeddingResponse, error)

	// 流式聊天完成
	ChatCompletionStream(ctx context.Context, req *ChatRequest) (<-chan StreamData, error)

	// 健康检查
	HealthCheck(ctx context.Context) error

	// 获取客户端信息
	GetClientInfo() ClientInfo
}

// ClientInfo 客户端信息
type ClientInfo struct {
	Provider     string            `json:"provider"`     // 提供商：openai, azure, claude等
	Version      string            `json:"version"`      // 客户端版本
	BaseURL      string            `json:"base_url"`     // 基础URL
	Models       []string          `json:"models"`       // 支持的模型列表
	Capabilities map[string]bool   `json:"capabilities"` // 功能支持：chat, embedding, stream等
	Metadata     map[string]string `json:"metadata"`     // 其他元数据
}

// StreamData 流式数据
type StreamData struct {
	Type    StreamType `json:"type"`
	Content string     `json:"content,omitempty"`
	Error   string     `json:"error,omitempty"`
	Done    bool       `json:"done"`
	Delta   *Delta     `json:"delta,omitempty"`
}

// StreamType 流式数据类型
type StreamType string

const (
	StreamTypeContent  StreamType = "content"
	StreamTypeError    StreamType = "error"
	StreamTypeComplete StreamType = "complete"
	StreamTypeDelta    StreamType = "delta"
)

// Delta 增量数据
type Delta struct {
	Role    string `json:"role,omitempty"`
	Content string `json:"content,omitempty"`
}

// Factory 客户端工厂接口
type Factory interface {
	// 创建客户端
	CreateClient(config Config) (Client, error)

	// 获取支持的提供商列表
	GetSupportedProviders() []string

	// 验证配置
	ValidateConfig(config Config) error
}
