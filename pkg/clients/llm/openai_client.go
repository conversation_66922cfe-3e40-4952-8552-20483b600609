package llm

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"hkchat_api/internal/config"
	"hkchat_api/pkg/utils"
	"io"
	"net/http"
	"strings"
	"time"
)

// OpenAIClient OpenAI 客户端实现
type OpenAIClient struct {
	baseURL    string
	apiKey     string
	model      string
	httpClient *http.Client
}

// NewOpenAIClient 创建新的 OpenAI 客户端
func NewOpenAIClient() Client {
	cfg := config.GlobalConfig.OpenAI
	return &OpenAIClient{
		baseURL: cfg.BaseURL,
		apiKey:  cfg.APIKey,
		model:   cfg.Model,
		httpClient: &http.Client{
			Timeout: time.Duration(cfg.Timeout) * time.Second,
		},
	}
}

// ChatCompletion 聊天完成
func (c *OpenAIClient) ChatCompletion(ctx context.Context, req *ChatRequest) (*ChatResponse, error) {
	utils.Debugf("发送聊天完成请求: 模型=%s, 消息数=%d", req.Model, len(req.Messages))

	// 构建请求数据
	requestData := map[string]interface{}{
		"model":    req.Model,
		"messages": req.Messages,
		"stream":   req.Stream,
	}

	// 设置可选参数
	if req.MaxTokens != nil {
		requestData["max_tokens"] = *req.MaxTokens
	}
	if req.Temperature != nil {
		requestData["temperature"] = *req.Temperature
	}
	if req.Tools != nil && len(req.Tools) > 0 {
		requestData["tools"] = req.Tools
		if req.ToolChoice != "" {
			requestData["tool_choice"] = req.ToolChoice
		}
	}

	// 序列化请求数据
	jsonData, err := json.Marshal(requestData)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %v", err)
	}

	// 打印请求数据（调试用）
	utils.Debugf("发送的请求数据: %s", string(jsonData))

	// 构建请求
	url := fmt.Sprintf("%s/chat/completions", strings.TrimRight(c.baseURL, "/"))
	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	// 设置请求头
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", "Bearer "+c.apiKey)

	// 发送请求
	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("发送HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	// 打印响应数据（调试用）
	utils.Debugf("接收到的响应数据: %s", string(body))

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP请求失败: %d, 响应: %s", resp.StatusCode, string(body))
	}

	// 解析响应
	var response ChatResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	utils.Debugf("聊天完成响应: ID=%s, 模型=%s, 选择数=%d", response.ID, response.Model, len(response.Choices))

	return &response, nil
}

// CreateEmbedding 创建向量嵌入
func (c *OpenAIClient) CreateEmbedding(ctx context.Context, req *EmbeddingRequest) (*EmbeddingResponse, error) {
	utils.Debugf("发送向量嵌入请求: 模型=%s, 输入数=%d", req.Model, len(req.Input))

	// 构建请求数据
	requestData := map[string]interface{}{
		"model": req.Model,
		"input": req.Input,
	}

	// 序列化请求数据
	jsonData, err := json.Marshal(requestData)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %v", err)
	}

	// 构建请求
	url := fmt.Sprintf("%s/embeddings", strings.TrimRight(c.baseURL, "/"))
	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	// 设置请求头
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", "Bearer "+c.apiKey)

	// 发送请求
	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("发送HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP请求失败: %d, 响应: %s", resp.StatusCode, string(body))
	}

	// 解析响应
	var response EmbeddingResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	return &response, nil
}

// ChatCompletionStream 流式聊天完成
func (c *OpenAIClient) ChatCompletionStream(ctx context.Context, req *ChatRequest) (<-chan StreamData, error) {
	utils.Debugf("发送流式聊天完成请求: 模型=%s", req.Model)

	// 构建请求数据
	requestData := map[string]interface{}{
		"model":    req.Model,
		"messages": req.Messages,
		"stream":   true,
	}

	// 设置可选参数
	if req.MaxTokens != nil {
		requestData["max_tokens"] = *req.MaxTokens
	}
	if req.Temperature != nil {
		requestData["temperature"] = *req.Temperature
	}

	// 序列化请求数据
	jsonData, err := json.Marshal(requestData)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %v", err)
	}

	// 构建请求
	url := fmt.Sprintf("%s/chat/completions", strings.TrimRight(c.baseURL, "/"))
	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	// 设置请求头
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", "Bearer "+c.apiKey)
	httpReq.Header.Set("Accept", "text/event-stream")

	// 发送请求
	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("发送HTTP请求失败: %v", err)
	}

	// 创建流式数据通道
	streamChan := make(chan StreamData, 10)

	// 在goroutine中处理流式响应
	go func() {
		defer resp.Body.Close()
		defer close(streamChan)

		// 检查HTTP状态码
		if resp.StatusCode != http.StatusOK {
			body, _ := io.ReadAll(resp.Body)
			streamChan <- StreamData{
				Type:  StreamTypeError,
				Error: fmt.Sprintf("HTTP请求失败: %d, 响应: %s", resp.StatusCode, string(body)),
				Done:  true,
			}
			return
		}

		// 读取流式响应
		buffer := make([]byte, 1024)
		for {
			n, err := resp.Body.Read(buffer)
			if err != nil {
				if err == io.EOF {
					streamChan <- StreamData{
						Type: StreamTypeComplete,
						Done: true,
					}
					break
				}
				streamChan <- StreamData{
					Type:  StreamTypeError,
					Error: fmt.Sprintf("读取流式响应失败: %v", err),
					Done:  true,
				}
				break
			}

			// 处理接收到的数据
			data := string(buffer[:n])
			streamChan <- StreamData{
				Type:    StreamTypeContent,
				Content: data,
				Done:    false,
			}
		}
	}()

	return streamChan, nil
}

// HealthCheck 健康检查
func (c *OpenAIClient) HealthCheck(ctx context.Context) error {
	utils.Debug("执行OpenAI客户端健康检查")

	// 发送简单的API调用测试连接
	req := &ChatRequest{
		Model: c.model,
		Messages: []ChatMessage{
			{Role: "user", Content: "test"},
		},
		MaxTokens: &[]int{1}[0],
	}

	_, err := c.ChatCompletion(ctx, req)
	if err != nil {
		return fmt.Errorf("健康检查失败: %v", err)
	}

	utils.Debug("OpenAI客户端健康检查通过")
	return nil
}

// GetClientInfo 获取客户端信息
func (c *OpenAIClient) GetClientInfo() ClientInfo {
	return ClientInfo{
		Provider: "openai",
		Version:  "1.0.0",
		BaseURL:  c.baseURL,
		Models:   []string{"gpt-3.5-turbo", "gpt-4", "gpt-4o", "HKGAI-V1"},
		Capabilities: map[string]bool{
			"chat":      true,
			"embedding": true,
			"stream":    true,
		},
		Metadata: map[string]string{
			"api_key_masked": c.getMaskedAPIKey(),
		},
	}
}

// getMaskedAPIKey 获取脱敏的API Key
func (c *OpenAIClient) getMaskedAPIKey() string {
	if len(c.apiKey) <= 8 {
		return "****"
	}
	return c.apiKey[:4] + "****" + c.apiKey[len(c.apiKey)-4:]
}
