package llm

import "time"

// Config 客户端配置
type Config struct {
	Provider    string            `json:"provider"`    // 提供商
	BaseURL     string            `json:"base_url"`    // 基础URL
	APIKey      string            `json:"api_key"`     // API密钥
	Model       string            `json:"model"`       // 默认模型
	MaxTokens   int               `json:"max_tokens"`  // 最大token数
	Temperature float32           `json:"temperature"` // 温度参数
	Timeout     time.Duration     `json:"timeout"`     // 超时时间
	Headers     map[string]string `json:"headers"`     // 自定义请求头
	Metadata    map[string]string `json:"metadata"`    // 其他元数据
}

// ChatRequest 聊天请求
type ChatRequest struct {
	Messages    []ChatMessage     `json:"messages"`              // 消息列表
	Model       string            `json:"model,omitempty"`       // 模型名称
	MaxTokens   *int              `json:"max_tokens,omitempty"`  // 最大token数
	Temperature *float32          `json:"temperature,omitempty"` // 温度参数
	Stream      bool              `json:"stream,omitempty"`      // 是否流式
	Tools       []Tool            `json:"tools,omitempty"`       // 工具列表
	ToolChoice  string            `json:"tool_choice,omitempty"` // 工具选择
	Metadata    map[string]string `json:"metadata,omitempty"`    // 元数据
}

// ChatResponse 聊天响应
type ChatResponse struct {
	ID      string       `json:"id"`
	Object  string       `json:"object"`
	Created int64        `json:"created"`
	Model   string       `json:"model"`
	Choices []ChatChoice `json:"choices"`
	Usage   Usage        `json:"usage"`
}

// ChatMessage 聊天消息
type ChatMessage struct {
	Role       string     `json:"role"`                   // 角色：system, user, assistant, tool
	Content    string     `json:"content,omitempty"`      // 文本内容
	Name       string     `json:"name,omitempty"`         // 发送者名称
	ToolCalls  []ToolCall `json:"tool_calls,omitempty"`   // 工具调用
	ToolCallID string     `json:"tool_call_id,omitempty"` // 工具调用ID
}

// ChatChoice 聊天选择
type ChatChoice struct {
	Index        int         `json:"index"`
	Message      ChatMessage `json:"message"`
	FinishReason string      `json:"finish_reason"`
}

// Tool 工具定义
type Tool struct {
	Type     string   `json:"type"`     // 工具类型：function
	Function Function `json:"function"` // 函数定义
}

// Function 函数定义
type Function struct {
	Name        string                 `json:"name"`        // 函数名
	Description string                 `json:"description"` // 函数描述
	Parameters  map[string]interface{} `json:"parameters"`  // 参数schema
}

// ToolCall 工具调用
type ToolCall struct {
	ID       string       `json:"id"`       // 调用ID
	Type     string       `json:"type"`     // 类型：function
	Function FunctionCall `json:"function"` // 函数调用
}

// FunctionCall 函数调用
type FunctionCall struct {
	Name      string `json:"name"`      // 函数名
	Arguments string `json:"arguments"` // 参数JSON字符串
}

// EmbeddingRequest 向量化请求
type EmbeddingRequest struct {
	Input    []string          `json:"input"`              // 输入文本列表
	Model    string            `json:"model,omitempty"`    // 模型名称
	Metadata map[string]string `json:"metadata,omitempty"` // 元数据
}

// EmbeddingResponse 向量化响应
type EmbeddingResponse struct {
	Object string            `json:"object"`
	Data   []EmbeddingResult `json:"data"`
	Model  string            `json:"model"`
	Usage  Usage             `json:"usage"`
}

// EmbeddingResult 向量化结果
type EmbeddingResult struct {
	Object    string    `json:"object"`
	Index     int       `json:"index"`
	Embedding []float64 `json:"embedding"`
}

// Usage 使用统计
type Usage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens,omitempty"`
	TotalTokens      int `json:"total_tokens"`
}

// Error 错误信息
type Error struct {
	Type    string `json:"type"`
	Code    string `json:"code"`
	Message string `json:"message"`
	Param   string `json:"param,omitempty"`
}

// ErrorResponse 错误响应
type ErrorResponse struct {
	Error Error `json:"error"`
}
