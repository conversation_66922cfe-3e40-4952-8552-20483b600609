package database

import "fmt"

// formatDSN 格式化数据库连接字符串
func formatDSN(host string, port int, user, password, dbname, sslmode, timezone string) string {
	return fmt.Sprintf(
		"host=%s user=%s password=%s dbname=%s port=%d sslmode=%s TimeZone=%s",
		host, user, password, dbname, port, sslmode, timezone,
	)
}

// GetLogLevel 根据字符串获取GORM日志级别
func GetLogLevel(level string) string {
	switch level {
	case "silent", "error", "warn", "info":
		return level
	default:
		return "warn"
	}
}
