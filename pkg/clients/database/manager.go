package database

import (
	"fmt"
	"sync"

	"hkchat_api/pkg/utils"

	"gorm.io/gorm"
)

// Manager 数据库管理器
type Manager struct {
	client *Client
	mu     sync.RWMutex
}

// 全局数据库管理器
var (
	globalManager *Manager
	once          sync.Once
)

// InitManager 初始化全局数据库管理器
func InitManager(config *Config) error {
	var err error
	once.Do(func() {
		client, clientErr := NewClient(config)
		if clientErr != nil {
			err = clientErr
			return
		}

		globalManager = &Manager{
			client: client,
		}

		utils.GetModuleLogger("database").Debug("全局数据库管理器初始化完成")
	})

	return err
}

// GetManager 获取全局数据库管理器
func GetManager() *Manager {
	if globalManager == nil {
		panic("数据库管理器未初始化，请先调用 InitManager()")
	}
	return globalManager
}

// GetDB 获取数据库实例
func (m *Manager) GetDB() *gorm.DB {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if m.client == nil {
		return nil
	}
	return m.client.GetDB()
}

// GetClient 获取数据库客户端
func (m *Manager) GetClient() *Client {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.client
}

// AutoMigrate 自动迁移表结构
func (m *Manager) AutoMigrate(models ...interface{}) error {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if m.client == nil {
		return fmt.Errorf("数据库客户端未初始化")
	}

	return m.client.AutoMigrate(models...)
}

// TestConnection 测试数据库连接
func (m *Manager) TestConnection() error {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if m.client == nil {
		return fmt.Errorf("数据库客户端未初始化")
	}

	return m.client.TestConnection()
}

// GetStats 获取数据库统计信息
func (m *Manager) GetStats() (map[string]interface{}, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if m.client == nil {
		return nil, fmt.Errorf("数据库客户端未初始化")
	}

	return m.client.GetStats()
}

// Close 关闭数据库连接
func (m *Manager) Close() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.client == nil {
		return nil
	}

	err := m.client.Close()
	m.client = nil
	return err
}

// === 全局便捷方法 ===

// GetGlobalDB 获取全局数据库实例
func GetGlobalDB() *gorm.DB {
	return GetManager().GetDB()
}

// AutoMigrateGlobal 全局自动迁移表结构
func AutoMigrateGlobal(models ...interface{}) error {
	return GetManager().AutoMigrate(models...)
}

// TestConnectionGlobal 全局测试数据库连接
func TestConnectionGlobal() error {
	return GetManager().TestConnection()
}

// GetStatsGlobal 全局获取数据库统计信息
func GetStatsGlobal() (map[string]interface{}, error) {
	return GetManager().GetStats()
}

// CloseGlobal 全局关闭数据库连接
func CloseGlobal() error {
	return GetManager().Close()
}
