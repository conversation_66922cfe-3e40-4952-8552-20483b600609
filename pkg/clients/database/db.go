package database

import (
	"context"
	"fmt"
	"time"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"hkchat_api/pkg/utils"
)

// CustomLogger 自定义GORM日志器
type CustomLogger struct {
	logger.Interface
	LogLevel logger.LogLevel
}

// NewCustomLogger 创建自定义日志器
func NewCustomLogger(logLevel string) *CustomLogger {
	var gormLogLevel logger.LogLevel

	// 根据应用日志级别决定GORM日志级别
	switch logLevel {
	case "debug":
		gormLogLevel = logger.Info // debug模式下显示所有SQL
	case "info":
		gormLogLevel = logger.Error // info模式下只显示错误
	case "warn":
		gormLogLevel = logger.Error // warn模式下只显示错误
	case "error":
		gormLogLevel = logger.Error // error模式下只显示错误
	default:
		gormLogLevel = logger.Silent // 其他情况静默
	}

	return &CustomLogger{
		Interface: logger.Default,
		LogLevel:  gormLogLevel,
	}
}

// LogMode 设置日志模式
func (l *CustomLogger) LogMode(level logger.LogLevel) logger.Interface {
	return &CustomLogger{
		Interface: l.Interface,
		LogLevel:  level,
	}
}

// Info 信息日志
func (l *CustomLogger) Info(ctx context.Context, msg string, data ...interface{}) {
	if l.LogLevel >= logger.Info {
		utils.GetModuleLogger("database").Debugf(msg, data...)
	}
}

// Warn 警告日志
func (l *CustomLogger) Warn(ctx context.Context, msg string, data ...interface{}) {
	if l.LogLevel >= logger.Warn {
		utils.GetModuleLogger("database").Warnf(msg, data...)
	}
}

// Error 错误日志
func (l *CustomLogger) Error(ctx context.Context, msg string, data ...interface{}) {
	if l.LogLevel >= logger.Error {
		utils.GetModuleLogger("database").Errorf(msg, data...)
	}
}

// Trace SQL跟踪日志
func (l *CustomLogger) Trace(ctx context.Context, begin time.Time, fc func() (sql string, rowsAffected int64), err error) {
	if l.LogLevel <= logger.Silent {
		return
	}

	elapsed := time.Since(begin)
	sql, rows := fc()

	if err != nil {
		// 记录错误
		if l.LogLevel >= logger.Error {
			utils.GetModuleLogger("database").Errorf("SQL执行失败 [%v] %s (行数: %d)", elapsed, sql, rows)
		}
	} else {
		// 记录成功执行的SQL
		if l.LogLevel >= logger.Info {
			if elapsed > 200*time.Millisecond {
				// 慢查询警告
				utils.GetModuleLogger("database").Warnf("慢查询检测 [%v] %s (行数: %d)", elapsed, sql, rows)
			} else {
				utils.GetModuleLogger("database").Debugf("SQL执行 [%v] %s (行数: %d)", elapsed, sql, rows)
			}
		}
	}
}

// Client 数据库客户端
type Client struct {
	DB *gorm.DB
}

// NewClient 创建数据库客户端
func NewClient(config *Config) (*Client, error) {
	utils.GetModuleLogger("database").Info("初始化数据库客户端...")

	if config == nil {
		return nil, fmt.Errorf("配置不能为空")
	}

	// 验证配置
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("配置验证失败: %w", err)
	}

	// 获取数据库连接字符串
	dsn := config.DSN()

	// 创建自定义日志器
	customLogger := NewCustomLogger(config.LogLevel)

	// 连接数据库
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: customLogger,
	})
	if err != nil {
		return nil, fmt.Errorf("%w: %v", ErrConnectionFailed, err)
	}

	// 配置连接池
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("获取数据库实例失败: %v", err)
	}

	sqlDB.SetMaxOpenConns(config.MaxOpenConn)
	sqlDB.SetMaxIdleConns(config.MaxIdleConn)
	sqlDB.SetConnMaxLifetime(time.Hour)

	// 测试连接
	if err := sqlDB.Ping(); err != nil {
		return nil, fmt.Errorf("数据库连接测试失败: %v", err)
	}

	utils.GetModuleLogger("database").Infof("数据库连接成功: %s@%s:%d/%s",
		config.User,
		config.Host,
		config.Port,
		config.DBName)
	utils.GetModuleLogger("database").Debugf("连接池配置: 最大连接数=%d, 最大空闲连接数=%d",
		config.MaxOpenConn, config.MaxIdleConn)

	return &Client{DB: db}, nil
}

// GetDB 获取数据库实例
func (c *Client) GetDB() *gorm.DB {
	return c.DB
}

// AutoMigrate 自动迁移表结构
func (c *Client) AutoMigrate(models ...interface{}) error {
	utils.GetModuleLogger("database").Debug("开始数据库表结构同步...")

	err := c.DB.AutoMigrate(models...)
	if err != nil {
		return fmt.Errorf("数据库表结构同步失败: %v", err)
	}

	utils.GetModuleLogger("database").Infof("数据库表结构同步完成! (共 %d 个模型)", len(models))
	return nil
}

// TestConnection 测试数据库连接
func (c *Client) TestConnection() error {
	if c.DB == nil {
		return fmt.Errorf("数据库连接未初始化")
	}

	sqlDB, err := c.DB.DB()
	if err != nil {
		return fmt.Errorf("获取底层数据库连接失败: %v", err)
	}

	if err := sqlDB.Ping(); err != nil {
		return fmt.Errorf("数据库连接测试失败: %v", err)
	}

	return nil
}

// GetStats 获取数据库连接统计信息
func (c *Client) GetStats() (map[string]interface{}, error) {
	if c.DB == nil {
		return nil, fmt.Errorf("数据库连接未初始化")
	}

	sqlDB, err := c.DB.DB()
	if err != nil {
		return nil, fmt.Errorf("获取底层数据库连接失败: %v", err)
	}

	stats := sqlDB.Stats()
	return map[string]interface{}{
		"open_connections":     stats.OpenConnections,
		"in_use":               stats.InUse,
		"idle":                 stats.Idle,
		"wait_count":           stats.WaitCount,
		"wait_duration":        stats.WaitDuration.String(),
		"max_idle_closed":      stats.MaxIdleClosed,
		"max_idle_time_closed": stats.MaxIdleTimeClosed,
		"max_lifetime_closed":  stats.MaxLifetimeClosed,
	}, nil
}

// Close 关闭数据库连接
func (c *Client) Close() error {
	if c.DB == nil {
		return nil
	}

	sqlDB, err := c.DB.DB()
	if err != nil {
		return err
	}

	utils.GetModuleLogger("database").Info("关闭数据库连接...")
	return sqlDB.Close()
}
