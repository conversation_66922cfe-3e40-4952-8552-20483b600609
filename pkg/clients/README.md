# OpenAI 客户端使用说明

## 概述

OpenAI 客户端是一个全局工具，封装了对 OpenAI API 的 HTTP 调用。它通过依赖注入容器管理，可以在任何服务中使用。

## 目录结构

```
pkg/clients/
├── openai_client.go    # OpenAI 客户端实现
├── registry.go         # 客户端注册器
└── README.md          # 使用说明
```

## 配置

在配置文件中添加 OpenAI 相关配置：

```yaml
openai:
  api_key: "${OPENAI_API_KEY}"  # 从环境变量读取
  base_url: "https://api.openai.com/v1"
  model: "gpt-3.5-turbo"
  max_tokens: 1000
  temperature: 0.7
  timeout: 30
```

环境变量设置：
```bash
export OPENAI_API_KEY="your-api-key-here"
```

## 在服务中使用

### 1. 通过依赖注入获取客户端

```go
// 在服务构造函数中注入
type chatService struct {
    openaiClient clients.OpenAIClient
}

func NewChatService(openaiClient clients.OpenAIClient) interfaces.ChatService {
    return &chatService{
        openaiClient: openaiClient,
    }
}
```

### 2. 调用聊天接口

```go
func (s *chatService) SendMessage(req *interfaces.ChatRequest) (*interfaces.ChatResponse, error) {
    // 构建请求
    openaiReq := &clients.ChatRequest{
        Messages: []clients.ChatMessage{
            {
                Role:    "user",
                Content: req.Message,
            },
        },
    }

    // 调用 OpenAI
    resp, err := s.openaiClient.ChatCompletion(openaiReq)
    if err != nil {
        return nil, err
    }

    // 处理响应
    return &interfaces.ChatResponse{
        AIResponse: resp.Choices[0].Message.Content,
        TokensUsed: resp.Usage.TotalTokens,
    }, nil
}
```

### 3. 调用向量化接口

```go
func (s *someService) CreateEmbedding(texts []string) (*clients.EmbeddingResponse, error) {
    req := &clients.EmbeddingRequest{
        Input: texts,
        Model: "text-embedding-ada-002",
    }
    
    return s.openaiClient.CreateEmbedding(req)
}
```

### 4. 流式聊天

```go
func (s *chatService) StreamChat(req *interfaces.ChatRequest) (<-chan string, error) {
    openaiReq := &clients.ChatRequest{
        Messages: []clients.ChatMessage{
            {Role: "user", Content: req.Message},
        },
    }
    
    return s.openaiClient.ChatCompletionStream(openaiReq)
}
```

## 接口定义

### OpenAIClient 接口

```go
type OpenAIClient interface {
    // 聊天完成
    ChatCompletion(req *ChatRequest) (*ChatResponse, error)
    
    // 文本向量化
    CreateEmbedding(req *EmbeddingRequest) (*EmbeddingResponse, error)
    
    // 流式聊天完成
    ChatCompletionStream(req *ChatRequest) (<-chan string, error)
    
    // 健康检查
    HealthCheck() error
}
```

## 错误处理

客户端会处理常见的错误情况：

1. **网络错误**：连接超时、网络不可达等
2. **API 错误**：OpenAI API 返回的错误信息
3. **配置错误**：API Key 无效、配置缺失等

错误信息会通过日志记录，并返回友好的错误提示。

## 最佳实践

1. **API Key 安全**：始终通过环境变量设置 API Key，不要硬编码
2. **错误处理**：总是检查返回的错误，并提供合适的回退机制
3. **Token 管理**：关注 Token 使用量，避免超出配额
4. **请求限制**：遵守 OpenAI 的请求频率限制
5. **日志记录**：重要操作都会记录日志，便于调试和监控

## 扩展

如果需要添加其他 AI 服务（如 Claude、Gemini 等），可以：

1. 在 `clients` 目录下创建对应的客户端
2. 在 `registry.go` 中注册新客户端
3. 在服务中通过依赖注入使用

这样的设计保证了代码的可扩展性和可维护性。 