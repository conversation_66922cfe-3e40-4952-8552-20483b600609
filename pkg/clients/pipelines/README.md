# Pipeline 客户端

这个包提供了与 Open WebUI 兼容的管道客户端实现，模仿了 Python 版本的 `pipelines.py` 功能。

## 功能特性

### 管道过滤器处理
- **入口过滤器 (Inlet Filter)**: 在消息发送给 LLM 之前处理和修改请求
- **出口过滤器 (Outlet Filter)**: 在 LLM 响应返回给用户之前处理和修改响应
- **优先级排序**: 支持多个过滤器按优先级顺序处理
- **模型特定过滤器**: 支持针对特定模型或通用(`*`)的过滤器

### 管道管理
- **获取管道列表**: 列出所有可用的管道服务器
- **上传管道**: 上传 Python (.py) 管道文件
- **添加管道**: 通过 URL 添加远程管道
- **删除管道**: 移除指定的管道

### 管道阀门 (Valves) 管理
- **获取阀门**: 获取管道的当前配置
- **获取阀门规格**: 获取管道支持的配置选项
- **更新阀门**: 修改管道的配置参数

## 配置获取

与 Python 版本不同，Go 版本**从数据库**获取配置信息，而不是环境变量：

```go
// 从数据库配置表获取 OPENAI_API_BASE_URLS
baseURLs, err := c.getOpenAIBaseURLs()

// 从数据库配置表获取 OPENAI_API_KEYS  
apiKeys, err := c.getOpenAIAPIKeys()
```

### 数据库配置结构

配置存储在 `config` 表的 JSONB 字段中：

```json
{
  "OPENAI_API_BASE_URLS": [
    "https://api.openai.com/v1",
    "https://your-pipeline-server.com"
  ],
  "OPENAI_API_KEYS": [
    "sk-xxx",
    "pipeline-key-xxx"
  ]
}
```

## 使用方式

### 1. 创建客户端实例

```go
import (
    "hkchat_api/pkg/clients/pipelines"
    "hkchat_api/internal/repository/interfaces"
)

// 需要配置仓库依赖
func NewPipelineClient(configRepo interfaces.ConfigRepository) pipelines.PipelineClient {
    return pipelines.NewPipelineClient(configRepo)
}
```

### 2. 在 ChatService 中使用

```go
// 处理入口过滤器 (在发送给 LLM 之前)
processedReq, err := pipelineClient.ProcessInletFilter(req, user, models)

// 处理出口过滤器 (在返回给用户之前)  
processedReq, err := pipelineClient.ProcessOutletFilter(req, user, models)
```

### 3. 管道管理

```go
// 获取管道列表
pipelines, err := client.GetPipelinesList()

// 上传管道文件
result, err := client.UploadPipeline(&UploadPipelineRequest{
    URLIdx: 0,
    File:   fileHeader,
})

// 获取管道阀门
valves, err := client.GetPipelineValves("pipeline-id", 0)
```

## 与 Python 版本的对比

| 功能 | Python 版本 | Go 版本 |
|------|-------------|---------|
| 配置获取 | `request.app.state.config.OPENAI_API_BASE_URLS` | `c.getOpenAIBaseURLs()` |
| 配置来源 | PersistentConfig (数据库) | ConfigRepository (数据库) |
| 过滤器处理 | `process_pipeline_inlet_filter` | `ProcessInletFilter` |
| 错误处理 | 异常 | error 返回值 |
| HTTP 客户端 | aiohttp | net/http |

## 过滤器处理流程

```mermaid
graph TD
    A[用户请求] --> B[获取排序后的过滤器]
    B --> C[处理每个过滤器]
    C --> D[发送HTTP请求到过滤器]
    D --> E[解析响应并更新请求]
    E --> F[继续下一个过滤器]
    F --> G[返回处理后的请求]
```

## 错误处理

- **配置获取失败**: 返回错误，但不会中断整个聊天流程
- **过滤器处理失败**: 记录错误日志，继续处理其他过滤器
- **网络请求失败**: 返回详细的错误信息

## 依赖注入

Pipeline 客户端通过依赖注入系统自动注册：

```go
// 在 clients/registry.go 中
{
    name:        "PipelineClient",
    clientFunc:  func() interface{} { return pipelines.NewPipelineClient(configRepo) },
    clientIface: (*pipelines.PipelineClient)(nil),
}
```

## 健康检查

客户端提供健康检查功能，验证所有配置的管道服务器是否可用：

```go
err := client.HealthCheck()
if err != nil {
    // 处理健康检查失败
}
``` 