package pipelines

import (
	"bytes"
	"encoding/json"
	"fmt"
	"hkchat_api/internal/models/domain"
	"hkchat_api/internal/core/interfaces"
	"hkchat_api/pkg/utils"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"sort"
	"time"
)

// PipelineClient 管道客户端接口
type PipelineClient interface {
	// 过滤器处理
	ProcessInletFilter(req *domain.ChatRequest, user *PipelineUser, models map[string]interface{}) (*domain.ChatRequest, error)
	ProcessOutletFilter(req *domain.ChatRequest, user *PipelineUser, models map[string]interface{}) (*domain.ChatRequest, error)

	// 管道列表管理
	GetPipelinesList() (*PipelineListResponse, error)
	GetPipelines(urlIdx int) (*PipelineResponse, error)

	// 管道 CRUD 操作
	UploadPipeline(req *UploadPipelineRequest) (*PipelineResponse, error)
	AddPipeline(req *AddPipelineRequest) (*PipelineResponse, error)
	DeletePipeline(req *DeletePipelineRequest) (*PipelineResponse, error)

	// 管道阀门管理
	GetPipelineValves(pipelineID string, urlIdx int) (*PipelineValvesResponse, error)
	GetPipelineValvesSpec(pipelineID string, urlIdx int) (*PipelineValvesResponse, error)
	UpdatePipelineValves(req *PipelineValvesRequest) (*PipelineResponse, error)

	// 健康检查
	HealthCheck() error
}

// pipelineClient 管道客户端实现
type pipelineClient struct {
	configRepo interfaces.ConfigRepository
	httpClient *http.Client
	timeout    time.Duration
}

// NewPipelineClient 创建管道客户端实例
func NewPipelineClient(configRepo interfaces.ConfigRepository) PipelineClient {
	// 创建 HTTP 客户端
	httpClient := &http.Client{
		Timeout: 30 * time.Second, // 默认超时时间
	}

	utils.Info("🔧 管道客户端初始化完成")
	return &pipelineClient{
		configRepo: configRepo,
		httpClient: httpClient,
		timeout:    30 * time.Second,
	}
}

// getConfigValue 获取配置值
func (c *pipelineClient) getConfigValue(key string) (interface{}, error) {
	utils.Debugf("获取配置值: %s", key)

	// 查询最新配置
	config, err := c.configRepo.GetLatestConfig()
	if err != nil {
		return nil, fmt.Errorf("获取配置失败: %v", err)
	}

	// 使用JSON数据（已经是map[string]interface{}类型）
	configData := map[string]interface{}(config.Data)

	value, exists := configData[key]
	if !exists {
		return nil, fmt.Errorf("配置项 %s 不存在", key)
	}

	return value, nil
}

// getOpenAIBaseURLs 获取OpenAI基础URL列表
func (c *pipelineClient) getOpenAIBaseURLs() ([]string, error) {
	value, err := c.getConfigValue("OPENAI_API_BASE_URLS")
	if err != nil {
		return nil, err
	}

	// 转换为字符串切片
	switch v := value.(type) {
	case []interface{}:
		urls := make([]string, len(v))
		for i, url := range v {
			if urlStr, ok := url.(string); ok {
				urls[i] = urlStr
			} else {
				return nil, fmt.Errorf("无效的URL格式: %v", url)
			}
		}
		return urls, nil
	case []string:
		return v, nil
	default:
		return nil, fmt.Errorf("无效的OPENAI_API_BASE_URLS格式: %T", v)
	}
}

// getOpenAIAPIKeys 获取OpenAI API密钥列表
func (c *pipelineClient) getOpenAIAPIKeys() ([]string, error) {
	value, err := c.getConfigValue("OPENAI_API_KEYS")
	if err != nil {
		return nil, err
	}

	// 转换为字符串切片
	switch v := value.(type) {
	case []interface{}:
		keys := make([]string, len(v))
		for i, key := range v {
			if keyStr, ok := key.(string); ok {
				keys[i] = keyStr
			} else {
				return nil, fmt.Errorf("无效的API密钥格式: %v", key)
			}
		}
		return keys, nil
	case []string:
		return v, nil
	default:
		return nil, fmt.Errorf("无效的OPENAI_API_KEYS格式: %T", v)
	}
}

// getSortedFilters 获取排序后的过滤器
func (c *pipelineClient) getSortedFilters(modelID string, models map[string]interface{}) []*FilterSpec {
	var filters []*FilterSpec

	for _, model := range models {
		if modelMap, ok := model.(map[string]interface{}); ok {
			if pipeline, exists := modelMap["pipeline"]; exists {
				if pipelineMap, ok := pipeline.(map[string]interface{}); ok {
					if pipelineType, exists := pipelineMap["type"]; exists && pipelineType == "filter" {
						// 检查是否应用于目标模型
						if pipelines, exists := pipelineMap["pipelines"]; exists {
							if pipelineSlice, ok := pipelines.([]interface{}); ok {
								shouldApply := false
								for _, p := range pipelineSlice {
									if p == "*" || p == modelID {
										shouldApply = true
										break
									}
								}
								if shouldApply {
									filter := &FilterSpec{
										ID:       fmt.Sprintf("%v", modelMap["id"]),
										Type:     "filter",
										Priority: 0,
										Pipeline: pipelineMap,
									}
									if priority, exists := pipelineMap["priority"]; exists {
										if p, ok := priority.(int); ok {
											filter.Priority = p
										}
									}
									// 获取urlIdx
									if urlIdx, exists := modelMap["urlIdx"]; exists {
										if idx, ok := urlIdx.(int); ok {
											filter.URLIdx = idx
										}
									}
									filters = append(filters, filter)
								}
							}
						}
					}
				}
			}
		}
	}

	// 按优先级排序
	sort.Slice(filters, func(i, j int) bool {
		return filters[i].Priority < filters[j].Priority
	})

	return filters
}

// ProcessInletFilter 处理入口过滤器
func (c *pipelineClient) ProcessInletFilter(req *domain.ChatRequest, user *PipelineUser, models map[string]interface{}) (*domain.ChatRequest, error) {
	utils.Info("🔧 开始处理入口过滤器...")

	if req == nil || user == nil {
		return req, fmt.Errorf("请求或用户信息不能为空")
	}

	sortedFilters := c.getSortedFilters(req.Model, models)

	// 如果模型本身也是管道，添加到过滤器列表
	if model, exists := models[req.Model]; exists {
		if modelMap, ok := model.(map[string]interface{}); ok {
			if pipeline, exists := modelMap["pipeline"]; exists {
				filter := &FilterSpec{
					ID:       req.Model,
					Type:     "filter",
					Priority: 999, // 模型管道优先级最高
					Pipeline: pipeline.(map[string]interface{}),
				}
				// 获取urlIdx
				if urlIdx, exists := modelMap["urlIdx"]; exists {
					if idx, ok := urlIdx.(int); ok {
						filter.URLIdx = idx
					}
				}
				sortedFilters = append(sortedFilters, filter)
			}
		}
	}

	// 处理每个过滤器
	processedReq := req
	for _, filter := range sortedFilters {
		if err := c.processFilter(filter, "inlet", &processedReq, user); err != nil {
			utils.Errorf("❌ 处理入口过滤器失败: %v", err)
			// 继续处理其他过滤器，不中断
		}
	}

	utils.Info("✅ 入口过滤器处理完成")
	return processedReq, nil
}

// ProcessOutletFilter 处理出口过滤器
func (c *pipelineClient) ProcessOutletFilter(req *domain.ChatRequest, user *PipelineUser, models map[string]interface{}) (*domain.ChatRequest, error) {
	utils.Info("🔧 开始处理出口过滤器...")

	if req == nil || user == nil {
		return req, fmt.Errorf("请求或用户信息不能为空")
	}

	sortedFilters := c.getSortedFilters(req.Model, models)

	// 如果模型本身也是管道，添加到过滤器列表（出口处理时顺序相反）
	if model, exists := models[req.Model]; exists {
		if modelMap, ok := model.(map[string]interface{}); ok {
			if pipeline, exists := modelMap["pipeline"]; exists {
				filter := &FilterSpec{
					ID:       req.Model,
					Type:     "filter",
					Priority: 0, // 模型管道优先级最低
					Pipeline: pipeline.(map[string]interface{}),
				}
				// 获取urlIdx
				if urlIdx, exists := modelMap["urlIdx"]; exists {
					if idx, ok := urlIdx.(int); ok {
						filter.URLIdx = idx
					}
				}
				sortedFilters = append([]*FilterSpec{filter}, sortedFilters...)
			}
		}
	}

	// 处理每个过滤器
	processedReq := req
	for _, filter := range sortedFilters {
		if err := c.processFilter(filter, "outlet", &processedReq, user); err != nil {
			utils.Errorf("❌ 处理出口过滤器失败: %v", err)
			// 继续处理其他过滤器，不中断
		}
	}

	utils.Info("✅ 出口过滤器处理完成")
	return processedReq, nil
}

// processFilter 处理单个过滤器
func (c *pipelineClient) processFilter(filter *FilterSpec, filterType string, req **domain.ChatRequest, user *PipelineUser) error {
	// 获取配置
	baseURLs, err := c.getOpenAIBaseURLs()
	if err != nil {
		return fmt.Errorf("获取基础URL配置失败: %v", err)
	}

	apiKeys, err := c.getOpenAIAPIKeys()
	if err != nil {
		return fmt.Errorf("获取API密钥配置失败: %v", err)
	}

	// 获取过滤器配置
	urlIdx := filter.URLIdx
	if urlIdx >= len(baseURLs) {
		return fmt.Errorf("无效的 URL 索引: %d", urlIdx)
	}

	baseURL := baseURLs[urlIdx]
	apiKey := ""
	if urlIdx < len(apiKeys) {
		apiKey = apiKeys[urlIdx]
	}

	if apiKey == "" {
		utils.Warnf("⚠️ 过滤器 %s 缺少 API 密钥", filter.ID)
		return nil
	}

	// 构建请求
	requestData := &PipelineRequest{
		User: user,
		Body: *req,
	}

	jsonData, err := json.Marshal(requestData)
	if err != nil {
		return fmt.Errorf("序列化请求数据失败: %v", err)
	}

	// 发送请求
	url := fmt.Sprintf("%s/%s/filter/%s", baseURL, filter.ID, filterType)
	httpReq, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("创建 HTTP 请求失败: %v", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", fmt.Sprintf("Bearer %s", apiKey))

	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return fmt.Errorf("发送 HTTP 请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 处理响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应数据失败: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("过滤器返回错误状态码: %d, 响应: %s", resp.StatusCode, string(respBody))
	}

	// 解析响应并更新请求
	var updatedReq domain.ChatRequest
	if err := json.Unmarshal(respBody, &updatedReq); err != nil {
		return fmt.Errorf("解析响应数据失败: %v", err)
	}

	*req = &updatedReq
	return nil
}

// GetPipelinesList 获取管道列表
func (c *pipelineClient) GetPipelinesList() (*PipelineListResponse, error) {
	utils.Info("📋 获取管道列表...")

	// 获取配置
	baseURLs, err := c.getOpenAIBaseURLs()
	if err != nil {
		return nil, fmt.Errorf("获取基础URL配置失败: %v", err)
	}

	var serverInfos []PipelineServerInfo

	// 检查每个配置的服务器
	for idx, baseURL := range baseURLs {
		// 简单检查是否支持管道
		if c.checkPipelineSupport(baseURL, idx) {
			serverInfos = append(serverInfos, PipelineServerInfo{
				URL: baseURL,
				Idx: idx,
			})
		}
	}

	return &PipelineListResponse{
		Data: serverInfos,
	}, nil
}

// checkPipelineSupport 检查服务器是否支持管道
func (c *pipelineClient) checkPipelineSupport(baseURL string, idx int) bool {
	// 获取API密钥
	apiKeys, err := c.getOpenAIAPIKeys()
	if err != nil {
		return false
	}

	apiKey := ""
	if idx < len(apiKeys) {
		apiKey = apiKeys[idx]
	}

	if apiKey == "" {
		return false
	}

	req, err := http.NewRequest("GET", fmt.Sprintf("%s/pipelines", baseURL), nil)
	if err != nil {
		return false
	}

	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", apiKey))

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return false
	}
	defer resp.Body.Close()

	return resp.StatusCode == http.StatusOK
}

// GetPipelines 获取管道
func (c *pipelineClient) GetPipelines(urlIdx int) (*PipelineResponse, error) {
	utils.Infof("📋 获取管道列表 (索引: %d)...", urlIdx)

	// 获取配置
	baseURLs, err := c.getOpenAIBaseURLs()
	if err != nil {
		return nil, fmt.Errorf("获取基础URL配置失败: %v", err)
	}

	apiKeys, err := c.getOpenAIAPIKeys()
	if err != nil {
		return nil, fmt.Errorf("获取API密钥配置失败: %v", err)
	}

	if urlIdx >= len(baseURLs) {
		return nil, fmt.Errorf("无效的 URL 索引: %d", urlIdx)
	}

	baseURL := baseURLs[urlIdx]
	apiKey := ""
	if urlIdx < len(apiKeys) {
		apiKey = apiKeys[urlIdx]
	}

	if apiKey == "" {
		return nil, fmt.Errorf("缺少 API 密钥")
	}

	req, err := http.NewRequest("GET", fmt.Sprintf("%s/pipelines", baseURL), nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", apiKey))

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("服务器返回错误: %d, %s", resp.StatusCode, string(respBody))
	}

	var pipelineResp PipelineResponse
	if err := json.Unmarshal(respBody, &pipelineResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	return &pipelineResp, nil
}

// UploadPipeline 上传管道
func (c *pipelineClient) UploadPipeline(req *UploadPipelineRequest) (*PipelineResponse, error) {
	utils.Info("📤 上传管道文件...")

	// 获取配置
	baseURLs, err := c.getOpenAIBaseURLs()
	if err != nil {
		return nil, fmt.Errorf("获取基础URL配置失败: %v", err)
	}

	apiKeys, err := c.getOpenAIAPIKeys()
	if err != nil {
		return nil, fmt.Errorf("获取API密钥配置失败: %v", err)
	}

	if req.URLIdx >= len(baseURLs) {
		return nil, fmt.Errorf("无效的 URL 索引: %d", req.URLIdx)
	}

	baseURL := baseURLs[req.URLIdx]
	apiKey := ""
	if req.URLIdx < len(apiKeys) {
		apiKey = apiKeys[req.URLIdx]
	}

	if apiKey == "" {
		return nil, fmt.Errorf("缺少 API 密钥")
	}

	// 检查文件扩展名
	if !c.isPythonFile(req.File.Filename) {
		return nil, fmt.Errorf("只允许上传 Python (.py) 文件")
	}

	// 创建临时文件
	tempFile, err := c.createTempFile(req.File)
	if err != nil {
		return nil, fmt.Errorf("创建临时文件失败: %v", err)
	}
	defer os.Remove(tempFile.Name()) // 清理临时文件

	// 创建 multipart 请求
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	file, err := writer.CreateFormFile("file", req.File.Filename)
	if err != nil {
		return nil, fmt.Errorf("创建表单文件失败: %v", err)
	}

	// 读取并写入文件内容
	tempFileContent, err := os.ReadFile(tempFile.Name())
	if err != nil {
		return nil, fmt.Errorf("读取临时文件失败: %v", err)
	}

	if _, err := file.Write(tempFileContent); err != nil {
		return nil, fmt.Errorf("写入文件内容失败: %v", err)
	}

	writer.Close()

	// 发送请求
	httpReq, err := http.NewRequest("POST", fmt.Sprintf("%s/pipelines/upload", baseURL), &buf)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	httpReq.Header.Set("Content-Type", writer.FormDataContentType())
	httpReq.Header.Set("Authorization", fmt.Sprintf("Bearer %s", apiKey))

	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("服务器返回错误: %d, %s", resp.StatusCode, string(respBody))
	}

	var pipelineResp PipelineResponse
	if err := json.Unmarshal(respBody, &pipelineResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	utils.Info("✅ 管道文件上传完成")
	return &pipelineResp, nil
}

// AddPipeline 添加管道
func (c *pipelineClient) AddPipeline(req *AddPipelineRequest) (*PipelineResponse, error) {
	utils.Info("➕ 添加管道...")

	// 获取配置
	baseURLs, err := c.getOpenAIBaseURLs()
	if err != nil {
		return nil, fmt.Errorf("获取基础URL配置失败: %v", err)
	}

	apiKeys, err := c.getOpenAIAPIKeys()
	if err != nil {
		return nil, fmt.Errorf("获取API密钥配置失败: %v", err)
	}

	if req.URLIdx >= len(baseURLs) {
		return nil, fmt.Errorf("无效的 URL 索引: %d", req.URLIdx)
	}

	baseURL := baseURLs[req.URLIdx]
	apiKey := ""
	if req.URLIdx < len(apiKeys) {
		apiKey = apiKeys[req.URLIdx]
	}

	if apiKey == "" {
		return nil, fmt.Errorf("缺少 API 密钥")
	}

	jsonData, err := json.Marshal(map[string]string{"url": req.URL})
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %v", err)
	}

	httpReq, err := http.NewRequest("POST", fmt.Sprintf("%s/pipelines/add", baseURL), bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", fmt.Sprintf("Bearer %s", apiKey))

	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("服务器返回错误: %d, %s", resp.StatusCode, string(respBody))
	}

	var pipelineResp PipelineResponse
	if err := json.Unmarshal(respBody, &pipelineResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	utils.Info("✅ 管道添加完成")
	return &pipelineResp, nil
}

// DeletePipeline 删除管道
func (c *pipelineClient) DeletePipeline(req *DeletePipelineRequest) (*PipelineResponse, error) {
	utils.Info("🗑️ 删除管道...")

	// 获取配置
	baseURLs, err := c.getOpenAIBaseURLs()
	if err != nil {
		return nil, fmt.Errorf("获取基础URL配置失败: %v", err)
	}

	apiKeys, err := c.getOpenAIAPIKeys()
	if err != nil {
		return nil, fmt.Errorf("获取API密钥配置失败: %v", err)
	}

	if req.URLIdx >= len(baseURLs) {
		return nil, fmt.Errorf("无效的 URL 索引: %d", req.URLIdx)
	}

	baseURL := baseURLs[req.URLIdx]
	apiKey := ""
	if req.URLIdx < len(apiKeys) {
		apiKey = apiKeys[req.URLIdx]
	}

	if apiKey == "" {
		return nil, fmt.Errorf("缺少 API 密钥")
	}

	jsonData, err := json.Marshal(map[string]string{"id": req.ID})
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %v", err)
	}

	httpReq, err := http.NewRequest("DELETE", fmt.Sprintf("%s/pipelines/delete", baseURL), bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", fmt.Sprintf("Bearer %s", apiKey))

	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("服务器返回错误: %d, %s", resp.StatusCode, string(respBody))
	}

	var pipelineResp PipelineResponse
	if err := json.Unmarshal(respBody, &pipelineResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	utils.Info("✅ 管道删除完成")
	return &pipelineResp, nil
}

// GetPipelineValves 获取管道阀门
func (c *pipelineClient) GetPipelineValves(pipelineID string, urlIdx int) (*PipelineValvesResponse, error) {
	utils.Infof("🔧 获取管道阀门 (ID: %s)...", pipelineID)

	// 获取配置
	baseURLs, err := c.getOpenAIBaseURLs()
	if err != nil {
		return nil, fmt.Errorf("获取基础URL配置失败: %v", err)
	}

	apiKeys, err := c.getOpenAIAPIKeys()
	if err != nil {
		return nil, fmt.Errorf("获取API密钥配置失败: %v", err)
	}

	if urlIdx >= len(baseURLs) {
		return nil, fmt.Errorf("无效的 URL 索引: %d", urlIdx)
	}

	baseURL := baseURLs[urlIdx]
	apiKey := ""
	if urlIdx < len(apiKeys) {
		apiKey = apiKeys[urlIdx]
	}

	if apiKey == "" {
		return nil, fmt.Errorf("缺少 API 密钥")
	}

	req, err := http.NewRequest("GET", fmt.Sprintf("%s/%s/valves", baseURL, pipelineID), nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", apiKey))

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("服务器返回错误: %d, %s", resp.StatusCode, string(respBody))
	}

	var valvesResp PipelineValvesResponse
	if err := json.Unmarshal(respBody, &valvesResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	return &valvesResp, nil
}

// GetPipelineValvesSpec 获取管道阀门规格
func (c *pipelineClient) GetPipelineValvesSpec(pipelineID string, urlIdx int) (*PipelineValvesResponse, error) {
	utils.Infof("📋 获取管道阀门规格 (ID: %s)...", pipelineID)

	// 获取配置
	baseURLs, err := c.getOpenAIBaseURLs()
	if err != nil {
		return nil, fmt.Errorf("获取基础URL配置失败: %v", err)
	}

	apiKeys, err := c.getOpenAIAPIKeys()
	if err != nil {
		return nil, fmt.Errorf("获取API密钥配置失败: %v", err)
	}

	if urlIdx >= len(baseURLs) {
		return nil, fmt.Errorf("无效的 URL 索引: %d", urlIdx)
	}

	baseURL := baseURLs[urlIdx]
	apiKey := ""
	if urlIdx < len(apiKeys) {
		apiKey = apiKeys[urlIdx]
	}

	if apiKey == "" {
		return nil, fmt.Errorf("缺少 API 密钥")
	}

	req, err := http.NewRequest("GET", fmt.Sprintf("%s/%s/valves/spec", baseURL, pipelineID), nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", apiKey))

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("服务器返回错误: %d, %s", resp.StatusCode, string(respBody))
	}

	var valvesResp PipelineValvesResponse
	if err := json.Unmarshal(respBody, &valvesResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	return &valvesResp, nil
}

// UpdatePipelineValves 更新管道阀门
func (c *pipelineClient) UpdatePipelineValves(req *PipelineValvesRequest) (*PipelineResponse, error) {
	utils.Infof("🔧 更新管道阀门 (ID: %s)...", req.PipelineID)

	// 获取配置
	baseURLs, err := c.getOpenAIBaseURLs()
	if err != nil {
		return nil, fmt.Errorf("获取基础URL配置失败: %v", err)
	}

	apiKeys, err := c.getOpenAIAPIKeys()
	if err != nil {
		return nil, fmt.Errorf("获取API密钥配置失败: %v", err)
	}

	if req.URLIdx >= len(baseURLs) {
		return nil, fmt.Errorf("无效的 URL 索引: %d", req.URLIdx)
	}

	baseURL := baseURLs[req.URLIdx]
	apiKey := ""
	if req.URLIdx < len(apiKeys) {
		apiKey = apiKeys[req.URLIdx]
	}

	if apiKey == "" {
		return nil, fmt.Errorf("缺少 API 密钥")
	}

	jsonData, err := json.Marshal(req.Valves)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %v", err)
	}

	httpReq, err := http.NewRequest("POST", fmt.Sprintf("%s/%s/valves/update", baseURL, req.PipelineID), bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", fmt.Sprintf("Bearer %s", apiKey))

	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("服务器返回错误: %d, %s", resp.StatusCode, string(respBody))
	}

	var pipelineResp PipelineResponse
	if err := json.Unmarshal(respBody, &pipelineResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	utils.Info("✅ 管道阀门更新完成")
	return &pipelineResp, nil
}

// HealthCheck 健康检查
func (c *pipelineClient) HealthCheck() error {
	utils.Info("🔍 检查管道服务连接状态...")

	// 获取配置
	baseURLs, err := c.getOpenAIBaseURLs()
	if err != nil {
		utils.Errorf("❌ 获取基础URL配置失败: %v", err)
		return err
	}

	for idx, baseURL := range baseURLs {
		if !c.checkPipelineSupport(baseURL, idx) {
			utils.Warnf("⚠️ 管道服务 %s 连接失败", baseURL)
		} else {
			utils.Infof("✅ 管道服务 %s 连接正常", baseURL)
		}
	}

	return nil
}

// 辅助方法

// isPythonFile 检查是否为 Python 文件
func (c *pipelineClient) isPythonFile(filename string) bool {
	return filepath.Ext(filename) == ".py"
}

// createTempFile 创建临时文件
func (c *pipelineClient) createTempFile(fileHeader *multipart.FileHeader) (*os.File, error) {
	src, err := fileHeader.Open()
	if err != nil {
		return nil, err
	}
	defer src.Close()

	dst, err := os.CreateTemp("", "pipeline_*.py")
	if err != nil {
		return nil, err
	}

	_, err = io.Copy(dst, src)
	if err != nil {
		os.Remove(dst.Name())
		return nil, err
	}

	return dst, nil
}
