package pipelines

import (
	"mime/multipart"
	"time"
)

// PipelineUser 管道用户信息
type PipelineUser struct {
	ID    string `json:"id"`
	Email string `json:"email"`
	Name  string `json:"name"`
	Role  string `json:"role"`
}

// PipelineRequest 管道请求结构
type PipelineRequest struct {
	User *PipelineUser `json:"user"`
	Body interface{}   `json:"body"`
}

// PipelineResponse 管道响应结构
type PipelineResponse struct {
	Success bool        `json:"success"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
}

// FilterSpec 过滤器规格
type FilterSpec struct {
	ID        string                 `json:"id"`
	Name      string                 `json:"name"`
	Type      string                 `json:"type"` // "filter"
	Priority  int                    `json:"priority"`
	Pipelines []string               `json:"pipelines"` // ["*"] for all
	URLIdx    int                    `json:"urlIdx"`
	Pipeline  map[string]interface{} `json:"pipeline"`
}

// PipelineInfo 管道信息
type PipelineInfo struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Version     string                 `json:"version"`
	Author      string                 `json:"author"`
	Created     time.Time              `json:"created"`
	Updated     time.Time              `json:"updated"`
	URLIdx      int                    `json:"urlIdx"`
	Config      map[string]interface{} `json:"config"`
	Valves      map[string]interface{} `json:"valves"`
}

// PipelineListResponse 管道列表响应
type PipelineListResponse struct {
	Data []PipelineServerInfo `json:"data"`
}

// PipelineServerInfo 管道服务器信息
type PipelineServerInfo struct {
	URL string `json:"url"`
	Idx int    `json:"idx"`
}

// UploadPipelineRequest 上传管道请求
type UploadPipelineRequest struct {
	URLIdx int                   `json:"urlIdx"`
	File   *multipart.FileHeader `json:"file"`
}

// AddPipelineRequest 添加管道请求
type AddPipelineRequest struct {
	URL    string `json:"url"`
	URLIdx int    `json:"urlIdx"`
}

// DeletePipelineRequest 删除管道请求
type DeletePipelineRequest struct {
	ID     string `json:"id"`
	URLIdx int    `json:"urlIdx"`
}

// PipelineValvesRequest 管道阀门请求
type PipelineValvesRequest struct {
	PipelineID string                 `json:"pipeline_id"`
	URLIdx     int                    `json:"urlIdx"`
	Valves     map[string]interface{} `json:"valves"`
}

// PipelineValvesResponse 管道阀门响应
type PipelineValvesResponse struct {
	PipelineID string                 `json:"pipeline_id"`
	Valves     map[string]interface{} `json:"valves"`
	Spec       map[string]interface{} `json:"spec"`
}

// ProcessResult 处理结果
type ProcessResult struct {
	Success bool        `json:"success"`
	Data    interface{} `json:"data"`
	Error   string      `json:"error,omitempty"`
}
