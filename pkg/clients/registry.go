package clients

import (
	"hkchat_api/internal/core/interfaces"
	"hkchat_api/pkg/clients/llm"
	"hkchat_api/pkg/clients/pipelines"
	"hkchat_api/pkg/clients/web_search"
	"hkchat_api/pkg/utils"
)

// RegistrationFunc 注册函数类型
type RegistrationFunc func(interface{}, interface{}) error

// RegisterAllClients 注册所有客户端到DI容器
func RegisterAllClients(registerFunc RegistrationFunc, configRepo interfaces.ConfigRepository) error {
	utils.Info("🔌 开始注册外部服务客户端...")

	// 定义所有客户端的注册信息
	clients := []struct {
		name        string
		clientFunc  func() interface{}
		clientIface interface{}
	}{
		{
			name:        "OpenAIClient",
			clientFunc:  func() interface{} { return llm.NewOpenAIClient() },
			clientIface: (*llm.OpenAIClient)(nil),
		},
		{
			name:        "TavilyClient",
			clientFunc:  func() interface{} { return web_search.NewClient() },
			clientIface: (*web_search.Client)(nil),
		},
		{
			name:        "PipelineClient",
			clientFunc:  func() interface{} { return pipelines.NewPipelineClient(configRepo) },
			clientIface: (*pipelines.PipelineClient)(nil),
		},
	}

	// 注册所有客户端
	for _, client := range clients {
		// 创建客户端实例
		clientInstance := client.clientFunc()

		// 注册客户端
		if err := registerFunc(client.clientIface, clientInstance); err != nil {
			utils.Errorf("❌ 注册 %s 失败: %v", client.name, err)
			return err
		}
		utils.Infof("   ✅ %s 注册成功", client.name)
	}

	utils.Info("🔌 外部服务客户端注册完成")
	return nil
}

// GetClientList 获取所有客户端列表（用于验证）
func GetClientList() []string {
	return []string{
		"OpenAIClient",
		"TavilyClient",
		"PipelineClient",
	}
}
