package prompts

// ToolSpec 工具规格定义
type ToolSpec struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Parameters  map[string]interface{} `json:"parameters"`
}

// GetWebSearchDetectionTool 获取网络搜索检测工具规格
func GetWebSearchDetectionTool() map[string]interface{} {
	return map[string]interface{}{
		"detect_web_search_need": map[string]interface{}{
			"spec": map[string]interface{}{
				"name":        "detect_web_search_need",
				"description": WebSearchDetectionPrompt,
				"parameters": map[string]interface{}{
					"type": "object",
					"properties": map[string]interface{}{
						"need_search": map[string]interface{}{
							"type":        "string",
							"enum":        []string{"1", "0"},
							"description": WebSearchDetectionParamDescription,
						},
					},
					"required": []string{"need_search"},
				},
			},
		},
	}
}

// GetDefaultToolSpec 获取默认工具规格
func GetDefaultToolSpec(toolName, description string) ToolSpec {
	return ToolSpec{
		Name:        toolName,
		Description: description,
		Parameters: map[string]interface{}{
			"type": "object",
			"properties": map[string]interface{}{
				"input": map[string]interface{}{
					"type":        "string",
					"description": "输入参数",
				},
			},
			"required": []string{"input"},
		},
	}
}

// ToolResponsePrompts 工具响应提示词
var ToolResponsePrompts = map[string]string{
	"execution_start": "🔧 开始执行工具：%s",
	"execution_end":   "✅ 工具执行完成：%s",
	"execution_error": "❌ 工具执行失败：%s，错误：%v",
	"tool_not_found":  "⚠️ 工具未找到：%s",
	"result_empty":    "📝 工具 %s 执行完成，但没有返回结果",
}
