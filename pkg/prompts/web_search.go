package prompts

// WebSearchDetectionPrompt 网络搜索需求检测提示词
const WebSearchDetectionPrompt = `判断用户的问题是否需要进行网络搜索来获取最新信息、实时数据或当前事件。

需要搜索的情况包括但不限于：
1. 明确要求最新信息：最新新闻、今日新闻、最近发生的事等
2. 实时数据查询：天气、股价、汇率、交通状况等
3. 当前事件和趋势：正在发生的事件、热点话题、流行趋势等
4. 地理位置相关：导航路线、地点信息、营业时间等
5. 特定时间相关：某个日期发生了什么、历史上的今天等
6. 简短或模糊的查询：可能涉及当前热点或需要上下文的问题（如"64发生了什么"、"最近怎么样"等）
7. 人物近况：某个人最近的动态、新闻等
8. 产品或服务信息：最新价格、可用性、评价等
9. 技术更新：软件版本、新功能、系统要求等
10. 社会热点：公共事件、政策变化、社会讨论等

判断时要特别注意：
- 即使问题看似简单或模糊，如果可能涉及当前信息或需要上下文理解，也应该搜索
- 历史事件查询也可能需要搜索最新的相关讨论或纪念信息
- 当无法确定是否需要搜索时，倾向于选择搜索`

// WebSearchDetectionSystemPrompt 网络搜索检测系统提示词
const WebSearchDetectionSystemPrompt = "你是一个网络搜索需求检测助手，请根据用户的问题判断是否需要进行网络搜索。"

// WebSearchDetectionParamDescription 网络搜索检测参数描述
const WebSearchDetectionParamDescription = "是否需要网络搜索：1表示需要，0表示不需要"

// DefaultQueryGenerationPromptTemplate 默认查询生成提示模板
const DefaultQueryGenerationPromptTemplate = `### **Task:**
You are an AI assistant designed to generate search queries. Your primary goal is to analyze a conversation history and determine if searching for new information is necessary.

### **Guidelines:**
1.  **Analyze the Context:** Review the chat_history. If the last message introduces a new topic that is unrelated to the previous messages, focus solely on the last message. Otherwise, use the context of the recent conversation.
2.  **Generate Queries:** Based on your analysis, generate 1 to 3 distinct and broad search queries to find comprehensive and up-to-date information.
3.  **Default to Searching:** Always generate queries if there is any uncertainty or possibility that new information could be helpful. Return an empty list **only if you are absolutely certain** that no search is needed.
4.  **Use Current Information:** Today's date is {{CURRENT_DATE}}.

### **Time_Range Selection:**
Choose appropriate time range based on the time sensitivity of the query content:
- **day** - For: Today's news, breaking events, real-time data like stock prices/exchange rates, weather/traffic info
- **week** - For: Recent trends, hot topics, new product launches, company updates, sports results  
- **month** - For: Monthly reports, statistics, industry dynamics, policy changes, new technology research
- **year** - For: Annual summaries, long-term trends, major policy/regulatory changes, historical events
- **Leave empty** - For: Basic concepts/definitions, historical knowledge/cultural content, technical tutorials/guides

### **Topic Selection:**
Choose appropriate search category based on the content type:
- **news** - For: Real-time updates, politics, sports, major current events covered by mainstream media sources, breaking news, recent developments
- **finance** - For: Financial markets, stock prices, economic data, investment information, cryptocurrency, banking, financial analysis, earnings reports
- **general** - For: Broader, general-purpose searches that may include a wide range of sources, educational content, technical information, research topics

### **Output Format:**
- You **must** respond exclusively with a JSON object.
- If generating queries, use the format: { "queries": ["query1", "query2"], "time_range": "", "topic": "general" }
- If no search is needed, return: { "queries": [], "time_range": "", "topic": "general" }

### **Chat History:**
<chat_history>
{{MESSAGES:END:3}}
</chat_history>`
