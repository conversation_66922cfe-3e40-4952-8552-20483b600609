# Prompts 包使用说明

这个包统一管理系统中所有的 AI prompt，便于维护、配置和复用。

## 📁 文件结构

```
internal/prompts/
├── web_search.go      # 网络搜索相关 prompt
├── code_interpreter.go # 代码解释器相关 prompt
├── rag.go            # RAG 模板相关 prompt
├── common.go         # 通用 prompt
├── tools.go          # 工具相关规格和 prompt
├── config.go         # 配置管理器
└── README.md         # 使用说明
```

## 🚀 基本使用

### 1. 使用静态 prompt

```go
import "hkchat_api/pkg/prompts"

// 使用网络搜索检测工具
tool := prompts.GetWebSearchDetectionTool()

// 使用代码解释器 prompt
systemMessage := request.ChatMessage{
    Role:    "system",
    Content: prompts.CodeInterpreterPrompt,
}

// 使用 RAG 模板
ragPrompt := prompts.GetRAGPrompt(context, question)
```

### 2. 使用配置管理器

```go
import "hkchat_api/pkg/prompts"

// 获取配置实例
config := prompts.GetPromptConfig()

// 获取动态配置的 prompt
webSearchPrompt := config.GetWebSearchDetectionPrompt()
codePrompt := config.GetCodeInterpreterPrompt()

// 动态修改 prompt
config.SetCodeInterpreterPrompt("自定义的代码解释器提示词")
```

## ⚙️ 环境变量配置

可以通过环境变量自定义 prompt：

```bash
# 网络搜索检测 prompt
export WEB_SEARCH_DETECTION_PROMPT="自定义的网络搜索检测提示词"

# 网络搜索检测系统 prompt
export WEB_SEARCH_DETECTION_SYSTEM_PROMPT="自定义的系统提示词"

# 代码解释器 prompt
export CODE_INTERPRETER_PROMPT="自定义的代码解释器提示词"

# RAG 模板 prompt
export RAG_TEMPLATE_PROMPT="自定义的 RAG 模板"
```

## 📝 Prompt 类型

### 1. 网络搜索相关
- `WebSearchDetectionPrompt`: 搜索需求检测的详细规则
- `WebSearchDetectionSystemPrompt`: 系统级提示词
- `WebSearchDetectionParamDescription`: 参数描述

### 2. 代码解释器相关
- `CodeInterpreterPrompt`: 基础代码解释器提示词
- `CodeInterpreterTemplatePrompt`: 增强版模板提示词

### 3. RAG 相关
- `RAGTemplatePrompt`: 标准 RAG 模板
- `DefaultRAGTemplate`: 默认 RAG 模板
- `GetRAGPrompt()`: 格式化 RAG 提示词

### 4. 通用提示词
- `SystemPrompts`: 系统提示词集合
- `ToolPrompts`: 工具相关提示词
- `ErrorPrompts`: 错误提示词

## 🔧 高级功能

### 1. 热更新配置

```go
config := prompts.GetPromptConfig()

// 从环境变量重新加载配置
config.ReloadFromEnv()
```

### 2. 工具规格定义

```go
// 获取默认工具规格
spec := prompts.GetDefaultToolSpec("tool_name", "工具描述")

// 获取网络搜索检测工具
tool := prompts.GetWebSearchDetectionTool()
```

### 3. 响应提示词

```go
// 使用预定义的响应提示词
fmt.Printf(prompts.ToolResponsePrompts["execution_start"], toolName)
fmt.Printf(prompts.ToolResponsePrompts["execution_error"], toolName, err)
```

## 🏗️ 扩展指南

### 添加新的 prompt

1. 在相应的文件中添加常量：
```go
const NewFeaturePrompt = "新功能的提示词"
```

2. 在 `config.go` 中添加配置支持：
```go
type PromptConfig struct {
    // ... existing fields
    newFeaturePrompt string
}
```

3. 添加对应的 getter/setter 方法

### 添加新的 prompt 类型

1. 创建新的 `.go` 文件
2. 定义相关常量和函数
3. 在 `config.go` 中添加配置支持

## 💡 最佳实践

1. **分类管理**: 按功能模块分别创建文件
2. **环境变量**: 敏感或经常变更的 prompt 使用环境变量
3. **版本控制**: 重要的 prompt 变更要记录在版本历史中
4. **测试验证**: 修改 prompt 后要充分测试效果
5. **文档更新**: 添加新 prompt 时要更新相关文档

## 🔍 调试技巧

```go
// 打印当前使用的 prompt
config := prompts.GetPromptConfig()
fmt.Printf("当前代码解释器 prompt: %s\n", config.GetCodeInterpreterPrompt())

// 临时修改 prompt 进行测试
config.SetCodeInterpreterPrompt("测试用的提示词")
defer config.ReloadFromEnv() // 测试完成后恢复
``` 