package prompts

import (
	"os"
	"sync"
)

// PromptConfig prompt 配置管理器
type PromptConfig struct {
	mu                             sync.RWMutex
	webSearchDetectionPrompt       string
	webSearchDetectionSystemPrompt string
	codeInterpreterPrompt          string
	ragTemplatePrompt              string
}

var (
	defaultConfig *PromptConfig
	configOnce    sync.Once
)

// GetPromptConfig 获取配置实例（单例模式）
func GetPromptConfig() *PromptConfig {
	configOnce.Do(func() {
		defaultConfig = &PromptConfig{
			webSearchDetectionPrompt:       getEnvOrDefault("WEB_SEARCH_DETECTION_PROMPT", WebSearchDetectionPrompt),
			webSearchDetectionSystemPrompt: getEnvOrDefault("WEB_SEARCH_DETECTION_SYSTEM_PROMPT", WebSearchDetectionSystemPrompt),
			codeInterpreterPrompt:          getEnvOrDefault("CODE_INTERPRETER_PROMPT", CodeInterpreterPrompt),
			ragTemplatePrompt:              getEnvOrDefault("RAG_TEMPLATE_PROMPT", RAGTemplatePrompt),
		}
	})
	return defaultConfig
}

// GetWebSearchDetectionPrompt 获取网络搜索检测 prompt
func (c *PromptConfig) GetWebSearchDetectionPrompt() string {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.webSearchDetectionPrompt
}

// GetWebSearchDetectionSystemPrompt 获取网络搜索检测系统 prompt
func (c *PromptConfig) GetWebSearchDetectionSystemPrompt() string {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.webSearchDetectionSystemPrompt
}

// GetCodeInterpreterPrompt 获取代码解释器 prompt
func (c *PromptConfig) GetCodeInterpreterPrompt() string {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.codeInterpreterPrompt
}

// GetRAGTemplatePrompt 获取 RAG 模板 prompt
func (c *PromptConfig) GetRAGTemplatePrompt() string {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.ragTemplatePrompt
}

// SetWebSearchDetectionPrompt 设置网络搜索检测 prompt
func (c *PromptConfig) SetWebSearchDetectionPrompt(prompt string) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.webSearchDetectionPrompt = prompt
}

// SetWebSearchDetectionSystemPrompt 设置网络搜索检测系统 prompt
func (c *PromptConfig) SetWebSearchDetectionSystemPrompt(prompt string) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.webSearchDetectionSystemPrompt = prompt
}

// SetCodeInterpreterPrompt 设置代码解释器 prompt
func (c *PromptConfig) SetCodeInterpreterPrompt(prompt string) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.codeInterpreterPrompt = prompt
}

// SetRAGTemplatePrompt 设置 RAG 模板 prompt
func (c *PromptConfig) SetRAGTemplatePrompt(prompt string) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.ragTemplatePrompt = prompt
}

// ReloadFromEnv 从环境变量重新加载配置
func (c *PromptConfig) ReloadFromEnv() {
	c.mu.Lock()
	defer c.mu.Unlock()

	c.webSearchDetectionPrompt = getEnvOrDefault("WEB_SEARCH_DETECTION_PROMPT", WebSearchDetectionPrompt)
	c.webSearchDetectionSystemPrompt = getEnvOrDefault("WEB_SEARCH_DETECTION_SYSTEM_PROMPT", WebSearchDetectionSystemPrompt)
	c.codeInterpreterPrompt = getEnvOrDefault("CODE_INTERPRETER_PROMPT", CodeInterpreterPrompt)
	c.ragTemplatePrompt = getEnvOrDefault("RAG_TEMPLATE_PROMPT", RAGTemplatePrompt)
}

// getEnvOrDefault 获取环境变量值，如果不存在则返回默认值
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
