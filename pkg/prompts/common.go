package prompts

// SystemPrompts 系统提示词集合
type SystemPrompts struct {
	// 通用助手提示词
	GeneralAssistant string
	// 专业助手提示词
	ProfessionalAssistant string
	// 友好助手提示词
	FriendlyAssistant string
}

// GetSystemPrompts 获取系统提示词
func GetSystemPrompts() SystemPrompts {
	return SystemPrompts{
		GeneralAssistant: `你是一个有用的AI助手，能够回答各种问题并提供帮助。请保持友好、准确和有建设性的回答。`,

		ProfessionalAssistant: `你是一个专业的AI助手，具备广泛的知识和技能。请提供准确、详细的信息，并在适当的时候给出专业建议。`,

		FriendlyAssistant: `你是一个友好的AI助手，善于与用户交流。请用温暖、耐心的语气回答问题，并尽力帮助用户解决问题。`,
	}
}

// ToolPrompts 工具相关提示词
type ToolPrompts struct {
	// 工具执行成功
	ToolExecutionSuccess string
	// 工具执行失败
	ToolExecutionFailed string
	// 工具不可用
	ToolUnavailable string
}

// GetToolPrompts 获取工具提示词
func GetToolPrompts() ToolPrompts {
	return ToolPrompts{
		ToolExecutionSuccess: "工具执行成功，结果如下：",
		ToolExecutionFailed:  "工具执行失败，错误信息：",
		ToolUnavailable:      "工具当前不可用，请稍后再试。",
	}
}

// ErrorPrompts 错误提示词
type ErrorPrompts struct {
	// 一般错误
	GeneralError string
	// 网络错误
	NetworkError string
	// 权限错误
	PermissionError string
}

// GetErrorPrompts 获取错误提示词
func GetErrorPrompts() ErrorPrompts {
	return ErrorPrompts{
		GeneralError:    "抱歉，处理您的请求时出现了错误，请稍后再试。",
		NetworkError:    "网络连接出现问题，请检查网络设置后重试。",
		PermissionError: "您没有执行此操作的权限，请联系管理员。",
	}
}
