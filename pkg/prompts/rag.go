package prompts

import "fmt"

// RAGTemplatePrompt RAG 模板提示词
const RAGTemplatePrompt = `使用以下上下文信息来回答用户的问题。如果上下文中没有相关信息，请说明无法找到相关信息。

上下文信息：
%s

用户问题：%s`

// DefaultRAGTemplate 默认 RAG 模板
const DefaultRAGTemplate = `根据以下提供的上下文信息回答用户问题：

<context>
%s
</context>

问题: %s

请基于上下文信息进行回答。如果上下文中没有相关信息，请明确说明。`

// WebSearchRAGTemplate 网络搜索后的 RAG 模板
const WebSearchRAGTemplate = `### Task:
Respond to the user query using the provided context, incorporating inline citations in the format [id] **only when the <source> tag includes an explicit id attribute** (e.g., <source id="1">).

### Guidelines:
- If you don't know the answer, clearly state that.
- If uncertain, ask the user for clarification.
- Respond in the same language as the user's query.
- If the context is unreadable or of poor quality, inform the user and provide the best possible answer.
- If the answer isn't present in the context but you possess the knowledge, explain this to the user and provide the answer using your own understanding.
- **Only include inline citations using [id] (e.g., [1], [2]) when the <source> tag includes an id attribute.**
- Do not cite if the <source> tag does not contain an id attribute.
- Do not use XML tags in your response.
- Ensure citations are concise and directly related to the information provided.

### Example of Citation:
If the user asks about a specific topic and the information is found in a source with a provided id attribute, the response should include the citation like in the following example:
* "According to the study, the proposed method increases efficiency by 20% [1]."

### Output:
Provide a clear and direct response to the user's query, including inline citations in the format [id] only when the <source> tag with id attribute is present in the context.

<context>
%s
</context>

<user_query>
%s
</user_query>`

// GetRAGPrompt 获取 RAG 提示词
func GetRAGPrompt(context, question string) string {
	return fmt.Sprintf(RAGTemplatePrompt, context, question)
}

// GetDefaultRAGPrompt 获取默认 RAG 提示词
func GetDefaultRAGPrompt(context, question string) string {
	return fmt.Sprintf(DefaultRAGTemplate, context, question)
}

// GetWebSearchRAGPrompt 获取网络搜索后的 RAG 提示词
func GetWebSearchRAGPrompt(context, question string) string {
	return fmt.Sprintf(WebSearchRAGTemplate, context, question)
}
