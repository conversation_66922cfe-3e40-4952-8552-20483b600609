package prompts

import "strings"

// DefaultToolsFunctionCallingPromptTemplate 默认工具函数调用提示词模板
const DefaultToolsFunctionCallingPromptTemplate = `Available Tools: {{TOOLS}}

Your task is to choose and return the correct tool(s) from the list of available tools based on the query. Follow these guidelines:

- Return only the JSON object, without any additional text or explanation.

- If no tools match the query, return an empty array: 
   {
     "tool_calls": []
   }

- If one or more tools match the query, construct a JSON response containing a "tool_calls" array with objects that include:
   - "name": The tool's name.
   - "parameters": A dictionary of required parameters and their corresponding values.

The format for the JSON response is strictly:
{
  "tool_calls": [
    {"name": "toolName1", "parameters": {"key1": "value1"}},
    {"name": "toolName2", "parameters": {"key2": "value2"}}
  ]
}`

// FunctionCallingPrompts 函数调用相关提示词
type FunctionCallingPrompts struct {
	// 默认工具函数调用模板
	DefaultTemplate string
	// 工具选择引导
	ToolSelectionGuide string
	// 参数验证提示
	ParameterValidationPrompt string
}

// GetFunctionCallingPrompts 获取函数调用提示词
func GetFunctionCallingPrompts() FunctionCallingPrompts {
	return FunctionCallingPrompts{
		DefaultTemplate: DefaultToolsFunctionCallingPromptTemplate,
		ToolSelectionGuide: `根据用户的问题分析是否需要使用工具：
1. 如果问题需要实时信息、外部数据或特定功能，选择合适的工具
2. 如果问题可以直接回答，返回空的工具调用数组
3. 确保工具参数格式正确`,
		ParameterValidationPrompt: `验证工具参数时请注意：
1. 参数类型是否正确
2. 必需参数是否完整
3. 参数值是否在允许范围内`,
	}
}

// GetToolsFunctionCallingTemplate 获取工具函数调用模板
func GetToolsFunctionCallingTemplate() string {
	return DefaultToolsFunctionCallingPromptTemplate
}

// BuildFunctionCallingPrompt 构建函数调用提示词，处理占位符替换
func BuildFunctionCallingPrompt(toolsSpecs string) string {
	template := GetToolsFunctionCallingTemplate()
	return strings.Replace(template, "{{TOOLS}}", toolsSpecs, 1)
}

// BuildFunctionCallingPromptWithTemplate 使用自定义模板构建函数调用提示词
func BuildFunctionCallingPromptWithTemplate(template, toolsSpecs string) string {
	return strings.Replace(template, "{{TOOLS}}", toolsSpecs, 1)
}
