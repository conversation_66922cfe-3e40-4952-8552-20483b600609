package utils

import "fmt"

// NewError 创建格式化的错误
func NewError(format string, args ...interface{}) error {
	return fmt.Errorf(format, args...)
}

// AppError 应用程序错误结构
type AppError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

// Error 实现error接口
func (e *AppError) Error() string {
	return fmt.Sprintf("[%s] %s", e.Code, e.Message)
}

// NewAppError 创建应用程序错误
func NewAppError(code, message string) *AppError {
	return &AppError{
		Code:    code,
		Message: message,
	}
}

// NewAppErrorWithDetails 创建带详情的应用程序错误
func NewAppErrorWithDetails(code, message, details string) *AppError {
	return &AppError{
		Code:    code,
		Message: message,
		Details: details,
	}
}
