package utils

import (
	"fmt"
	"io"
	"os"
	"path/filepath"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// Logger 全局日志实例
var Logger *MultiLevelLogger

// LogConfig 日志配置结构
type LogConfig struct {
	FilePath string
	Level    string
}

// MultiLevelLogger 多级别日志器
type MultiLevelLogger struct {
	loggers     map[string]*logrus.Logger
	baseDir     string
	globalLevel logrus.Level // 全局日志级别
	mu          sync.RWMutex
}

// LogLevel 日志级别常量
const (
	LevelInfo  = "info"
	LevelDebug = "debug"
	LevelWarn  = "warn"
	LevelError = "error"
	LevelFatal = "fatal"
)

// InitLogger 初始化日志系统
func InitLogger(config *LogConfig) error {
	// 解析全局日志级别
	globalLevel := logrus.InfoLevel
	if config != nil && config.Level != "" {
		if level, err := logrus.ParseLevel(config.Level); err == nil {
			globalLevel = level
		}
	}

	Logger = &MultiLevelLogger{
		loggers:     make(map[string]*logrus.Logger),
		baseDir:     "logs",
		globalLevel: globalLevel,
		mu:          sync.RWMutex{},
	}

	// 如果有配置，使用配置的基础目录
	if config != nil && config.FilePath != "" {
		Logger.baseDir = filepath.Dir(config.FilePath)
	}

	// 创建基础日志目录
	if err := os.MkdirAll(Logger.baseDir, 0755); err != nil {
		return err
	}

	// 初始化各级别日志器
	levels := []string{LevelInfo, LevelDebug, LevelWarn, LevelError, LevelFatal}
	for _, level := range levels {
		if err := Logger.initLevelLogger(level); err != nil {
			return err
		}
	}

	// 使用Info级别的日志器记录初始化信息
	Logger.GetLogger(LevelInfo).Infof("📁 日志目录: %s", Logger.baseDir)

	return nil
}

// initLevelLogger 初始化指定级别的日志器
func (ml *MultiLevelLogger) initLevelLogger(level string) error {
	logger := logrus.New()

	// 设置自定义格式化器（显示时间戳）
	logger.SetFormatter(&CustomFormatter{ShowTimestamp: true})

	// 所有logger都设置为Debug级别，由调用时决定是否输出
	logger.SetLevel(logrus.DebugLevel)

	// 创建文件输出
	writer, err := ml.createWriter(level)
	if err != nil {
		return err
	}

	// 设置输出到文件和控制台
	multiWriter := io.MultiWriter(os.Stdout, writer)
	logger.SetOutput(multiWriter)

	ml.loggers[level] = logger
	return nil
}

// createWriter 创建文件写入器
func (ml *MultiLevelLogger) createWriter(level string) (io.Writer, error) {
	now := time.Now()
	dateDir := now.Format("2006-01-02")

	// 创建日期目录
	fullDir := filepath.Join(ml.baseDir, dateDir)
	if err := os.MkdirAll(fullDir, 0755); err != nil {
		return nil, err
	}

	// 创建文件名：level.log
	filename := fmt.Sprintf("%s.log", level)
	filePath := filepath.Join(fullDir, filename)

	// 打开文件（追加模式）
	file, err := os.OpenFile(filePath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return nil, err
	}

	return &HourlyRotatingWriter{
		file:        file,
		level:       level,
		baseDir:     ml.baseDir,
		lastHour:    now.Format("15"),
		currentPath: filePath,
		mu:          sync.Mutex{},
	}, nil
}

// HourlyRotatingWriter 按小时轮转的写入器
type HourlyRotatingWriter struct {
	file        *os.File
	level       string
	baseDir     string
	lastHour    string
	currentPath string
	mu          sync.Mutex
}

// Write 实现io.Writer接口
func (w *HourlyRotatingWriter) Write(p []byte) (n int, err error) {
	w.mu.Lock()
	defer w.mu.Unlock()

	now := time.Now()
	currentHour := now.Format("15")
	currentDate := now.Format("2006-01-02")

	// 检查是否需要轮转
	if currentHour != w.lastHour {
		// 关闭当前文件
		if w.file != nil {
			w.file.Close()
		}

		// 移动旧文件到hourly目录
		if err := w.archiveOldFile(currentDate); err != nil {
			// 如果归档失败，记录错误但继续
			fmt.Printf("警告：归档日志文件失败: %v\n", err)
		}

		// 创建新文件
		if err := w.createNewFile(currentDate); err != nil {
			return 0, err
		}

		w.lastHour = currentHour
	}

	return w.file.Write(p)
}

// archiveOldFile 归档旧文件
func (w *HourlyRotatingWriter) archiveOldFile(currentDate string) error {
	// 创建hourly目录
	hourlyDir := filepath.Join(w.baseDir, currentDate, "hourly", w.lastHour)
	if err := os.MkdirAll(hourlyDir, 0755); err != nil {
		return err
	}

	// 目标文件路径
	oldFilename := fmt.Sprintf("%s.log", w.level)
	destPath := filepath.Join(hourlyDir, oldFilename)

	// 检查当前文件是否存在且不为空
	if stat, err := os.Stat(w.currentPath); err == nil && stat.Size() > 0 {
		// 移动文件
		if err := os.Rename(w.currentPath, destPath); err != nil {
			// 如果移动失败，尝试复制后删除
			if err := w.copyAndRemove(w.currentPath, destPath); err != nil {
				return err
			}
		}
	}

	return nil
}

// copyAndRemove 复制文件后删除原文件
func (w *HourlyRotatingWriter) copyAndRemove(src, dst string) error {
	// 读取源文件
	data, err := os.ReadFile(src)
	if err != nil {
		return err
	}

	// 写入目标文件
	if err := os.WriteFile(dst, data, 0644); err != nil {
		return err
	}

	// 删除源文件
	return os.Remove(src)
}

// createNewFile 创建新的日志文件
func (w *HourlyRotatingWriter) createNewFile(currentDate string) error {
	// 创建日期目录
	dateDir := filepath.Join(w.baseDir, currentDate)
	if err := os.MkdirAll(dateDir, 0755); err != nil {
		return err
	}

	// 新文件路径
	filename := fmt.Sprintf("%s.log", w.level)
	w.currentPath = filepath.Join(dateDir, filename)

	// 打开新文件
	file, err := os.OpenFile(w.currentPath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return err
	}

	w.file = file
	return nil
}

// CustomFormatter 自定义格式化器
type CustomFormatter struct {
	ShowTimestamp bool // 是否显示时间戳
}

// Format 实现logrus.Formatter接口
func (f *CustomFormatter) Format(entry *logrus.Entry) ([]byte, error) {
	level := getEmojiForLevel(entry.Level)
	message := entry.Message

	if f.ShowTimestamp {
		timestamp := entry.Time.Format("2006/01/02 15:04:05")
		return []byte(fmt.Sprintf("%s %s %s\n", timestamp, level, message)), nil
	} else {
		return []byte(fmt.Sprintf("%s %s\n", level, message)), nil
	}
}

// getEmojiForLevel 获取日志级别对应的emoji
func getEmojiForLevel(level logrus.Level) string {
	switch level {
	case logrus.DebugLevel:
		return "🐛"
	case logrus.InfoLevel:
		return "📝"
	case logrus.WarnLevel:
		return "⚠️"
	case logrus.ErrorLevel:
		return "❌"
	case logrus.FatalLevel:
		return "💀"
	default:
		return "📝"
	}
}

// GetLogger 获取指定级别的日志器
func (ml *MultiLevelLogger) GetLogger(level string) *logrus.Logger {
	ml.mu.RLock()
	defer ml.mu.RUnlock()

	if logger, exists := ml.loggers[level]; exists {
		return logger
	}
	return ml.loggers[LevelInfo] // 默认返回info级别
}

// Info 记录Info级别日志
func Info(args ...interface{}) {
	if Logger != nil {
		Logger.GetLogger(LevelInfo).Info(args...)
	}
}

// Infof 记录Info级别格式化日志
func Infof(format string, args ...interface{}) {
	if Logger != nil {
		Logger.GetLogger(LevelInfo).Infof(format, args...)
	}
}

// Debug 记录Debug级别日志
func Debug(args ...interface{}) {
	if Logger != nil && Logger.globalLevel <= logrus.DebugLevel {
		Logger.GetLogger(LevelDebug).Debug(args...)
	}
}

// Debugf 记录Debug级别格式化日志
func Debugf(format string, args ...interface{}) {
	if Logger != nil && Logger.globalLevel <= logrus.DebugLevel {
		Logger.GetLogger(LevelDebug).Debugf(format, args...)
	}
}

// Warn 记录Warn级别日志
func Warn(args ...interface{}) {
	if Logger != nil && Logger.globalLevel <= logrus.WarnLevel {
		Logger.GetLogger(LevelWarn).Warn(args...)
	}
}

// Warnf 记录Warn级别格式化日志
func Warnf(format string, args ...interface{}) {
	if Logger != nil && Logger.globalLevel <= logrus.WarnLevel {
		Logger.GetLogger(LevelWarn).Warnf(format, args...)
	}
}

// Error 记录Error级别日志
func Error(args ...interface{}) {
	if Logger != nil && Logger.globalLevel <= logrus.ErrorLevel {
		Logger.GetLogger(LevelError).Error(args...)
	}
}

// Errorf 记录Error级别格式化日志
func Errorf(format string, args ...interface{}) {
	if Logger != nil && Logger.globalLevel <= logrus.ErrorLevel {
		Logger.GetLogger(LevelError).Errorf(format, args...)
	}
}

// Fatal 记录Fatal级别日志并退出程序
func Fatal(args ...interface{}) {
	if Logger != nil {
		Logger.GetLogger(LevelFatal).Fatal(args...)
	}
}

// Fatalf 记录Fatal级别格式化日志并退出程序
func Fatalf(format string, args ...interface{}) {
	if Logger != nil {
		Logger.GetLogger(LevelFatal).Fatalf(format, args...)
	}
}

// GetLogger 获取日志实例（兼容旧版本）
func GetLogger() *logrus.Logger {
	if Logger != nil {
		return Logger.GetLogger(LevelInfo)
	}

	// 如果没有初始化，创建一个默认的
	defaultLogger := logrus.New()
	defaultLogger.SetOutput(os.Stdout)
	defaultLogger.SetLevel(logrus.InfoLevel)
	defaultLogger.SetFormatter(&CustomFormatter{ShowTimestamp: true})
	return defaultLogger
}
