package utils

import (
	"crypto/rand"
	"fmt"
	"math/big"
	"time"
)

// GenerateID 生成唯一 ID
func GenerateID() string {
	// 使用时间戳 + 随机数生成唯一ID
	timestamp := time.Now().UnixNano()

	// 生成6位随机数
	randomNum, _ := rand.Int(rand.Reader, big.NewInt(999999))

	return fmt.Sprintf("%d%06d", timestamp, randomNum.Int64())
}

// GenerateShortID 生成短ID（8位）
func GenerateShortID() string {
	const chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	result := make([]byte, 8)

	for i := range result {
		num, _ := rand.Int(rand.Reader, big.NewInt(int64(len(chars))))
		result[i] = chars[num.Int64()]
	}

	return string(result)
}

// GenerateChatID 生成聊天ID（以chat_开头）
func GenerateChatID() string {
	return "chat_" + GenerateShortID()
}

// GenerateMessageID 生成消息ID（以msg_开头）
func GenerateMessageID() string {
	return "msg_" + GenerateShortID()
}

// GenerateSessionID 生成会话ID（以sess_开头）
func GenerateSessionID() string {
	return "sess_" + GenerateShortID()
}

// GetCurrentTimestamp 获取当前时间戳
func GetCurrentTimestamp() int64 {
	return time.Now().Unix()
}
