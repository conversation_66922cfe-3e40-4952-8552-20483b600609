package utils

import (
	"fmt"
	"strings"
	"sync"
)

// ModuleLogger 模块化日志器
type ModuleLogger struct {
	moduleName string
	tag        string
	color      string
}

// 模块日志器缓存
var moduleLoggers = make(map[string]*ModuleLogger)
var moduleLoggerMutex sync.RWMutex

// 预定义的模块标记和颜色
var moduleConfig = map[string]struct {
	tag   string
	color string
}{
	"api":        {"🌐", "\033[36m"},  // 青色 - API层
	"service":    {"⚙️", "\033[35m"}, // 紫色 - 服务层
	"database":   {"🗄️", "\033[33m"}, // 黄色 - 数据库层
	"auth":       {"🔐", "\033[32m"},  // 绿色 - 认证模块
	"chat":       {"💬", "\033[94m"},  // 亮蓝色 - 聊天模块
	"llm":        {"🧠", "\033[95m"},  // 亮紫色 - LLM模块
	"middleware": {"🔧", "\033[37m"},  // 白色 - 中间件
	"config":     {"📋", "\033[93m"},  // 亮黄色 - 配置模块
	"gateway":    {"🚪", "\033[31m"},  // 红色 - 网关模块
	"function":   {"🔮", "\033[96m"},  // 亮青色 - 函数调用
	"tools":      {"🛠️", "\033[97m"}, // 亮白色 - 工具模块

}

// 颜色重置
const colorReset = "\033[0m"

// GetModuleLogger 获取模块日志器
func GetModuleLogger(moduleName string) *ModuleLogger {
	moduleLoggerMutex.RLock()
	if logger, exists := moduleLoggers[moduleName]; exists {
		moduleLoggerMutex.RUnlock()
		return logger
	}
	moduleLoggerMutex.RUnlock()

	// 创建新的模块日志器
	moduleLoggerMutex.Lock()
	defer moduleLoggerMutex.Unlock()

	// 双重检查
	if logger, exists := moduleLoggers[moduleName]; exists {
		return logger
	}

	// 获取模块配置
	config, exists := moduleConfig[strings.ToLower(moduleName)]
	if !exists {
		// 使用默认配置
		config = struct {
			tag   string
			color string
		}{"📦", "\033[90m"} // 默认灰色
	}

	logger := &ModuleLogger{
		moduleName: moduleName,
		tag:        config.tag,
		color:      config.color,
	}

	moduleLoggers[moduleName] = logger
	return logger
}

// formatMessage 格式化日志消息
func (ml *ModuleLogger) formatMessage(level, message string) string {
	// 构建模块标记
	moduleTag := fmt.Sprintf("[%s %s]", ml.tag, strings.ToUpper(ml.moduleName))

	// 如果在控制台输出，添加颜色
	if ml.color != "" {
		moduleTag = fmt.Sprintf("%s%s%s", ml.color, moduleTag, colorReset)
	}

	return fmt.Sprintf("%s %s", moduleTag, message)
}

// Info 模块Info级别日志
func (ml *ModuleLogger) Info(args ...interface{}) {
	if Logger != nil {
		message := fmt.Sprint(args...)
		formattedMessage := ml.formatMessage("INFO", message)
		Logger.GetLogger(LevelInfo).Info(formattedMessage)
	}
}

// Infof 模块Info级别格式化日志
func (ml *ModuleLogger) Infof(format string, args ...interface{}) {
	if Logger != nil {
		message := fmt.Sprintf(format, args...)
		formattedMessage := ml.formatMessage("INFO", message)
		Logger.GetLogger(LevelInfo).Info(formattedMessage)
	}
}

// Debug 模块Debug级别日志
func (ml *ModuleLogger) Debug(args ...interface{}) {
	if Logger != nil && Logger.globalLevel <= 5 { // DebugLevel
		message := fmt.Sprint(args...)
		formattedMessage := ml.formatMessage("DEBUG", message)
		Logger.GetLogger(LevelDebug).Debug(formattedMessage)
	}
}

// Debugf 模块Debug级别格式化日志
func (ml *ModuleLogger) Debugf(format string, args ...interface{}) {
	if Logger != nil && Logger.globalLevel <= 5 { // DebugLevel
		message := fmt.Sprintf(format, args...)
		formattedMessage := ml.formatMessage("DEBUG", message)
		Logger.GetLogger(LevelDebug).Debug(formattedMessage)
	}
}

// Warn 模块Warn级别日志
func (ml *ModuleLogger) Warn(args ...interface{}) {
	if Logger != nil && Logger.globalLevel <= 3 { // WarnLevel
		message := fmt.Sprint(args...)
		formattedMessage := ml.formatMessage("WARN", message)
		Logger.GetLogger(LevelWarn).Warn(formattedMessage)
	}
}

// Warnf 模块Warn级别格式化日志
func (ml *ModuleLogger) Warnf(format string, args ...interface{}) {
	if Logger != nil && Logger.globalLevel <= 3 { // WarnLevel
		message := fmt.Sprintf(format, args...)
		formattedMessage := ml.formatMessage("WARN", message)
		Logger.GetLogger(LevelWarn).Warn(formattedMessage)
	}
}

// Error 模块Error级别日志
func (ml *ModuleLogger) Error(args ...interface{}) {
	if Logger != nil && Logger.globalLevel <= 2 { // ErrorLevel
		message := fmt.Sprint(args...)
		formattedMessage := ml.formatMessage("ERROR", message)
		Logger.GetLogger(LevelError).Error(formattedMessage)
	}
}

// Errorf 模块Error级别格式化日志
func (ml *ModuleLogger) Errorf(format string, args ...interface{}) {
	if Logger != nil && Logger.globalLevel <= 2 { // ErrorLevel
		message := fmt.Sprintf(format, args...)
		formattedMessage := ml.formatMessage("ERROR", message)
		Logger.GetLogger(LevelError).Error(formattedMessage)
	}
}

// Fatal 模块Fatal级别日志
func (ml *ModuleLogger) Fatal(args ...interface{}) {
	if Logger != nil {
		message := fmt.Sprint(args...)
		formattedMessage := ml.formatMessage("FATAL", message)
		Logger.GetLogger(LevelFatal).Fatal(formattedMessage)
	}
}

// Fatalf 模块Fatal级别格式化日志
func (ml *ModuleLogger) Fatalf(format string, args ...interface{}) {
	if Logger != nil {
		message := fmt.Sprintf(format, args...)
		formattedMessage := ml.formatMessage("FATAL", message)
		Logger.GetLogger(LevelFatal).Fatal(formattedMessage)
	}
}

// 便捷函数：为常用模块创建快捷日志器
var (
	ApiLogger        = GetModuleLogger("api")
	ServiceLogger    = GetModuleLogger("service")
	DatabaseLogger   = GetModuleLogger("database")
	AuthLogger       = GetModuleLogger("auth")
	ChatLogger       = GetModuleLogger("chat")
	LLMLogger        = GetModuleLogger("llm")
	MiddlewareLogger = GetModuleLogger("middleware")
	ConfigLogger     = GetModuleLogger("config")
	GatewayLogger    = GetModuleLogger("gateway")
	FunctionLogger   = GetModuleLogger("function")
	ToolsLogger      = GetModuleLogger("tools")
)

// 预定义模块日志函数
func ApiInfo(format string, args ...interface{}) {
	ApiLogger.Infof(format, args...)
}

func ApiDebug(format string, args ...interface{}) {
	ApiLogger.Debugf(format, args...)
}

func ApiWarn(format string, args ...interface{}) {
	ApiLogger.Warnf(format, args...)
}

func ApiError(format string, args ...interface{}) {
	ApiLogger.Errorf(format, args...)
}

func ServiceInfo(format string, args ...interface{}) {
	ServiceLogger.Infof(format, args...)
}

func ServiceDebug(format string, args ...interface{}) {
	ServiceLogger.Debugf(format, args...)
}

func ServiceWarn(format string, args ...interface{}) {
	ServiceLogger.Warnf(format, args...)
}

func ServiceError(format string, args ...interface{}) {
	ServiceLogger.Errorf(format, args...)
}

func ChatInfo(format string, args ...interface{}) {
	ChatLogger.Infof(format, args...)
}

func ChatDebug(format string, args ...interface{}) {
	ChatLogger.Debugf(format, args...)
}

func ChatWarn(format string, args ...interface{}) {
	ChatLogger.Warnf(format, args...)
}

func ChatError(format string, args ...interface{}) {
	ChatLogger.Errorf(format, args...)
}

func AuthInfo(format string, args ...interface{}) {
	AuthLogger.Infof(format, args...)
}

func AuthDebug(format string, args ...interface{}) {
	AuthLogger.Debugf(format, args...)
}

func AuthWarn(format string, args ...interface{}) {
	AuthLogger.Warnf(format, args...)
}

func AuthError(format string, args ...interface{}) {
	AuthLogger.Errorf(format, args...)
}

// RegisterCustomModule 注册自定义模块
func RegisterCustomModule(moduleName, tag, color string) {
	moduleLoggerMutex.Lock()
	defer moduleLoggerMutex.Unlock()

	moduleConfig[strings.ToLower(moduleName)] = struct {
		tag   string
		color string
	}{tag, color}
}

// ListRegisteredModules 列出已注册的模块
func ListRegisteredModules() []string {
	moduleLoggerMutex.RLock()
	defer moduleLoggerMutex.RUnlock()

	var modules []string
	for moduleName := range moduleConfig {
		modules = append(modules, moduleName)
	}
	return modules
}
