package auth

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"hkchat_api/pkg/utils"
)

// JWTClaims JWT载荷结构
type JWTClaims struct {
	ID       string `json:"id"`                 // 用户ID
	Username string `json:"username,omitempty"` // 用户名
	Email    string `json:"email,omitempty"`    // 邮箱
	Role     string `json:"role,omitempty"`     // 角色
	Exp      int64  `json:"exp,omitempty"`      // 过期时间
	Iat      int64  `json:"iat,omitempty"`      // 签发时间
	Iss      string `json:"iss,omitempty"`      // 签发者
}

// JWTHeader JWT头部结构
type JWTHeader struct {
	Alg string `json:"alg"` // 算法
	Typ string `json:"typ"` // 类型
}

// JWTService JWT服务
type JWTService struct {
	secretKey string
}

// NewJWTService 创建JWT服务
func NewJWTService(secretKey string) *JWTService {
	return &JWTService{
		secretKey: secretKey,
	}
}

// ParseToken 解析JWT token
func (j *JWTService) ParseToken(tokenString string) (*JWTClaims, error) {
	// 移除 "Bearer " 前缀
	if strings.HasPrefix(tokenString, "Bearer ") {
		tokenString = strings.TrimPrefix(tokenString, "Bearer ")
	}

	utils.AuthDebug("开始解析JWT token: %s", tokenString[:min(20, len(tokenString))]+"...")

	// 分割token为三部分
	parts := strings.Split(tokenString, ".")
	if len(parts) != 3 {
		utils.AuthError("无效的JWT token格式")
		return nil, fmt.Errorf("无效的JWT token格式")
	}

	// 解析头部
	header, err := j.parseHeader(parts[0])
	if err != nil {
		utils.AuthError("解析JWT头部失败: %v", err)
		return nil, fmt.Errorf("解析JWT头部失败: %v", err)
	}

	utils.AuthDebug("JWT头部: 算法=%s, 类型=%s", header.Alg, header.Typ)

	// 检查算法
	if header.Alg != "HS256" {
		utils.AuthWarn("不支持的JWT算法: %s", header.Alg)
		return nil, fmt.Errorf("不支持的JWT算法: %s", header.Alg)
	}

	// 验证签名
	if err := j.verifySignature(parts[0], parts[1], parts[2]); err != nil {
		utils.AuthError("JWT签名验证失败: %v", err)
		return nil, fmt.Errorf("JWT签名验证失败: %v", err)
	}

	// 解析载荷
	claims, err := j.parseClaims(parts[1])
	if err != nil {
		utils.AuthError("解析JWT载荷失败: %v", err)
		return nil, fmt.Errorf("解析JWT载荷失败: %v", err)
	}

	utils.AuthDebug("JWT载荷解析成功: 用户ID=%s", claims.ID)

	// 检查过期时间
	if claims.Exp > 0 && time.Now().Unix() > claims.Exp {
		utils.AuthWarn("JWT token已过期: 过期时间=%d, 当前时间=%d", claims.Exp, time.Now().Unix())
		return nil, fmt.Errorf("JWT token已过期")
	}

	utils.AuthInfo("JWT token验证成功: 用户ID=%s", claims.ID)
	return claims, nil
}

// parseHeader 解析JWT头部
func (j *JWTService) parseHeader(headerB64 string) (*JWTHeader, error) {
	// Base64解码
	headerJSON, err := j.base64URLDecode(headerB64)
	if err != nil {
		return nil, fmt.Errorf("Base64解码头部失败: %v", err)
	}

	// JSON解析
	var header JWTHeader
	if err := json.Unmarshal(headerJSON, &header); err != nil {
		return nil, fmt.Errorf("JSON解析头部失败: %v", err)
	}

	return &header, nil
}

// parseClaims 解析JWT载荷
func (j *JWTService) parseClaims(claimsB64 string) (*JWTClaims, error) {
	// Base64解码
	claimsJSON, err := j.base64URLDecode(claimsB64)
	if err != nil {
		return nil, fmt.Errorf("Base64解码载荷失败: %v", err)
	}

	utils.AuthDebug("JWT载荷JSON: %s", string(claimsJSON))

	// JSON解析
	var claims JWTClaims
	if err := json.Unmarshal(claimsJSON, &claims); err != nil {
		return nil, fmt.Errorf("JSON解析载荷失败: %v", err)
	}

	return &claims, nil
}

// verifySignature 验证JWT签名
func (j *JWTService) verifySignature(headerB64, claimsB64, signatureB64 string) error {
	// 构建待签名数据
	data := headerB64 + "." + claimsB64

	// 使用HMAC-SHA256计算签名
	mac := hmac.New(sha256.New, []byte(j.secretKey))
	mac.Write([]byte(data))
	expectedSignature := mac.Sum(nil)

	// Base64URL编码期望的签名
	expectedSignatureB64 := j.base64URLEncode(expectedSignature)

	// 比较签名
	if expectedSignatureB64 != signatureB64 {
		return fmt.Errorf("签名不匹配")
	}

	return nil
}

// base64URLDecode Base64URL解码
func (j *JWTService) base64URLDecode(data string) ([]byte, error) {
	// 添加必要的填充
	data = j.addBase64Padding(data)

	// 替换URL安全字符
	data = strings.ReplaceAll(data, "-", "+")
	data = strings.ReplaceAll(data, "_", "/")

	return base64.StdEncoding.DecodeString(data)
}

// base64URLEncode Base64URL编码
func (j *JWTService) base64URLEncode(data []byte) string {
	encoded := base64.StdEncoding.EncodeToString(data)
	encoded = strings.ReplaceAll(encoded, "+", "-")
	encoded = strings.ReplaceAll(encoded, "/", "_")
	encoded = strings.TrimRight(encoded, "=")
	return encoded
}

// addBase64Padding 添加Base64填充
func (j *JWTService) addBase64Padding(data string) string {
	padding := len(data) % 4
	if padding > 0 {
		data += strings.Repeat("=", 4-padding)
	}
	return data
}

// GenerateToken 生成JWT token
func (j *JWTService) GenerateToken(claims *JWTClaims) (string, error) {
	// 设置默认值
	now := time.Now().Unix()
	if claims.Iat == 0 {
		claims.Iat = now
	}
	if claims.Exp == 0 {
		claims.Exp = now + 24*3600 // 24小时后过期
	}
	if claims.Iss == "" {
		claims.Iss = "hkchat-api"
	}

	// 构建头部
	header := JWTHeader{
		Alg: "HS256",
		Typ: "JWT",
	}

	// 序列化头部
	headerJSON, err := json.Marshal(header)
	if err != nil {
		return "", fmt.Errorf("序列化头部失败: %v", err)
	}

	// 序列化载荷
	claimsJSON, err := json.Marshal(claims)
	if err != nil {
		return "", fmt.Errorf("序列化载荷失败: %v", err)
	}

	// Base64URL编码
	headerB64 := j.base64URLEncode(headerJSON)
	claimsB64 := j.base64URLEncode(claimsJSON)

	// 构建待签名数据
	data := headerB64 + "." + claimsB64

	// 生成签名
	mac := hmac.New(sha256.New, []byte(j.secretKey))
	mac.Write([]byte(data))
	signature := mac.Sum(nil)
	signatureB64 := j.base64URLEncode(signature)

	// 构建完整token
	token := data + "." + signatureB64

	utils.AuthInfo("生成JWT token成功: 用户ID=%s", claims.ID)
	return token, nil
}

// ExtractUserID 从token中提取用户ID（快速方法，不验证签名）
func (j *JWTService) ExtractUserID(tokenString string) (string, error) {
	// 移除 "Bearer " 前缀
	if strings.HasPrefix(tokenString, "Bearer ") {
		tokenString = strings.TrimPrefix(tokenString, "Bearer ")
	}

	// 分割token
	parts := strings.Split(tokenString, ".")
	if len(parts) != 3 {
		return "", fmt.Errorf("无效的JWT token格式")
	}

	// 仅解析载荷（不验证签名，用于快速提取用户ID）
	claims, err := j.parseClaims(parts[1])
	if err != nil {
		return "", err
	}

	return claims.ID, nil
}

// IsTokenExpired 检查token是否过期
func (j *JWTService) IsTokenExpired(tokenString string) bool {
	claims, err := j.ParseToken(tokenString)
	if err != nil {
		return true
	}

	if claims.Exp > 0 && time.Now().Unix() > claims.Exp {
		return true
	}

	return false
}

// 辅助函数
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
