package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"hkchat_api/api"
	"hkchat_api/internal/config"
	"hkchat_api/internal/core/di"
	"hkchat_api/internal/models"
	"hkchat_api/pkg/clients/database"
	"hkchat_api/pkg/utils"

	"github.com/gin-gonic/gin"
)

func main() {
	// 先使用标准log输出启动信息，因为此时自定义日志系统还未初始化
	println("🚀 启动 HKChat 服务器...")

	// 初始化配置
	if err := config.InitConfig(); err != nil {
		println("❌ 配置初始化失败:", err.Error())
		os.Exit(1)
	}

	// 初始化日志系统
	logConfig := &utils.LogConfig{
		FilePath: config.GlobalConfig.Log.FilePath,
		Level:    config.GlobalConfig.Log.Level,
	}
	if err := utils.InitLogger(logConfig); err != nil {
		println("❌ 日志系统初始化失败:", err.Error())
		// 继续执行，使用默认日志
	}

	// 从这里开始使用自定义日志系统
	utils.Info("📊 初始化数据库...")

	// 获取数据库配置
	dbConfig := config.GetDatabaseConfig()
	if dbConfig == nil {
		utils.Fatal("❌ 数据库配置获取失败")
	}

	// 初始化数据库管理器
	if err := database.InitManager(dbConfig); err != nil {
		utils.Fatal("❌ 数据库初始化失败:", err)
	}

	// 获取所有模型进行自动迁移
	allModels := models.GetAllModels()
	if err := database.AutoMigrateGlobal(allModels...); err != nil {
		utils.Fatal("❌ 数据库表结构同步失败:", err)
	}

	utils.Debugf("📈 共迁移 %d 个实体模型", len(allModels))
	modelNames := models.GetModelNames()
	utils.Debug("🗃️  已创建的数据表:")
	for _, name := range modelNames {
		utils.Debugf("   - %s", name)
	}

	// 设置GIN模式（减少debug输出）
	if config.GlobalConfig.Log.Level != "debug" {
		gin.SetMode(gin.ReleaseMode)
	}

	// 创建应用容器
	utils.Info("🧠 初始化应用容器...")
	container, err := di.CreateMinimalDI()
	if err != nil {
		utils.Fatal("❌ 容器初始化失败:", err)
	}

	// 详细检查容器状态
	utils.Info("🔍 检查容器初始化状态...")

	// 调用完整的依赖注入状态报告
	di.PrintDependencyInjectionStatus()

	// 检查数据库连接状态
	if db := database.GetGlobalDB(); db != nil {
		utils.Info("   ✅ 数据库连接正常")
	} else {
		utils.Fatal("   ❌ 数据库连接为空")
	}

	// 检查配置管理器状态
	if configComponent := di.GetCoreComponent("ConfigManager"); configComponent != nil {
		utils.Info("   ✅ 配置管理器组件存在")
	} else {
		utils.Fatal("   ❌ 配置管理器组件为空")
	}

	// 检查LLM网关状态
	if llmGateway := di.GetGlobalLLMGateway(); llmGateway != nil {
		utils.Info("   ✅ LLM网关存在")
	} else {
		utils.Fatal("   ❌ LLM网关为空")
	}

	// 打印服务实例
	status := container.GetContainerStatus()
	utils.Debugf("📊 容器状态: %+v", status)

	// 设置路由
	router := container.GetDI().GetRouter()
	api.SetupRoutesWithContainer(router, container)

	// 获取服务器配置
	serverAddr := fmt.Sprintf(":%d", config.GetAppPort())

	// 启动HTTP服务器
	server := &http.Server{
		Addr:    serverAddr,
		Handler: router,
	}

	utils.Info("🎉 HKChat 服务器启动成功!")
	utils.Infof("📡 服务器地址: http://localhost%s", serverAddr)
	utils.Infof("📚 API文档: http://localhost%s/swagger/index.html", serverAddr)

	// 优雅关闭设置
	go func() {
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			utils.Fatal("❌ 服务器启动失败:", err)
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	utils.Info("🛑 正在关闭服务器...")

	// 优雅关闭服务器
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer shutdownCancel()

	if err := server.Shutdown(shutdownCtx); err != nil {
		utils.Errorf("❌ 服务器关闭失败: %v", err)
	}

	// 关闭应用容器
	if err := container.Shutdown(shutdownCtx); err != nil {
		utils.Errorf("❌ 容器关闭失败: %v", err)
	}

	// 关闭数据库连接
	if err := database.CloseGlobal(); err != nil {
		utils.Errorf("❌ 数据库关闭失败: %v", err)
	}

	utils.Info("✅ 服务器已安全关闭")
}
