# HKChat API - 智能聊天后端服务

HKChat API 是一个基于 Go 语言开发的现代化智能聊天后端服务，支持多种 LLM 模型集成、实时聊天、用户管理、文档检索等功能。

## 📋 快速导航

- [🏗️ 系统架构文档](./docs/architecture/README.md)
- [🔧 依赖注入架构](./docs/architecture/dependency-injection.md)  
- [🤖 LLM 集成架构](./docs/architecture/llm-integration.md)
- [📊 系统架构流程图](./docs/architecture/system-architecture-flow.drawio.xml)
- [📖 API 文档](./docs/swagger.yaml)

## 🚀 项目概述

HKChat API 采用分层架构设计，通过依赖注入容器管理组件依赖关系，支持多种大语言模型的统一接入和智能功能扩展。

### 核心特性

- ✅ **多 LLM 支持**: OpenAI GPT、本地模型等多种 LLM 提供商
- ✅ **智能函数调用**: 支持 Web 搜索、文档检索等功能
- ✅ **依赖注入**: 自定义 DI 容器，松耦合架构设计
- ✅ **实时聊天**: WebSocket 支持的实时消息流
- ✅ **配置管理**: 灵活的多环境配置系统
- ✅ **数据持久化**: PostgreSQL + GORM ORM
- ✅ **API 文档**: Swagger 自动生成的 API 文档
- ✅ **容器化部署**: Docker + Kubernetes 支持

## 🏗️ 架构概览

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   客户端层      │───▶│   API 网关层     │───▶│   中间件层      │
│ Web/Mobile App  │    │  Gin Router      │    │ Auth/Log/CORS   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                        业务服务层                                 │
│  ChatService │ ConfigService │ PromptService │ LLM Gateway      │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                      数据访问层 (Repository)                      │
│   ChatRepo │ UserRepo │ ModelRepo │ ConfigRepo │ MemoryRepo     │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                        数据存储层                                 │
│           PostgreSQL (主数据库) │ Redis (缓存)                   │
└─────────────────────────────────────────────────────────────────┘
```

## 📁 项目结构

```
hkchat_api/
├── api/                    # API 路由层
│   ├── chat_api_routes.go  # 聊天 API 路由
│   ├── middleware/         # 中间件
│   └── registry.go         # 路由注册器
├── cmd/                    # 应用入口点
│   └── server/             # 服务器启动逻辑
├── config/                 # 配置文件
│   ├── dev.yaml           # 开发环境配置
│   └── prod.yaml          # 生产环境配置
├── deploy/                 # 部署配置
│   ├── docker/            # Docker 配置
│   └── k8s/               # Kubernetes 配置
├── docs/                   # 文档和架构设计
│   ├── architecture/      # 架构文档
│   └── swagger.yaml       # API 规范
├── internal/              # 内部模块
│   ├── config/            # 配置管理
│   ├── core/              # 核心组件
│   │   ├── di/            # 依赖注入
│   │   ├── factories/     # 工厂模式
│   │   ├── interfaces/    # 业务接口
│   │   └── llm/           # LLM 集成
│   ├── models/            # 数据模型
│   │   ├── entities/      # 数据库实体
│   │   ├── dto/           # 数据传输对象
│   │   └── domain/        # 领域模型
│   ├── repository/        # 数据访问层
│   └── services/          # 业务服务层
├── pkg/                   # 公共包
│   ├── auth/              # 认证相关
│   ├── clients/           # 外部客户端
│   ├── prompts/           # 提示词模板
│   └── utils/             # 工具函数
├── scripts/               # 脚本工具
├── go.mod                 # 依赖管理
├── go.sum                 # 依赖锁定
└── main.go               # 主程序入口
```

## 🛠️ 技术栈

- **Web 框架**: [Gin](https://github.com/gin-gonic/gin) - 高性能 HTTP Web 框架
- **ORM**: [GORM](https://gorm.io/) - Go 语言 ORM 库
- **数据库**: [PostgreSQL](https://www.postgresql.org/) - 关系型数据库
- **缓存**: [Redis](https://redis.io/) - 内存数据库
- **文档**: [Swagger](https://swagger.io/) - API 文档生成
- **容器化**: [Docker](https://www.docker.com/) - 容器化部署
- **编排**: [Kubernetes](https://kubernetes.io/) - 容器编排

## 🚀 快速开始

### 环境要求

- Go 1.21+
- PostgreSQL 13+
- Redis 6+ (可选)
- Docker (可选)

### 本地开发

1. **克隆项目**
```bash
git clone <repository-url>
cd hkchat_api
```

2. **安装依赖**
```bash
go mod download
```

3. **配置环境变量**
```bash
cp env.example .env
# 编辑 .env 文件，配置数据库连接等信息
```

4. **启动数据库**
```bash
# 使用 Docker Compose 启动
docker-compose -f deploy/docker/docker-compose.yml up -d postgres redis
```

5. **运行项目**
```bash
# 开发模式
go run main.go run

# 或使用脚本
./scripts/dev.sh
```

6. **访问服务**
- API 服务: http://localhost:8080
- Swagger 文档: http://localhost:8080/swagger/index.html
- 健康检查: http://localhost:8080/health

### Docker 部署

1. **构建镜像**
```bash
docker build -t hkchat-api .
```

2. **运行容器**
```bash
docker run -p 8080:8080 --env-file .env hkchat-api
```

3. **使用 Docker Compose**
```bash
docker-compose -f deploy/docker/docker-compose.yml up
```

### Kubernetes 部署

```bash
kubectl apply -f deploy/k8s/
```

## ⚙️ 配置管理

### 环境变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `DB_HOST` | 数据库主机 | localhost |
| `DB_PORT` | 数据库端口 | 5432 |
| `DB_USER` | 数据库用户名 | postgres |
| `DB_PASSWORD` | 数据库密码 | - |
| `DB_NAME` | 数据库名称 | hkchat |
| `REDIS_URL` | Redis 连接地址 | redis://localhost:6379 |
| `OPENAI_API_KEY` | OpenAI API 密钥 | - |
| `PORT` | 服务端口 | 8080 |

### 配置文件

项目支持多环境配置，配置文件位于 `config/` 目录：

- `dev.yaml`: 开发环境配置
- `prod.yaml`: 生产环境配置

## 📖 开发指南

### 代码结构

项目采用 Clean Architecture 设计原则：

1. **API 层** (`api/`): HTTP 路由和中间件
2. **服务层** (`internal/services/`): 业务逻辑处理
3. **仓储层** (`internal/repository/`): 数据访问抽象
4. **实体层** (`internal/models/`): 数据模型定义

### 添加新功能

1. 在 `internal/models/entities/` 定义数据实体
2. 在 `internal/repository/` 实现数据访问逻辑
3. 在 `internal/services/` 实现业务逻辑
4. 在 `api/` 添加 HTTP 路由
5. 在 DI 容器中注册新组件

### 测试

```bash
# 运行测试
go test ./...

# 生成测试覆盖率
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out
```

## 🔗 相关链接

- [系统架构文档](./docs/architecture/README.md)
- [API 文档](./docs/swagger.yaml)
- [部署指南](./deploy/README.md)
- [开发者指南](./docs/developer-guide.md)

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进项目！
